<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>ls-by-icefire</artifactId>
	<groupId>com.longtech.icefire</groupId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.target>21</maven.compiler.target>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.release>21</maven.compiler.release>
	</properties>
	<modules>
		<module>sgf-parent</module>
		<module>sgf-core</module>
		<module>sgf-net</module>
		<module>sgf-common</module>
		<module>sgf-monitoring</module>
		<module>icefire-parent</module>
		<module>icefire-core</module>
		<module>icefire-protocol</module>
		<module>ls-flyway-mongo</module>
		<module>icefire-game</module>
		<module>icefire-web</module>
		<!--<module>icefire-client</module> -->

    </modules>

</project>