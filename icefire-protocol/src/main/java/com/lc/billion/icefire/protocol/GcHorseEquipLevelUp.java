/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 坐骑装备升级结果返回
 * @Message(7470)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcHorseEquipLevelUp implements org.apache.thrift.TBase<GcHorseEquipLevelUp, GcHorseEquipLevelUp._Fields>, java.io.Serializable, Cloneable, Comparable<GcHorseEquipLevelUp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcHorseEquipLevelUp");

  private static final org.apache.thrift.protocol.TField ERROR_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("errorCode", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField HORSE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("horseId", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField PART_FIELD_DESC = new org.apache.thrift.protocol.TField("part", org.apache.thrift.protocol.TType.I32, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcHorseEquipLevelUpStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcHorseEquipLevelUpTupleSchemeFactory();

  /**
   * 0 成功 其他失败
   */
  public int errorCode; // optional
  /**
   * 升级的坐骑
   */
  public int horseId; // optional
  /**
   * 升级的部位
   */
  public int part; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 0 成功 其他失败
     */
    ERROR_CODE((short)1, "errorCode"),
    /**
     * 升级的坐骑
     */
    HORSE_ID((short)2, "horseId"),
    /**
     * 升级的部位
     */
    PART((short)3, "part");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ERROR_CODE
          return ERROR_CODE;
        case 2: // HORSE_ID
          return HORSE_ID;
        case 3: // PART
          return PART;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ERRORCODE_ISSET_ID = 0;
  private static final int __HORSEID_ISSET_ID = 1;
  private static final int __PART_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ERROR_CODE,_Fields.HORSE_ID,_Fields.PART};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ERROR_CODE, new org.apache.thrift.meta_data.FieldMetaData("errorCode", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.HORSE_ID, new org.apache.thrift.meta_data.FieldMetaData("horseId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PART, new org.apache.thrift.meta_data.FieldMetaData("part", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcHorseEquipLevelUp.class, metaDataMap);
  }

  public GcHorseEquipLevelUp() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcHorseEquipLevelUp(GcHorseEquipLevelUp other) {
    __isset_bitfield = other.__isset_bitfield;
    this.errorCode = other.errorCode;
    this.horseId = other.horseId;
    this.part = other.part;
  }

  public GcHorseEquipLevelUp deepCopy() {
    return new GcHorseEquipLevelUp(this);
  }

  @Override
  public void clear() {
    setErrorCodeIsSet(false);
    this.errorCode = 0;
    setHorseIdIsSet(false);
    this.horseId = 0;
    setPartIsSet(false);
    this.part = 0;
  }

  /**
   * 0 成功 其他失败
   */
  public int getErrorCode() {
    return this.errorCode;
  }

  /**
   * 0 成功 其他失败
   */
  public GcHorseEquipLevelUp setErrorCode(int errorCode) {
    this.errorCode = errorCode;
    setErrorCodeIsSet(true);
    return this;
  }

  public void unsetErrorCode() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ERRORCODE_ISSET_ID);
  }

  /** Returns true if field errorCode is set (has been assigned a value) and false otherwise */
  public boolean isSetErrorCode() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ERRORCODE_ISSET_ID);
  }

  public void setErrorCodeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ERRORCODE_ISSET_ID, value);
  }

  /**
   * 升级的坐骑
   */
  public int getHorseId() {
    return this.horseId;
  }

  /**
   * 升级的坐骑
   */
  public GcHorseEquipLevelUp setHorseId(int horseId) {
    this.horseId = horseId;
    setHorseIdIsSet(true);
    return this;
  }

  public void unsetHorseId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __HORSEID_ISSET_ID);
  }

  /** Returns true if field horseId is set (has been assigned a value) and false otherwise */
  public boolean isSetHorseId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __HORSEID_ISSET_ID);
  }

  public void setHorseIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __HORSEID_ISSET_ID, value);
  }

  /**
   * 升级的部位
   */
  public int getPart() {
    return this.part;
  }

  /**
   * 升级的部位
   */
  public GcHorseEquipLevelUp setPart(int part) {
    this.part = part;
    setPartIsSet(true);
    return this;
  }

  public void unsetPart() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PART_ISSET_ID);
  }

  /** Returns true if field part is set (has been assigned a value) and false otherwise */
  public boolean isSetPart() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PART_ISSET_ID);
  }

  public void setPartIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PART_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ERROR_CODE:
      if (value == null) {
        unsetErrorCode();
      } else {
        setErrorCode((java.lang.Integer)value);
      }
      break;

    case HORSE_ID:
      if (value == null) {
        unsetHorseId();
      } else {
        setHorseId((java.lang.Integer)value);
      }
      break;

    case PART:
      if (value == null) {
        unsetPart();
      } else {
        setPart((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ERROR_CODE:
      return getErrorCode();

    case HORSE_ID:
      return getHorseId();

    case PART:
      return getPart();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ERROR_CODE:
      return isSetErrorCode();
    case HORSE_ID:
      return isSetHorseId();
    case PART:
      return isSetPart();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcHorseEquipLevelUp)
      return this.equals((GcHorseEquipLevelUp)that);
    return false;
  }

  public boolean equals(GcHorseEquipLevelUp that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_errorCode = true && this.isSetErrorCode();
    boolean that_present_errorCode = true && that.isSetErrorCode();
    if (this_present_errorCode || that_present_errorCode) {
      if (!(this_present_errorCode && that_present_errorCode))
        return false;
      if (this.errorCode != that.errorCode)
        return false;
    }

    boolean this_present_horseId = true && this.isSetHorseId();
    boolean that_present_horseId = true && that.isSetHorseId();
    if (this_present_horseId || that_present_horseId) {
      if (!(this_present_horseId && that_present_horseId))
        return false;
      if (this.horseId != that.horseId)
        return false;
    }

    boolean this_present_part = true && this.isSetPart();
    boolean that_present_part = true && that.isSetPart();
    if (this_present_part || that_present_part) {
      if (!(this_present_part && that_present_part))
        return false;
      if (this.part != that.part)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetErrorCode()) ? 131071 : 524287);
    if (isSetErrorCode())
      hashCode = hashCode * 8191 + errorCode;

    hashCode = hashCode * 8191 + ((isSetHorseId()) ? 131071 : 524287);
    if (isSetHorseId())
      hashCode = hashCode * 8191 + horseId;

    hashCode = hashCode * 8191 + ((isSetPart()) ? 131071 : 524287);
    if (isSetPart())
      hashCode = hashCode * 8191 + part;

    return hashCode;
  }

  @Override
  public int compareTo(GcHorseEquipLevelUp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetErrorCode(), other.isSetErrorCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetErrorCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.errorCode, other.errorCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHorseId(), other.isSetHorseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHorseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.horseId, other.horseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPart(), other.isSetPart());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPart()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.part, other.part);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcHorseEquipLevelUp(");
    boolean first = true;

    if (isSetErrorCode()) {
      sb.append("errorCode:");
      sb.append(this.errorCode);
      first = false;
    }
    if (isSetHorseId()) {
      if (!first) sb.append(", ");
      sb.append("horseId:");
      sb.append(this.horseId);
      first = false;
    }
    if (isSetPart()) {
      if (!first) sb.append(", ");
      sb.append("part:");
      sb.append(this.part);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcHorseEquipLevelUpStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcHorseEquipLevelUpStandardScheme getScheme() {
      return new GcHorseEquipLevelUpStandardScheme();
    }
  }

  private static class GcHorseEquipLevelUpStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcHorseEquipLevelUp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcHorseEquipLevelUp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ERROR_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.errorCode = iprot.readI32();
              struct.setErrorCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // HORSE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.horseId = iprot.readI32();
              struct.setHorseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PART
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.part = iprot.readI32();
              struct.setPartIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcHorseEquipLevelUp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetErrorCode()) {
        oprot.writeFieldBegin(ERROR_CODE_FIELD_DESC);
        oprot.writeI32(struct.errorCode);
        oprot.writeFieldEnd();
      }
      if (struct.isSetHorseId()) {
        oprot.writeFieldBegin(HORSE_ID_FIELD_DESC);
        oprot.writeI32(struct.horseId);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPart()) {
        oprot.writeFieldBegin(PART_FIELD_DESC);
        oprot.writeI32(struct.part);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcHorseEquipLevelUpTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcHorseEquipLevelUpTupleScheme getScheme() {
      return new GcHorseEquipLevelUpTupleScheme();
    }
  }

  private static class GcHorseEquipLevelUpTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcHorseEquipLevelUp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcHorseEquipLevelUp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetErrorCode()) {
        optionals.set(0);
      }
      if (struct.isSetHorseId()) {
        optionals.set(1);
      }
      if (struct.isSetPart()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetErrorCode()) {
        oprot.writeI32(struct.errorCode);
      }
      if (struct.isSetHorseId()) {
        oprot.writeI32(struct.horseId);
      }
      if (struct.isSetPart()) {
        oprot.writeI32(struct.part);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcHorseEquipLevelUp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.errorCode = iprot.readI32();
        struct.setErrorCodeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.horseId = iprot.readI32();
        struct.setHorseIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.part = iprot.readI32();
        struct.setPartIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

