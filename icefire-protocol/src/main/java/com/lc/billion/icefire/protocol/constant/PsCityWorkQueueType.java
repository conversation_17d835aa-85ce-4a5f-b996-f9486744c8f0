/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsCityWorkQueueType implements org.apache.thrift.TEnum {
  /**
   * 城建1
   */
  Build1(1),
  /**
   * 城建2
   */
  Build2(2),
  /**
   * 城建3
   */
  Build3(3),
  /**
   * 城建4
   */
  Build4(4),
  /**
   * 城建1
   */
  Build5(5),
  /**
   * 城建6
   */
  Build6(6),
  /**
   * 城建7
   */
  Build7(7),
  /**
   * 城建8
   */
  Build8(8),
  /**
   * 城建9
   */
  Build9(9),
  /**
   * 科研
   */
  Tech(11),
  /**
   * 治疗
   */
  Cure(12),
  /**
   * 步兵训练
   */
  Train_Infantry(13),
  /**
   * 弓兵训练
   */
  Train_Archer(14),
  /**
   * 骑兵训练
   */
  Train_Cavalry(15),
  /**
   * 晋升兵种 目前这个是没有用的 晋升兵种占用的是13 14 15
   */
  Upgrade_Soldier(16),
  /**
   * 粮工
   */
  FoodWorker(21),
  /**
   * 水工
   */
  WaterWorker(22),
  /**
   * 木工
   */
  WoodWorker(25),
  /**
   * 铁工
   */
  IronWorker(26),
  /**
   * 金工
   */
  GoldWorker(27),
  /**
   * 探索1
   */
  Explore1(31),
  /**
   * 探索2
   */
  Explore2(32),
  /**
   * 探索3
   */
  Explore3(33),
  /**
   * 探索4
   */
  Explore4(34),
  /**
   * 探索5
   */
  Explore5(35),
  /**
   * 探索6
   */
  Explore6(36),
  /**
   * 探索7
   */
  Explore7(37),
  /**
   * 探索8
   */
  Explore8(38),
  /**
   * 探索9
   */
  Explore9(39),
  /**
   * 探索10
   */
  Explore10(40),
  /**
   * 科研2
   */
  Tech2(41);

  private final int value;

  private PsCityWorkQueueType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsCityWorkQueueType findByValue(int value) { 
    switch (value) {
      case 1:
        return Build1;
      case 2:
        return Build2;
      case 3:
        return Build3;
      case 4:
        return Build4;
      case 5:
        return Build5;
      case 6:
        return Build6;
      case 7:
        return Build7;
      case 8:
        return Build8;
      case 9:
        return Build9;
      case 11:
        return Tech;
      case 12:
        return Cure;
      case 13:
        return Train_Infantry;
      case 14:
        return Train_Archer;
      case 15:
        return Train_Cavalry;
      case 16:
        return Upgrade_Soldier;
      case 21:
        return FoodWorker;
      case 22:
        return WaterWorker;
      case 25:
        return WoodWorker;
      case 26:
        return IronWorker;
      case 27:
        return GoldWorker;
      case 31:
        return Explore1;
      case 32:
        return Explore2;
      case 33:
        return Explore3;
      case 34:
        return Explore4;
      case 35:
        return Explore5;
      case 36:
        return Explore6;
      case 37:
        return Explore7;
      case 38:
        return Explore8;
      case 39:
        return Explore9;
      case 40:
        return Explore10;
      case 41:
        return Tech2;
      default:
        return null;
    }
  }
}
