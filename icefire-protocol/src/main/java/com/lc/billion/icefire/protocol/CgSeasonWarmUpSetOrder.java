/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 选地顺序确认操作
 * @Message(7441)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgSeasonWarmUpSetOrder implements org.apache.thrift.TBase<CgSeasonWarmUpSetOrder, CgSeasonWarmUpSetOrder._Fields>, java.io.Serializable, Cloneable, Comparable<CgSeasonWarmUpSetOrder> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgSeasonWarmUpSetOrder");

  private static final org.apache.thrift.protocol.TField ORDER_FIELD_DESC = new org.apache.thrift.protocol.TField("order", org.apache.thrift.protocol.TType.I32, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgSeasonWarmUpSetOrderStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgSeasonWarmUpSetOrderTupleSchemeFactory();

  /**
   * 顺序 四个服 就是 1-4
   */
  public int order; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 顺序 四个服 就是 1-4
     */
    ORDER((short)1, "order");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ORDER
          return ORDER;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ORDER_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ORDER};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ORDER, new org.apache.thrift.meta_data.FieldMetaData("order", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgSeasonWarmUpSetOrder.class, metaDataMap);
  }

  public CgSeasonWarmUpSetOrder() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgSeasonWarmUpSetOrder(CgSeasonWarmUpSetOrder other) {
    __isset_bitfield = other.__isset_bitfield;
    this.order = other.order;
  }

  public CgSeasonWarmUpSetOrder deepCopy() {
    return new CgSeasonWarmUpSetOrder(this);
  }

  @Override
  public void clear() {
    setOrderIsSet(false);
    this.order = 0;
  }

  /**
   * 顺序 四个服 就是 1-4
   */
  public int getOrder() {
    return this.order;
  }

  /**
   * 顺序 四个服 就是 1-4
   */
  public CgSeasonWarmUpSetOrder setOrder(int order) {
    this.order = order;
    setOrderIsSet(true);
    return this;
  }

  public void unsetOrder() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ORDER_ISSET_ID);
  }

  /** Returns true if field order is set (has been assigned a value) and false otherwise */
  public boolean isSetOrder() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ORDER_ISSET_ID);
  }

  public void setOrderIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ORDER_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ORDER:
      if (value == null) {
        unsetOrder();
      } else {
        setOrder((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ORDER:
      return getOrder();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ORDER:
      return isSetOrder();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgSeasonWarmUpSetOrder)
      return this.equals((CgSeasonWarmUpSetOrder)that);
    return false;
  }

  public boolean equals(CgSeasonWarmUpSetOrder that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_order = true && this.isSetOrder();
    boolean that_present_order = true && that.isSetOrder();
    if (this_present_order || that_present_order) {
      if (!(this_present_order && that_present_order))
        return false;
      if (this.order != that.order)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetOrder()) ? 131071 : 524287);
    if (isSetOrder())
      hashCode = hashCode * 8191 + order;

    return hashCode;
  }

  @Override
  public int compareTo(CgSeasonWarmUpSetOrder other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetOrder(), other.isSetOrder());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrder()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.order, other.order);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgSeasonWarmUpSetOrder(");
    boolean first = true;

    if (isSetOrder()) {
      sb.append("order:");
      sb.append(this.order);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgSeasonWarmUpSetOrderStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgSeasonWarmUpSetOrderStandardScheme getScheme() {
      return new CgSeasonWarmUpSetOrderStandardScheme();
    }
  }

  private static class CgSeasonWarmUpSetOrderStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgSeasonWarmUpSetOrder> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgSeasonWarmUpSetOrder struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ORDER
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.order = iprot.readI32();
              struct.setOrderIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgSeasonWarmUpSetOrder struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetOrder()) {
        oprot.writeFieldBegin(ORDER_FIELD_DESC);
        oprot.writeI32(struct.order);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgSeasonWarmUpSetOrderTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgSeasonWarmUpSetOrderTupleScheme getScheme() {
      return new CgSeasonWarmUpSetOrderTupleScheme();
    }
  }

  private static class CgSeasonWarmUpSetOrderTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgSeasonWarmUpSetOrder> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgSeasonWarmUpSetOrder struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetOrder()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetOrder()) {
        oprot.writeI32(struct.order);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgSeasonWarmUpSetOrder struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.order = iprot.readI32();
        struct.setOrderIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

