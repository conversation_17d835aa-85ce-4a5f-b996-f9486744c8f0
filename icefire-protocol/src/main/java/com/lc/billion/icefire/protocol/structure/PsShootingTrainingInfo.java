/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 射击训练奖励内容
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsShootingTrainingInfo implements org.apache.thrift.TBase<PsShootingTrainingInfo, PsShootingTrainingInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PsShootingTrainingInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsShootingTrainingInfo");

  private static final org.apache.thrift.protocol.TField GROUP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("groupId", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField ITEM_FIELD_DESC = new org.apache.thrift.protocol.TField("item", org.apache.thrift.protocol.TType.STRUCT, (short)2);
  private static final org.apache.thrift.protocol.TField REWARD_FIELD_DESC = new org.apache.thrift.protocol.TField("reward", org.apache.thrift.protocol.TType.BOOL, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsShootingTrainingInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsShootingTrainingInfoTupleSchemeFactory();

  /**
   * groupId 库ID
   */
  public int groupId; // required
  /**
   * 奖励道具
   */
  public @org.apache.thrift.annotation.Nullable PsSimpleItem item; // required
  /**
   * 是否已领取
   */
  public boolean reward; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * groupId 库ID
     */
    GROUP_ID((short)1, "groupId"),
    /**
     * 奖励道具
     */
    ITEM((short)2, "item"),
    /**
     * 是否已领取
     */
    REWARD((short)3, "reward");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // GROUP_ID
          return GROUP_ID;
        case 2: // ITEM
          return ITEM;
        case 3: // REWARD
          return REWARD;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __GROUPID_ISSET_ID = 0;
  private static final int __REWARD_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.GROUP_ID, new org.apache.thrift.meta_data.FieldMetaData("groupId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ITEM, new org.apache.thrift.meta_data.FieldMetaData("item", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsSimpleItem.class)));
    tmpMap.put(_Fields.REWARD, new org.apache.thrift.meta_data.FieldMetaData("reward", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsShootingTrainingInfo.class, metaDataMap);
  }

  public PsShootingTrainingInfo() {
  }

  public PsShootingTrainingInfo(
    int groupId,
    PsSimpleItem item,
    boolean reward)
  {
    this();
    this.groupId = groupId;
    setGroupIdIsSet(true);
    this.item = item;
    this.reward = reward;
    setRewardIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsShootingTrainingInfo(PsShootingTrainingInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.groupId = other.groupId;
    if (other.isSetItem()) {
      this.item = new PsSimpleItem(other.item);
    }
    this.reward = other.reward;
  }

  public PsShootingTrainingInfo deepCopy() {
    return new PsShootingTrainingInfo(this);
  }

  @Override
  public void clear() {
    setGroupIdIsSet(false);
    this.groupId = 0;
    this.item = null;
    setRewardIsSet(false);
    this.reward = false;
  }

  /**
   * groupId 库ID
   */
  public int getGroupId() {
    return this.groupId;
  }

  /**
   * groupId 库ID
   */
  public PsShootingTrainingInfo setGroupId(int groupId) {
    this.groupId = groupId;
    setGroupIdIsSet(true);
    return this;
  }

  public void unsetGroupId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __GROUPID_ISSET_ID);
  }

  /** Returns true if field groupId is set (has been assigned a value) and false otherwise */
  public boolean isSetGroupId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __GROUPID_ISSET_ID);
  }

  public void setGroupIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __GROUPID_ISSET_ID, value);
  }

  /**
   * 奖励道具
   */
  @org.apache.thrift.annotation.Nullable
  public PsSimpleItem getItem() {
    return this.item;
  }

  /**
   * 奖励道具
   */
  public PsShootingTrainingInfo setItem(@org.apache.thrift.annotation.Nullable PsSimpleItem item) {
    this.item = item;
    return this;
  }

  public void unsetItem() {
    this.item = null;
  }

  /** Returns true if field item is set (has been assigned a value) and false otherwise */
  public boolean isSetItem() {
    return this.item != null;
  }

  public void setItemIsSet(boolean value) {
    if (!value) {
      this.item = null;
    }
  }

  /**
   * 是否已领取
   */
  public boolean isReward() {
    return this.reward;
  }

  /**
   * 是否已领取
   */
  public PsShootingTrainingInfo setReward(boolean reward) {
    this.reward = reward;
    setRewardIsSet(true);
    return this;
  }

  public void unsetReward() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __REWARD_ISSET_ID);
  }

  /** Returns true if field reward is set (has been assigned a value) and false otherwise */
  public boolean isSetReward() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __REWARD_ISSET_ID);
  }

  public void setRewardIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __REWARD_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case GROUP_ID:
      if (value == null) {
        unsetGroupId();
      } else {
        setGroupId((java.lang.Integer)value);
      }
      break;

    case ITEM:
      if (value == null) {
        unsetItem();
      } else {
        setItem((PsSimpleItem)value);
      }
      break;

    case REWARD:
      if (value == null) {
        unsetReward();
      } else {
        setReward((java.lang.Boolean)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case GROUP_ID:
      return getGroupId();

    case ITEM:
      return getItem();

    case REWARD:
      return isReward();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case GROUP_ID:
      return isSetGroupId();
    case ITEM:
      return isSetItem();
    case REWARD:
      return isSetReward();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsShootingTrainingInfo)
      return this.equals((PsShootingTrainingInfo)that);
    return false;
  }

  public boolean equals(PsShootingTrainingInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_groupId = true;
    boolean that_present_groupId = true;
    if (this_present_groupId || that_present_groupId) {
      if (!(this_present_groupId && that_present_groupId))
        return false;
      if (this.groupId != that.groupId)
        return false;
    }

    boolean this_present_item = true && this.isSetItem();
    boolean that_present_item = true && that.isSetItem();
    if (this_present_item || that_present_item) {
      if (!(this_present_item && that_present_item))
        return false;
      if (!this.item.equals(that.item))
        return false;
    }

    boolean this_present_reward = true;
    boolean that_present_reward = true;
    if (this_present_reward || that_present_reward) {
      if (!(this_present_reward && that_present_reward))
        return false;
      if (this.reward != that.reward)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + groupId;

    hashCode = hashCode * 8191 + ((isSetItem()) ? 131071 : 524287);
    if (isSetItem())
      hashCode = hashCode * 8191 + item.hashCode();

    hashCode = hashCode * 8191 + ((reward) ? 131071 : 524287);

    return hashCode;
  }

  @Override
  public int compareTo(PsShootingTrainingInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetGroupId(), other.isSetGroupId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGroupId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.groupId, other.groupId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetItem(), other.isSetItem());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItem()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.item, other.item);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetReward(), other.isSetReward());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReward()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reward, other.reward);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsShootingTrainingInfo(");
    boolean first = true;

    sb.append("groupId:");
    sb.append(this.groupId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("item:");
    if (this.item == null) {
      sb.append("null");
    } else {
      sb.append(this.item);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("reward:");
    sb.append(this.reward);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'groupId' because it's a primitive and you chose the non-beans generator.
    if (item == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'item' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'reward' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
    if (item != null) {
      item.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsShootingTrainingInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsShootingTrainingInfoStandardScheme getScheme() {
      return new PsShootingTrainingInfoStandardScheme();
    }
  }

  private static class PsShootingTrainingInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsShootingTrainingInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsShootingTrainingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // GROUP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.groupId = iprot.readI32();
              struct.setGroupIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ITEM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.item = new PsSimpleItem();
              struct.item.read(iprot);
              struct.setItemIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REWARD
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.reward = iprot.readBool();
              struct.setRewardIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetGroupId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'groupId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetReward()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'reward' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsShootingTrainingInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(GROUP_ID_FIELD_DESC);
      oprot.writeI32(struct.groupId);
      oprot.writeFieldEnd();
      if (struct.item != null) {
        oprot.writeFieldBegin(ITEM_FIELD_DESC);
        struct.item.write(oprot);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(REWARD_FIELD_DESC);
      oprot.writeBool(struct.reward);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsShootingTrainingInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsShootingTrainingInfoTupleScheme getScheme() {
      return new PsShootingTrainingInfoTupleScheme();
    }
  }

  private static class PsShootingTrainingInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsShootingTrainingInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsShootingTrainingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.groupId);
      struct.item.write(oprot);
      oprot.writeBool(struct.reward);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsShootingTrainingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.groupId = iprot.readI32();
      struct.setGroupIdIsSet(true);
      struct.item = new PsSimpleItem();
      struct.item.read(iprot);
      struct.setItemIsSet(true);
      struct.reward = iprot.readBool();
      struct.setRewardIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

