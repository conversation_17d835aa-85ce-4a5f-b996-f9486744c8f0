/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 联盟事务
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsAllianceAffair implements org.apache.thrift.TBase<PsAllianceAffair, PsAllianceAffair._Fields>, java.io.Serializable, Cloneable, Comparable<PsAllianceAffair> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsAllianceAffair");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField ALLIANCE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField AFFAIR_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("affairType", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField TARGET_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("targetMetaId", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField X_FIELD_DESC = new org.apache.thrift.protocol.TField("x", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField Y_FIELD_DESC = new org.apache.thrift.protocol.TField("y", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField TARGET_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("targetTime", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField SLOGAN_FIELD_DESC = new org.apache.thrift.protocol.TField("slogan", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField PARTICIPATE_PPLS_FIELD_DESC = new org.apache.thrift.protocol.TField("participatePPLs", org.apache.thrift.protocol.TType.I32, (short)9);
  private static final org.apache.thrift.protocol.TField IS_PARTICIPATE_FIELD_DESC = new org.apache.thrift.protocol.TField("isParticipate", org.apache.thrift.protocol.TType.BOOL, (short)10);
  private static final org.apache.thrift.protocol.TField IS_TOP_FIELD_DESC = new org.apache.thrift.protocol.TField("isTop", org.apache.thrift.protocol.TType.BOOL, (short)11);
  private static final org.apache.thrift.protocol.TField SERVER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("serverId", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.I64, (short)14);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsAllianceAffairStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsAllianceAffairTupleSchemeFactory();

  public long id; // required
  public long allianceId; // required
  /**
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsAllianceAffairType
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsAllianceAffairType affairType; // required
  public @org.apache.thrift.annotation.Nullable java.lang.String targetMetaId; // optional
  public int x; // optional
  public int y; // optional
  public long targetTime; // required
  public @org.apache.thrift.annotation.Nullable java.lang.String slogan; // optional
  public int participatePPLs; // optional
  public boolean isParticipate; // optional
  public boolean isTop; // optional
  /**
   * 事务实体所在服务器id
   */
  public int serverId; // optional
  public long endTime; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ID((short)1, "id"),
    ALLIANCE_ID((short)2, "allianceId"),
    /**
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsAllianceAffairType
     */
    AFFAIR_TYPE((short)3, "affairType"),
    TARGET_META_ID((short)4, "targetMetaId"),
    X((short)5, "x"),
    Y((short)6, "y"),
    TARGET_TIME((short)7, "targetTime"),
    SLOGAN((short)8, "slogan"),
    PARTICIPATE_PPLS((short)9, "participatePPLs"),
    IS_PARTICIPATE((short)10, "isParticipate"),
    IS_TOP((short)11, "isTop"),
    /**
     * 事务实体所在服务器id
     */
    SERVER_ID((short)12, "serverId"),
    END_TIME((short)14, "endTime");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // ALLIANCE_ID
          return ALLIANCE_ID;
        case 3: // AFFAIR_TYPE
          return AFFAIR_TYPE;
        case 4: // TARGET_META_ID
          return TARGET_META_ID;
        case 5: // X
          return X;
        case 6: // Y
          return Y;
        case 7: // TARGET_TIME
          return TARGET_TIME;
        case 8: // SLOGAN
          return SLOGAN;
        case 9: // PARTICIPATE_PPLS
          return PARTICIPATE_PPLS;
        case 10: // IS_PARTICIPATE
          return IS_PARTICIPATE;
        case 11: // IS_TOP
          return IS_TOP;
        case 12: // SERVER_ID
          return SERVER_ID;
        case 14: // END_TIME
          return END_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ID_ISSET_ID = 0;
  private static final int __ALLIANCEID_ISSET_ID = 1;
  private static final int __X_ISSET_ID = 2;
  private static final int __Y_ISSET_ID = 3;
  private static final int __TARGETTIME_ISSET_ID = 4;
  private static final int __PARTICIPATEPPLS_ISSET_ID = 5;
  private static final int __ISPARTICIPATE_ISSET_ID = 6;
  private static final int __ISTOP_ISSET_ID = 7;
  private static final int __SERVERID_ISSET_ID = 8;
  private static final int __ENDTIME_ISSET_ID = 9;
  private short __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.TARGET_META_ID,_Fields.X,_Fields.Y,_Fields.SLOGAN,_Fields.PARTICIPATE_PPLS,_Fields.IS_PARTICIPATE,_Fields.IS_TOP,_Fields.SERVER_ID};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ALLIANCE_ID, new org.apache.thrift.meta_data.FieldMetaData("allianceId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.AFFAIR_TYPE, new org.apache.thrift.meta_data.FieldMetaData("affairType", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsAllianceAffairType.class)));
    tmpMap.put(_Fields.TARGET_META_ID, new org.apache.thrift.meta_data.FieldMetaData("targetMetaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.X, new org.apache.thrift.meta_data.FieldMetaData("x", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.Y, new org.apache.thrift.meta_data.FieldMetaData("y", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TARGET_TIME, new org.apache.thrift.meta_data.FieldMetaData("targetTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SLOGAN, new org.apache.thrift.meta_data.FieldMetaData("slogan", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PARTICIPATE_PPLS, new org.apache.thrift.meta_data.FieldMetaData("participatePPLs", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IS_PARTICIPATE, new org.apache.thrift.meta_data.FieldMetaData("isParticipate", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.IS_TOP, new org.apache.thrift.meta_data.FieldMetaData("isTop", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.SERVER_ID, new org.apache.thrift.meta_data.FieldMetaData("serverId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsAllianceAffair.class, metaDataMap);
  }

  public PsAllianceAffair() {
  }

  public PsAllianceAffair(
    long id,
    long allianceId,
    com.lc.billion.icefire.protocol.constant.PsAllianceAffairType affairType,
    long targetTime,
    long endTime)
  {
    this();
    this.id = id;
    setIdIsSet(true);
    this.allianceId = allianceId;
    setAllianceIdIsSet(true);
    this.affairType = affairType;
    this.targetTime = targetTime;
    setTargetTimeIsSet(true);
    this.endTime = endTime;
    setEndTimeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsAllianceAffair(PsAllianceAffair other) {
    __isset_bitfield = other.__isset_bitfield;
    this.id = other.id;
    this.allianceId = other.allianceId;
    if (other.isSetAffairType()) {
      this.affairType = other.affairType;
    }
    if (other.isSetTargetMetaId()) {
      this.targetMetaId = other.targetMetaId;
    }
    this.x = other.x;
    this.y = other.y;
    this.targetTime = other.targetTime;
    if (other.isSetSlogan()) {
      this.slogan = other.slogan;
    }
    this.participatePPLs = other.participatePPLs;
    this.isParticipate = other.isParticipate;
    this.isTop = other.isTop;
    this.serverId = other.serverId;
    this.endTime = other.endTime;
  }

  public PsAllianceAffair deepCopy() {
    return new PsAllianceAffair(this);
  }

  @Override
  public void clear() {
    setIdIsSet(false);
    this.id = 0;
    setAllianceIdIsSet(false);
    this.allianceId = 0;
    this.affairType = null;
    this.targetMetaId = null;
    setXIsSet(false);
    this.x = 0;
    setYIsSet(false);
    this.y = 0;
    setTargetTimeIsSet(false);
    this.targetTime = 0;
    this.slogan = null;
    setParticipatePPLsIsSet(false);
    this.participatePPLs = 0;
    setIsParticipateIsSet(false);
    this.isParticipate = false;
    setIsTopIsSet(false);
    this.isTop = false;
    setServerIdIsSet(false);
    this.serverId = 0;
    setEndTimeIsSet(false);
    this.endTime = 0;
  }

  public long getId() {
    return this.id;
  }

  public PsAllianceAffair setId(long id) {
    this.id = id;
    setIdIsSet(true);
    return this;
  }

  public void unsetId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
  }

  public void setIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
  }

  public long getAllianceId() {
    return this.allianceId;
  }

  public PsAllianceAffair setAllianceId(long allianceId) {
    this.allianceId = allianceId;
    setAllianceIdIsSet(true);
    return this;
  }

  public void unsetAllianceId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ALLIANCEID_ISSET_ID);
  }

  /** Returns true if field allianceId is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ALLIANCEID_ISSET_ID);
  }

  public void setAllianceIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ALLIANCEID_ISSET_ID, value);
  }

  /**
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsAllianceAffairType
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsAllianceAffairType getAffairType() {
    return this.affairType;
  }

  /**
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsAllianceAffairType
   */
  public PsAllianceAffair setAffairType(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsAllianceAffairType affairType) {
    this.affairType = affairType;
    return this;
  }

  public void unsetAffairType() {
    this.affairType = null;
  }

  /** Returns true if field affairType is set (has been assigned a value) and false otherwise */
  public boolean isSetAffairType() {
    return this.affairType != null;
  }

  public void setAffairTypeIsSet(boolean value) {
    if (!value) {
      this.affairType = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getTargetMetaId() {
    return this.targetMetaId;
  }

  public PsAllianceAffair setTargetMetaId(@org.apache.thrift.annotation.Nullable java.lang.String targetMetaId) {
    this.targetMetaId = targetMetaId;
    return this;
  }

  public void unsetTargetMetaId() {
    this.targetMetaId = null;
  }

  /** Returns true if field targetMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetMetaId() {
    return this.targetMetaId != null;
  }

  public void setTargetMetaIdIsSet(boolean value) {
    if (!value) {
      this.targetMetaId = null;
    }
  }

  public int getX() {
    return this.x;
  }

  public PsAllianceAffair setX(int x) {
    this.x = x;
    setXIsSet(true);
    return this;
  }

  public void unsetX() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __X_ISSET_ID);
  }

  /** Returns true if field x is set (has been assigned a value) and false otherwise */
  public boolean isSetX() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __X_ISSET_ID);
  }

  public void setXIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __X_ISSET_ID, value);
  }

  public int getY() {
    return this.y;
  }

  public PsAllianceAffair setY(int y) {
    this.y = y;
    setYIsSet(true);
    return this;
  }

  public void unsetY() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __Y_ISSET_ID);
  }

  /** Returns true if field y is set (has been assigned a value) and false otherwise */
  public boolean isSetY() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __Y_ISSET_ID);
  }

  public void setYIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __Y_ISSET_ID, value);
  }

  public long getTargetTime() {
    return this.targetTime;
  }

  public PsAllianceAffair setTargetTime(long targetTime) {
    this.targetTime = targetTime;
    setTargetTimeIsSet(true);
    return this;
  }

  public void unsetTargetTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TARGETTIME_ISSET_ID);
  }

  /** Returns true if field targetTime is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TARGETTIME_ISSET_ID);
  }

  public void setTargetTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TARGETTIME_ISSET_ID, value);
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getSlogan() {
    return this.slogan;
  }

  public PsAllianceAffair setSlogan(@org.apache.thrift.annotation.Nullable java.lang.String slogan) {
    this.slogan = slogan;
    return this;
  }

  public void unsetSlogan() {
    this.slogan = null;
  }

  /** Returns true if field slogan is set (has been assigned a value) and false otherwise */
  public boolean isSetSlogan() {
    return this.slogan != null;
  }

  public void setSloganIsSet(boolean value) {
    if (!value) {
      this.slogan = null;
    }
  }

  public int getParticipatePPLs() {
    return this.participatePPLs;
  }

  public PsAllianceAffair setParticipatePPLs(int participatePPLs) {
    this.participatePPLs = participatePPLs;
    setParticipatePPLsIsSet(true);
    return this;
  }

  public void unsetParticipatePPLs() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PARTICIPATEPPLS_ISSET_ID);
  }

  /** Returns true if field participatePPLs is set (has been assigned a value) and false otherwise */
  public boolean isSetParticipatePPLs() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PARTICIPATEPPLS_ISSET_ID);
  }

  public void setParticipatePPLsIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PARTICIPATEPPLS_ISSET_ID, value);
  }

  public boolean isIsParticipate() {
    return this.isParticipate;
  }

  public PsAllianceAffair setIsParticipate(boolean isParticipate) {
    this.isParticipate = isParticipate;
    setIsParticipateIsSet(true);
    return this;
  }

  public void unsetIsParticipate() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ISPARTICIPATE_ISSET_ID);
  }

  /** Returns true if field isParticipate is set (has been assigned a value) and false otherwise */
  public boolean isSetIsParticipate() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ISPARTICIPATE_ISSET_ID);
  }

  public void setIsParticipateIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ISPARTICIPATE_ISSET_ID, value);
  }

  public boolean isIsTop() {
    return this.isTop;
  }

  public PsAllianceAffair setIsTop(boolean isTop) {
    this.isTop = isTop;
    setIsTopIsSet(true);
    return this;
  }

  public void unsetIsTop() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ISTOP_ISSET_ID);
  }

  /** Returns true if field isTop is set (has been assigned a value) and false otherwise */
  public boolean isSetIsTop() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ISTOP_ISSET_ID);
  }

  public void setIsTopIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ISTOP_ISSET_ID, value);
  }

  /**
   * 事务实体所在服务器id
   */
  public int getServerId() {
    return this.serverId;
  }

  /**
   * 事务实体所在服务器id
   */
  public PsAllianceAffair setServerId(int serverId) {
    this.serverId = serverId;
    setServerIdIsSet(true);
    return this;
  }

  public void unsetServerId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SERVERID_ISSET_ID);
  }

  /** Returns true if field serverId is set (has been assigned a value) and false otherwise */
  public boolean isSetServerId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SERVERID_ISSET_ID);
  }

  public void setServerIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SERVERID_ISSET_ID, value);
  }

  public long getEndTime() {
    return this.endTime;
  }

  public PsAllianceAffair setEndTime(long endTime) {
    this.endTime = endTime;
    setEndTimeIsSet(true);
    return this;
  }

  public void unsetEndTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
  public boolean isSetEndTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  public void setEndTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ENDTIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((java.lang.Long)value);
      }
      break;

    case ALLIANCE_ID:
      if (value == null) {
        unsetAllianceId();
      } else {
        setAllianceId((java.lang.Long)value);
      }
      break;

    case AFFAIR_TYPE:
      if (value == null) {
        unsetAffairType();
      } else {
        setAffairType((com.lc.billion.icefire.protocol.constant.PsAllianceAffairType)value);
      }
      break;

    case TARGET_META_ID:
      if (value == null) {
        unsetTargetMetaId();
      } else {
        setTargetMetaId((java.lang.String)value);
      }
      break;

    case X:
      if (value == null) {
        unsetX();
      } else {
        setX((java.lang.Integer)value);
      }
      break;

    case Y:
      if (value == null) {
        unsetY();
      } else {
        setY((java.lang.Integer)value);
      }
      break;

    case TARGET_TIME:
      if (value == null) {
        unsetTargetTime();
      } else {
        setTargetTime((java.lang.Long)value);
      }
      break;

    case SLOGAN:
      if (value == null) {
        unsetSlogan();
      } else {
        setSlogan((java.lang.String)value);
      }
      break;

    case PARTICIPATE_PPLS:
      if (value == null) {
        unsetParticipatePPLs();
      } else {
        setParticipatePPLs((java.lang.Integer)value);
      }
      break;

    case IS_PARTICIPATE:
      if (value == null) {
        unsetIsParticipate();
      } else {
        setIsParticipate((java.lang.Boolean)value);
      }
      break;

    case IS_TOP:
      if (value == null) {
        unsetIsTop();
      } else {
        setIsTop((java.lang.Boolean)value);
      }
      break;

    case SERVER_ID:
      if (value == null) {
        unsetServerId();
      } else {
        setServerId((java.lang.Integer)value);
      }
      break;

    case END_TIME:
      if (value == null) {
        unsetEndTime();
      } else {
        setEndTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case ALLIANCE_ID:
      return getAllianceId();

    case AFFAIR_TYPE:
      return getAffairType();

    case TARGET_META_ID:
      return getTargetMetaId();

    case X:
      return getX();

    case Y:
      return getY();

    case TARGET_TIME:
      return getTargetTime();

    case SLOGAN:
      return getSlogan();

    case PARTICIPATE_PPLS:
      return getParticipatePPLs();

    case IS_PARTICIPATE:
      return isIsParticipate();

    case IS_TOP:
      return isIsTop();

    case SERVER_ID:
      return getServerId();

    case END_TIME:
      return getEndTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case ALLIANCE_ID:
      return isSetAllianceId();
    case AFFAIR_TYPE:
      return isSetAffairType();
    case TARGET_META_ID:
      return isSetTargetMetaId();
    case X:
      return isSetX();
    case Y:
      return isSetY();
    case TARGET_TIME:
      return isSetTargetTime();
    case SLOGAN:
      return isSetSlogan();
    case PARTICIPATE_PPLS:
      return isSetParticipatePPLs();
    case IS_PARTICIPATE:
      return isSetIsParticipate();
    case IS_TOP:
      return isSetIsTop();
    case SERVER_ID:
      return isSetServerId();
    case END_TIME:
      return isSetEndTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsAllianceAffair)
      return this.equals((PsAllianceAffair)that);
    return false;
  }

  public boolean equals(PsAllianceAffair that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_id = true;
    boolean that_present_id = true;
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (this.id != that.id)
        return false;
    }

    boolean this_present_allianceId = true;
    boolean that_present_allianceId = true;
    if (this_present_allianceId || that_present_allianceId) {
      if (!(this_present_allianceId && that_present_allianceId))
        return false;
      if (this.allianceId != that.allianceId)
        return false;
    }

    boolean this_present_affairType = true && this.isSetAffairType();
    boolean that_present_affairType = true && that.isSetAffairType();
    if (this_present_affairType || that_present_affairType) {
      if (!(this_present_affairType && that_present_affairType))
        return false;
      if (!this.affairType.equals(that.affairType))
        return false;
    }

    boolean this_present_targetMetaId = true && this.isSetTargetMetaId();
    boolean that_present_targetMetaId = true && that.isSetTargetMetaId();
    if (this_present_targetMetaId || that_present_targetMetaId) {
      if (!(this_present_targetMetaId && that_present_targetMetaId))
        return false;
      if (!this.targetMetaId.equals(that.targetMetaId))
        return false;
    }

    boolean this_present_x = true && this.isSetX();
    boolean that_present_x = true && that.isSetX();
    if (this_present_x || that_present_x) {
      if (!(this_present_x && that_present_x))
        return false;
      if (this.x != that.x)
        return false;
    }

    boolean this_present_y = true && this.isSetY();
    boolean that_present_y = true && that.isSetY();
    if (this_present_y || that_present_y) {
      if (!(this_present_y && that_present_y))
        return false;
      if (this.y != that.y)
        return false;
    }

    boolean this_present_targetTime = true;
    boolean that_present_targetTime = true;
    if (this_present_targetTime || that_present_targetTime) {
      if (!(this_present_targetTime && that_present_targetTime))
        return false;
      if (this.targetTime != that.targetTime)
        return false;
    }

    boolean this_present_slogan = true && this.isSetSlogan();
    boolean that_present_slogan = true && that.isSetSlogan();
    if (this_present_slogan || that_present_slogan) {
      if (!(this_present_slogan && that_present_slogan))
        return false;
      if (!this.slogan.equals(that.slogan))
        return false;
    }

    boolean this_present_participatePPLs = true && this.isSetParticipatePPLs();
    boolean that_present_participatePPLs = true && that.isSetParticipatePPLs();
    if (this_present_participatePPLs || that_present_participatePPLs) {
      if (!(this_present_participatePPLs && that_present_participatePPLs))
        return false;
      if (this.participatePPLs != that.participatePPLs)
        return false;
    }

    boolean this_present_isParticipate = true && this.isSetIsParticipate();
    boolean that_present_isParticipate = true && that.isSetIsParticipate();
    if (this_present_isParticipate || that_present_isParticipate) {
      if (!(this_present_isParticipate && that_present_isParticipate))
        return false;
      if (this.isParticipate != that.isParticipate)
        return false;
    }

    boolean this_present_isTop = true && this.isSetIsTop();
    boolean that_present_isTop = true && that.isSetIsTop();
    if (this_present_isTop || that_present_isTop) {
      if (!(this_present_isTop && that_present_isTop))
        return false;
      if (this.isTop != that.isTop)
        return false;
    }

    boolean this_present_serverId = true && this.isSetServerId();
    boolean that_present_serverId = true && that.isSetServerId();
    if (this_present_serverId || that_present_serverId) {
      if (!(this_present_serverId && that_present_serverId))
        return false;
      if (this.serverId != that.serverId)
        return false;
    }

    boolean this_present_endTime = true;
    boolean that_present_endTime = true;
    if (this_present_endTime || that_present_endTime) {
      if (!(this_present_endTime && that_present_endTime))
        return false;
      if (this.endTime != that.endTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(id);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(allianceId);

    hashCode = hashCode * 8191 + ((isSetAffairType()) ? 131071 : 524287);
    if (isSetAffairType())
      hashCode = hashCode * 8191 + affairType.getValue();

    hashCode = hashCode * 8191 + ((isSetTargetMetaId()) ? 131071 : 524287);
    if (isSetTargetMetaId())
      hashCode = hashCode * 8191 + targetMetaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetX()) ? 131071 : 524287);
    if (isSetX())
      hashCode = hashCode * 8191 + x;

    hashCode = hashCode * 8191 + ((isSetY()) ? 131071 : 524287);
    if (isSetY())
      hashCode = hashCode * 8191 + y;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(targetTime);

    hashCode = hashCode * 8191 + ((isSetSlogan()) ? 131071 : 524287);
    if (isSetSlogan())
      hashCode = hashCode * 8191 + slogan.hashCode();

    hashCode = hashCode * 8191 + ((isSetParticipatePPLs()) ? 131071 : 524287);
    if (isSetParticipatePPLs())
      hashCode = hashCode * 8191 + participatePPLs;

    hashCode = hashCode * 8191 + ((isSetIsParticipate()) ? 131071 : 524287);
    if (isSetIsParticipate())
      hashCode = hashCode * 8191 + ((isParticipate) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetIsTop()) ? 131071 : 524287);
    if (isSetIsTop())
      hashCode = hashCode * 8191 + ((isTop) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetServerId()) ? 131071 : 524287);
    if (isSetServerId())
      hashCode = hashCode * 8191 + serverId;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(endTime);

    return hashCode;
  }

  @Override
  public int compareTo(PsAllianceAffair other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetId(), other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAllianceId(), other.isSetAllianceId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceId, other.allianceId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAffairType(), other.isSetAffairType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAffairType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.affairType, other.affairType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTargetMetaId(), other.isSetTargetMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetMetaId, other.targetMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetX(), other.isSetX());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetX()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.x, other.x);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetY(), other.isSetY());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetY()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.y, other.y);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTargetTime(), other.isSetTargetTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetTime, other.targetTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSlogan(), other.isSetSlogan());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSlogan()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.slogan, other.slogan);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetParticipatePPLs(), other.isSetParticipatePPLs());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetParticipatePPLs()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.participatePPLs, other.participatePPLs);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetIsParticipate(), other.isSetIsParticipate());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsParticipate()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isParticipate, other.isParticipate);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetIsTop(), other.isSetIsTop());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsTop()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isTop, other.isTop);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetServerId(), other.isSetServerId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetServerId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.serverId, other.serverId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEndTime(), other.isSetEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsAllianceAffair(");
    boolean first = true;

    sb.append("id:");
    sb.append(this.id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("allianceId:");
    sb.append(this.allianceId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("affairType:");
    if (this.affairType == null) {
      sb.append("null");
    } else {
      sb.append(this.affairType);
    }
    first = false;
    if (isSetTargetMetaId()) {
      if (!first) sb.append(", ");
      sb.append("targetMetaId:");
      if (this.targetMetaId == null) {
        sb.append("null");
      } else {
        sb.append(this.targetMetaId);
      }
      first = false;
    }
    if (isSetX()) {
      if (!first) sb.append(", ");
      sb.append("x:");
      sb.append(this.x);
      first = false;
    }
    if (isSetY()) {
      if (!first) sb.append(", ");
      sb.append("y:");
      sb.append(this.y);
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("targetTime:");
    sb.append(this.targetTime);
    first = false;
    if (isSetSlogan()) {
      if (!first) sb.append(", ");
      sb.append("slogan:");
      if (this.slogan == null) {
        sb.append("null");
      } else {
        sb.append(this.slogan);
      }
      first = false;
    }
    if (isSetParticipatePPLs()) {
      if (!first) sb.append(", ");
      sb.append("participatePPLs:");
      sb.append(this.participatePPLs);
      first = false;
    }
    if (isSetIsParticipate()) {
      if (!first) sb.append(", ");
      sb.append("isParticipate:");
      sb.append(this.isParticipate);
      first = false;
    }
    if (isSetIsTop()) {
      if (!first) sb.append(", ");
      sb.append("isTop:");
      sb.append(this.isTop);
      first = false;
    }
    if (isSetServerId()) {
      if (!first) sb.append(", ");
      sb.append("serverId:");
      sb.append(this.serverId);
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("endTime:");
    sb.append(this.endTime);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'id' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'allianceId' because it's a primitive and you chose the non-beans generator.
    if (affairType == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'affairType' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'targetTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'endTime' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsAllianceAffairStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsAllianceAffairStandardScheme getScheme() {
      return new PsAllianceAffairStandardScheme();
    }
  }

  private static class PsAllianceAffairStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsAllianceAffair> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsAllianceAffair struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.id = iprot.readI64();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ALLIANCE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.allianceId = iprot.readI64();
              struct.setAllianceIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // AFFAIR_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.affairType = com.lc.billion.icefire.protocol.constant.PsAllianceAffairType.findByValue(iprot.readI32());
              struct.setAffairTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TARGET_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.targetMetaId = iprot.readString();
              struct.setTargetMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // X
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.x = iprot.readI32();
              struct.setXIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // Y
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.y = iprot.readI32();
              struct.setYIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // TARGET_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.targetTime = iprot.readI64();
              struct.setTargetTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // SLOGAN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.slogan = iprot.readString();
              struct.setSloganIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // PARTICIPATE_PPLS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.participatePPLs = iprot.readI32();
              struct.setParticipatePPLsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // IS_PARTICIPATE
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isParticipate = iprot.readBool();
              struct.setIsParticipateIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // IS_TOP
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isTop = iprot.readBool();
              struct.setIsTopIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // SERVER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.serverId = iprot.readI32();
              struct.setServerIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.endTime = iprot.readI64();
              struct.setEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'id' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAllianceId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'allianceId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTargetTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'targetTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetEndTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'endTime' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsAllianceAffair struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ID_FIELD_DESC);
      oprot.writeI64(struct.id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ALLIANCE_ID_FIELD_DESC);
      oprot.writeI64(struct.allianceId);
      oprot.writeFieldEnd();
      if (struct.affairType != null) {
        oprot.writeFieldBegin(AFFAIR_TYPE_FIELD_DESC);
        oprot.writeI32(struct.affairType.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.targetMetaId != null) {
        if (struct.isSetTargetMetaId()) {
          oprot.writeFieldBegin(TARGET_META_ID_FIELD_DESC);
          oprot.writeString(struct.targetMetaId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetX()) {
        oprot.writeFieldBegin(X_FIELD_DESC);
        oprot.writeI32(struct.x);
        oprot.writeFieldEnd();
      }
      if (struct.isSetY()) {
        oprot.writeFieldBegin(Y_FIELD_DESC);
        oprot.writeI32(struct.y);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TARGET_TIME_FIELD_DESC);
      oprot.writeI64(struct.targetTime);
      oprot.writeFieldEnd();
      if (struct.slogan != null) {
        if (struct.isSetSlogan()) {
          oprot.writeFieldBegin(SLOGAN_FIELD_DESC);
          oprot.writeString(struct.slogan);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetParticipatePPLs()) {
        oprot.writeFieldBegin(PARTICIPATE_PPLS_FIELD_DESC);
        oprot.writeI32(struct.participatePPLs);
        oprot.writeFieldEnd();
      }
      if (struct.isSetIsParticipate()) {
        oprot.writeFieldBegin(IS_PARTICIPATE_FIELD_DESC);
        oprot.writeBool(struct.isParticipate);
        oprot.writeFieldEnd();
      }
      if (struct.isSetIsTop()) {
        oprot.writeFieldBegin(IS_TOP_FIELD_DESC);
        oprot.writeBool(struct.isTop);
        oprot.writeFieldEnd();
      }
      if (struct.isSetServerId()) {
        oprot.writeFieldBegin(SERVER_ID_FIELD_DESC);
        oprot.writeI32(struct.serverId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(END_TIME_FIELD_DESC);
      oprot.writeI64(struct.endTime);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsAllianceAffairTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsAllianceAffairTupleScheme getScheme() {
      return new PsAllianceAffairTupleScheme();
    }
  }

  private static class PsAllianceAffairTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsAllianceAffair> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsAllianceAffair struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.id);
      oprot.writeI64(struct.allianceId);
      oprot.writeI32(struct.affairType.getValue());
      oprot.writeI64(struct.targetTime);
      oprot.writeI64(struct.endTime);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetTargetMetaId()) {
        optionals.set(0);
      }
      if (struct.isSetX()) {
        optionals.set(1);
      }
      if (struct.isSetY()) {
        optionals.set(2);
      }
      if (struct.isSetSlogan()) {
        optionals.set(3);
      }
      if (struct.isSetParticipatePPLs()) {
        optionals.set(4);
      }
      if (struct.isSetIsParticipate()) {
        optionals.set(5);
      }
      if (struct.isSetIsTop()) {
        optionals.set(6);
      }
      if (struct.isSetServerId()) {
        optionals.set(7);
      }
      oprot.writeBitSet(optionals, 8);
      if (struct.isSetTargetMetaId()) {
        oprot.writeString(struct.targetMetaId);
      }
      if (struct.isSetX()) {
        oprot.writeI32(struct.x);
      }
      if (struct.isSetY()) {
        oprot.writeI32(struct.y);
      }
      if (struct.isSetSlogan()) {
        oprot.writeString(struct.slogan);
      }
      if (struct.isSetParticipatePPLs()) {
        oprot.writeI32(struct.participatePPLs);
      }
      if (struct.isSetIsParticipate()) {
        oprot.writeBool(struct.isParticipate);
      }
      if (struct.isSetIsTop()) {
        oprot.writeBool(struct.isTop);
      }
      if (struct.isSetServerId()) {
        oprot.writeI32(struct.serverId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsAllianceAffair struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.id = iprot.readI64();
      struct.setIdIsSet(true);
      struct.allianceId = iprot.readI64();
      struct.setAllianceIdIsSet(true);
      struct.affairType = com.lc.billion.icefire.protocol.constant.PsAllianceAffairType.findByValue(iprot.readI32());
      struct.setAffairTypeIsSet(true);
      struct.targetTime = iprot.readI64();
      struct.setTargetTimeIsSet(true);
      struct.endTime = iprot.readI64();
      struct.setEndTimeIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(8);
      if (incoming.get(0)) {
        struct.targetMetaId = iprot.readString();
        struct.setTargetMetaIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.x = iprot.readI32();
        struct.setXIsSet(true);
      }
      if (incoming.get(2)) {
        struct.y = iprot.readI32();
        struct.setYIsSet(true);
      }
      if (incoming.get(3)) {
        struct.slogan = iprot.readString();
        struct.setSloganIsSet(true);
      }
      if (incoming.get(4)) {
        struct.participatePPLs = iprot.readI32();
        struct.setParticipatePPLsIsSet(true);
      }
      if (incoming.get(5)) {
        struct.isParticipate = iprot.readBool();
        struct.setIsParticipateIsSet(true);
      }
      if (incoming.get(6)) {
        struct.isTop = iprot.readBool();
        struct.setIsTopIsSet(true);
      }
      if (incoming.get(7)) {
        struct.serverId = iprot.readI32();
        struct.setServerIdIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

