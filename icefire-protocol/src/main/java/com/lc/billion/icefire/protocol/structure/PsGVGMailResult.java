/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsGVGMailResult implements org.apache.thrift.TBase<PsGVGMailResult, PsGVGMailResult._Fields>, java.io.Serializable, Cloneable, Comparable<PsGVGMailResult> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsGVGMailResult");

  private static final org.apache.thrift.protocol.TField RESULT_FIELD_DESC = new org.apache.thrift.protocol.TField("result", org.apache.thrift.protocol.TType.BOOL, (short)1);
  private static final org.apache.thrift.protocol.TField ALLIANCE_RESULT1_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceResult1", org.apache.thrift.protocol.TType.STRUCT, (short)2);
  private static final org.apache.thrift.protocol.TField ALLIANCE_RESULT2_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceResult2", org.apache.thrift.protocol.TType.STRUCT, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsGVGMailResultStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsGVGMailResultTupleSchemeFactory();

  public boolean result; // required
  public @org.apache.thrift.annotation.Nullable PsGVGMailAllianceResult allianceResult1; // required
  public @org.apache.thrift.annotation.Nullable PsGVGMailAllianceResult allianceResult2; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RESULT((short)1, "result"),
    ALLIANCE_RESULT1((short)2, "allianceResult1"),
    ALLIANCE_RESULT2((short)3, "allianceResult2");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RESULT
          return RESULT;
        case 2: // ALLIANCE_RESULT1
          return ALLIANCE_RESULT1;
        case 3: // ALLIANCE_RESULT2
          return ALLIANCE_RESULT2;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RESULT_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ALLIANCE_RESULT2};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RESULT, new org.apache.thrift.meta_data.FieldMetaData("result", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.ALLIANCE_RESULT1, new org.apache.thrift.meta_data.FieldMetaData("allianceResult1", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT        , "PsGVGMailAllianceResult")));
    tmpMap.put(_Fields.ALLIANCE_RESULT2, new org.apache.thrift.meta_data.FieldMetaData("allianceResult2", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT        , "PsGVGMailAllianceResult")));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsGVGMailResult.class, metaDataMap);
  }

  public PsGVGMailResult() {
  }

  public PsGVGMailResult(
    boolean result,
    PsGVGMailAllianceResult allianceResult1)
  {
    this();
    this.result = result;
    setResultIsSet(true);
    this.allianceResult1 = allianceResult1;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsGVGMailResult(PsGVGMailResult other) {
    __isset_bitfield = other.__isset_bitfield;
    this.result = other.result;
    if (other.isSetAllianceResult1()) {
      this.allianceResult1 = new PsGVGMailAllianceResult(other.allianceResult1);
    }
    if (other.isSetAllianceResult2()) {
      this.allianceResult2 = new PsGVGMailAllianceResult(other.allianceResult2);
    }
  }

  public PsGVGMailResult deepCopy() {
    return new PsGVGMailResult(this);
  }

  @Override
  public void clear() {
    setResultIsSet(false);
    this.result = false;
    this.allianceResult1 = null;
    this.allianceResult2 = null;
  }

  public boolean isResult() {
    return this.result;
  }

  public PsGVGMailResult setResult(boolean result) {
    this.result = result;
    setResultIsSet(true);
    return this;
  }

  public void unsetResult() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  /** Returns true if field result is set (has been assigned a value) and false otherwise */
  public boolean isSetResult() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  public void setResultIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RESULT_ISSET_ID, value);
  }

  @org.apache.thrift.annotation.Nullable
  public PsGVGMailAllianceResult getAllianceResult1() {
    return this.allianceResult1;
  }

  public PsGVGMailResult setAllianceResult1(@org.apache.thrift.annotation.Nullable PsGVGMailAllianceResult allianceResult1) {
    this.allianceResult1 = allianceResult1;
    return this;
  }

  public void unsetAllianceResult1() {
    this.allianceResult1 = null;
  }

  /** Returns true if field allianceResult1 is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceResult1() {
    return this.allianceResult1 != null;
  }

  public void setAllianceResult1IsSet(boolean value) {
    if (!value) {
      this.allianceResult1 = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public PsGVGMailAllianceResult getAllianceResult2() {
    return this.allianceResult2;
  }

  public PsGVGMailResult setAllianceResult2(@org.apache.thrift.annotation.Nullable PsGVGMailAllianceResult allianceResult2) {
    this.allianceResult2 = allianceResult2;
    return this;
  }

  public void unsetAllianceResult2() {
    this.allianceResult2 = null;
  }

  /** Returns true if field allianceResult2 is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceResult2() {
    return this.allianceResult2 != null;
  }

  public void setAllianceResult2IsSet(boolean value) {
    if (!value) {
      this.allianceResult2 = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case RESULT:
      if (value == null) {
        unsetResult();
      } else {
        setResult((java.lang.Boolean)value);
      }
      break;

    case ALLIANCE_RESULT1:
      if (value == null) {
        unsetAllianceResult1();
      } else {
        setAllianceResult1((PsGVGMailAllianceResult)value);
      }
      break;

    case ALLIANCE_RESULT2:
      if (value == null) {
        unsetAllianceResult2();
      } else {
        setAllianceResult2((PsGVGMailAllianceResult)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case RESULT:
      return isResult();

    case ALLIANCE_RESULT1:
      return getAllianceResult1();

    case ALLIANCE_RESULT2:
      return getAllianceResult2();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case RESULT:
      return isSetResult();
    case ALLIANCE_RESULT1:
      return isSetAllianceResult1();
    case ALLIANCE_RESULT2:
      return isSetAllianceResult2();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsGVGMailResult)
      return this.equals((PsGVGMailResult)that);
    return false;
  }

  public boolean equals(PsGVGMailResult that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_result = true;
    boolean that_present_result = true;
    if (this_present_result || that_present_result) {
      if (!(this_present_result && that_present_result))
        return false;
      if (this.result != that.result)
        return false;
    }

    boolean this_present_allianceResult1 = true && this.isSetAllianceResult1();
    boolean that_present_allianceResult1 = true && that.isSetAllianceResult1();
    if (this_present_allianceResult1 || that_present_allianceResult1) {
      if (!(this_present_allianceResult1 && that_present_allianceResult1))
        return false;
      if (!this.allianceResult1.equals(that.allianceResult1))
        return false;
    }

    boolean this_present_allianceResult2 = true && this.isSetAllianceResult2();
    boolean that_present_allianceResult2 = true && that.isSetAllianceResult2();
    if (this_present_allianceResult2 || that_present_allianceResult2) {
      if (!(this_present_allianceResult2 && that_present_allianceResult2))
        return false;
      if (!this.allianceResult2.equals(that.allianceResult2))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((result) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetAllianceResult1()) ? 131071 : 524287);
    if (isSetAllianceResult1())
      hashCode = hashCode * 8191 + allianceResult1.hashCode();

    hashCode = hashCode * 8191 + ((isSetAllianceResult2()) ? 131071 : 524287);
    if (isSetAllianceResult2())
      hashCode = hashCode * 8191 + allianceResult2.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(PsGVGMailResult other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetResult(), other.isSetResult());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResult()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.result, other.result);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAllianceResult1(), other.isSetAllianceResult1());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceResult1()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceResult1, other.allianceResult1);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAllianceResult2(), other.isSetAllianceResult2());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceResult2()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceResult2, other.allianceResult2);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsGVGMailResult(");
    boolean first = true;

    sb.append("result:");
    sb.append(this.result);
    first = false;
    if (!first) sb.append(", ");
    sb.append("allianceResult1:");
    if (this.allianceResult1 == null) {
      sb.append("null");
    } else {
      sb.append(this.allianceResult1);
    }
    first = false;
    if (isSetAllianceResult2()) {
      if (!first) sb.append(", ");
      sb.append("allianceResult2:");
      if (this.allianceResult2 == null) {
        sb.append("null");
      } else {
        sb.append(this.allianceResult2);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'result' because it's a primitive and you chose the non-beans generator.
    if (allianceResult1 == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'allianceResult1' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsGVGMailResultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsGVGMailResultStandardScheme getScheme() {
      return new PsGVGMailResultStandardScheme();
    }
  }

  private static class PsGVGMailResultStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsGVGMailResult> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsGVGMailResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RESULT
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.result = iprot.readBool();
              struct.setResultIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ALLIANCE_RESULT1
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.allianceResult1 = new PsGVGMailAllianceResult();
              struct.allianceResult1.read(iprot);
              struct.setAllianceResult1IsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ALLIANCE_RESULT2
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.allianceResult2 = new PsGVGMailAllianceResult();
              struct.allianceResult2.read(iprot);
              struct.setAllianceResult2IsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetResult()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'result' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsGVGMailResult struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RESULT_FIELD_DESC);
      oprot.writeBool(struct.result);
      oprot.writeFieldEnd();
      if (struct.allianceResult1 != null) {
        oprot.writeFieldBegin(ALLIANCE_RESULT1_FIELD_DESC);
        struct.allianceResult1.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.allianceResult2 != null) {
        if (struct.isSetAllianceResult2()) {
          oprot.writeFieldBegin(ALLIANCE_RESULT2_FIELD_DESC);
          struct.allianceResult2.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsGVGMailResultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsGVGMailResultTupleScheme getScheme() {
      return new PsGVGMailResultTupleScheme();
    }
  }

  private static class PsGVGMailResultTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsGVGMailResult> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsGVGMailResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeBool(struct.result);
      struct.allianceResult1.write(oprot);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetAllianceResult2()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetAllianceResult2()) {
        struct.allianceResult2.write(oprot);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsGVGMailResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.result = iprot.readBool();
      struct.setResultIsSet(true);
      struct.allianceResult1 = new PsGVGMailAllianceResult();
      struct.allianceResult1.read(iprot);
      struct.setAllianceResult1IsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.allianceResult2 = new PsGVGMailAllianceResult();
        struct.allianceResult2.read(iprot);
        struct.setAllianceResult2IsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

