/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 联盟排行榜邮件
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsAllianceRankEmail implements org.apache.thrift.TBase<PsAllianceRankEmail, PsAllianceRankEmail._Fields>, java.io.Serializable, Cloneable, Comparable<PsAllianceRankEmail> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsAllianceRankEmail");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("time", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("info", org.apache.thrift.protocol.TType.LIST, (short)4);
  private static final org.apache.thrift.protocol.TField SERVER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("serverId", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField REWARDS_FIELD_DESC = new org.apache.thrift.protocol.TField("rewards", org.apache.thrift.protocol.TType.LIST, (short)6);
  private static final org.apache.thrift.protocol.TField CONTENT_PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("contentParams", org.apache.thrift.protocol.TType.LIST, (short)7);
  private static final org.apache.thrift.protocol.TField CONTENT_FIELD_DESC = new org.apache.thrift.protocol.TField("content", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField BANNER_FIELD_DESC = new org.apache.thrift.protocol.TField("banner", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField MAPPER_FIELD_DESC = new org.apache.thrift.protocol.TField("mapper", org.apache.thrift.protocol.TType.MAP, (short)10);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsAllianceRankEmailStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsAllianceRankEmailTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.lang.String id; // required
  /**
   * 0: 未读
   * 1：已读
   * 2: 领取奖励
   */
  public int status; // required
  public long time; // required
  public @org.apache.thrift.annotation.Nullable java.util.List<PsRankAlliance> info; // optional
  public int serverId; // optional
  public @org.apache.thrift.annotation.Nullable java.util.List<PsSimpleItem> rewards; // optional
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> contentParams; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String content; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String banner; // optional
  /**
   * 掉落的物品映射，PsRankAlliance.rewardGroup 优先上此处查询
   */
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.Integer,java.util.List<PsSimpleItem>> mapper; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ID((short)1, "id"),
    /**
     * 0: 未读
     * 1：已读
     * 2: 领取奖励
     */
    STATUS((short)2, "status"),
    TIME((short)3, "time"),
    INFO((short)4, "info"),
    SERVER_ID((short)5, "serverId"),
    REWARDS((short)6, "rewards"),
    CONTENT_PARAMS((short)7, "contentParams"),
    CONTENT((short)8, "content"),
    BANNER((short)9, "banner"),
    /**
     * 掉落的物品映射，PsRankAlliance.rewardGroup 优先上此处查询
     */
    MAPPER((short)10, "mapper");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // STATUS
          return STATUS;
        case 3: // TIME
          return TIME;
        case 4: // INFO
          return INFO;
        case 5: // SERVER_ID
          return SERVER_ID;
        case 6: // REWARDS
          return REWARDS;
        case 7: // CONTENT_PARAMS
          return CONTENT_PARAMS;
        case 8: // CONTENT
          return CONTENT;
        case 9: // BANNER
          return BANNER;
        case 10: // MAPPER
          return MAPPER;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __STATUS_ISSET_ID = 0;
  private static final int __TIME_ISSET_ID = 1;
  private static final int __SERVERID_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.INFO,_Fields.SERVER_ID,_Fields.REWARDS,_Fields.CONTENT_PARAMS,_Fields.CONTENT,_Fields.BANNER,_Fields.MAPPER};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TIME, new org.apache.thrift.meta_data.FieldMetaData("time", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.INFO, new org.apache.thrift.meta_data.FieldMetaData("info", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT            , "PsRankAlliance"))));
    tmpMap.put(_Fields.SERVER_ID, new org.apache.thrift.meta_data.FieldMetaData("serverId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.REWARDS, new org.apache.thrift.meta_data.FieldMetaData("rewards", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsSimpleItem.class))));
    tmpMap.put(_Fields.CONTENT_PARAMS, new org.apache.thrift.meta_data.FieldMetaData("contentParams", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.CONTENT, new org.apache.thrift.meta_data.FieldMetaData("content", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BANNER, new org.apache.thrift.meta_data.FieldMetaData("banner", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MAPPER, new org.apache.thrift.meta_data.FieldMetaData("mapper", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32), 
            new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
                new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsSimpleItem.class)))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsAllianceRankEmail.class, metaDataMap);
  }

  public PsAllianceRankEmail() {
  }

  public PsAllianceRankEmail(
    java.lang.String id,
    int status,
    long time)
  {
    this();
    this.id = id;
    this.status = status;
    setStatusIsSet(true);
    this.time = time;
    setTimeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsAllianceRankEmail(PsAllianceRankEmail other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetId()) {
      this.id = other.id;
    }
    this.status = other.status;
    this.time = other.time;
    if (other.isSetInfo()) {
      java.util.List<PsRankAlliance> __this__info = new java.util.ArrayList<PsRankAlliance>(other.info.size());
      for (PsRankAlliance other_element : other.info) {
        __this__info.add(new PsRankAlliance(other_element));
      }
      this.info = __this__info;
    }
    this.serverId = other.serverId;
    if (other.isSetRewards()) {
      java.util.List<PsSimpleItem> __this__rewards = new java.util.ArrayList<PsSimpleItem>(other.rewards.size());
      for (PsSimpleItem other_element : other.rewards) {
        __this__rewards.add(new PsSimpleItem(other_element));
      }
      this.rewards = __this__rewards;
    }
    if (other.isSetContentParams()) {
      java.util.List<java.lang.String> __this__contentParams = new java.util.ArrayList<java.lang.String>(other.contentParams);
      this.contentParams = __this__contentParams;
    }
    if (other.isSetContent()) {
      this.content = other.content;
    }
    if (other.isSetBanner()) {
      this.banner = other.banner;
    }
    if (other.isSetMapper()) {
      java.util.Map<java.lang.Integer,java.util.List<PsSimpleItem>> __this__mapper = new java.util.HashMap<java.lang.Integer,java.util.List<PsSimpleItem>>(other.mapper.size());
      for (java.util.Map.Entry<java.lang.Integer, java.util.List<PsSimpleItem>> other_element : other.mapper.entrySet()) {

        java.lang.Integer other_element_key = other_element.getKey();
        java.util.List<PsSimpleItem> other_element_value = other_element.getValue();

        java.lang.Integer __this__mapper_copy_key = other_element_key;

        java.util.List<PsSimpleItem> __this__mapper_copy_value = new java.util.ArrayList<PsSimpleItem>(other_element_value.size());
        for (PsSimpleItem other_element_value_element : other_element_value) {
          __this__mapper_copy_value.add(new PsSimpleItem(other_element_value_element));
        }

        __this__mapper.put(__this__mapper_copy_key, __this__mapper_copy_value);
      }
      this.mapper = __this__mapper;
    }
  }

  public PsAllianceRankEmail deepCopy() {
    return new PsAllianceRankEmail(this);
  }

  @Override
  public void clear() {
    this.id = null;
    setStatusIsSet(false);
    this.status = 0;
    setTimeIsSet(false);
    this.time = 0;
    this.info = null;
    setServerIdIsSet(false);
    this.serverId = 0;
    this.rewards = null;
    this.contentParams = null;
    this.content = null;
    this.banner = null;
    this.mapper = null;
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getId() {
    return this.id;
  }

  public PsAllianceRankEmail setId(@org.apache.thrift.annotation.Nullable java.lang.String id) {
    this.id = id;
    return this;
  }

  public void unsetId() {
    this.id = null;
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return this.id != null;
  }

  public void setIdIsSet(boolean value) {
    if (!value) {
      this.id = null;
    }
  }

  /**
   * 0: 未读
   * 1：已读
   * 2: 领取奖励
   */
  public int getStatus() {
    return this.status;
  }

  /**
   * 0: 未读
   * 1：已读
   * 2: 领取奖励
   */
  public PsAllianceRankEmail setStatus(int status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  public long getTime() {
    return this.time;
  }

  public PsAllianceRankEmail setTime(long time) {
    this.time = time;
    setTimeIsSet(true);
    return this;
  }

  public void unsetTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TIME_ISSET_ID);
  }

  /** Returns true if field time is set (has been assigned a value) and false otherwise */
  public boolean isSetTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TIME_ISSET_ID);
  }

  public void setTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TIME_ISSET_ID, value);
  }

  public int getInfoSize() {
    return (this.info == null) ? 0 : this.info.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<PsRankAlliance> getInfoIterator() {
    return (this.info == null) ? null : this.info.iterator();
  }

  public void addToInfo(PsRankAlliance elem) {
    if (this.info == null) {
      this.info = new java.util.ArrayList<PsRankAlliance>();
    }
    this.info.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<PsRankAlliance> getInfo() {
    return this.info;
  }

  public PsAllianceRankEmail setInfo(@org.apache.thrift.annotation.Nullable java.util.List<PsRankAlliance> info) {
    this.info = info;
    return this;
  }

  public void unsetInfo() {
    this.info = null;
  }

  /** Returns true if field info is set (has been assigned a value) and false otherwise */
  public boolean isSetInfo() {
    return this.info != null;
  }

  public void setInfoIsSet(boolean value) {
    if (!value) {
      this.info = null;
    }
  }

  public int getServerId() {
    return this.serverId;
  }

  public PsAllianceRankEmail setServerId(int serverId) {
    this.serverId = serverId;
    setServerIdIsSet(true);
    return this;
  }

  public void unsetServerId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SERVERID_ISSET_ID);
  }

  /** Returns true if field serverId is set (has been assigned a value) and false otherwise */
  public boolean isSetServerId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SERVERID_ISSET_ID);
  }

  public void setServerIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SERVERID_ISSET_ID, value);
  }

  public int getRewardsSize() {
    return (this.rewards == null) ? 0 : this.rewards.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<PsSimpleItem> getRewardsIterator() {
    return (this.rewards == null) ? null : this.rewards.iterator();
  }

  public void addToRewards(PsSimpleItem elem) {
    if (this.rewards == null) {
      this.rewards = new java.util.ArrayList<PsSimpleItem>();
    }
    this.rewards.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<PsSimpleItem> getRewards() {
    return this.rewards;
  }

  public PsAllianceRankEmail setRewards(@org.apache.thrift.annotation.Nullable java.util.List<PsSimpleItem> rewards) {
    this.rewards = rewards;
    return this;
  }

  public void unsetRewards() {
    this.rewards = null;
  }

  /** Returns true if field rewards is set (has been assigned a value) and false otherwise */
  public boolean isSetRewards() {
    return this.rewards != null;
  }

  public void setRewardsIsSet(boolean value) {
    if (!value) {
      this.rewards = null;
    }
  }

  public int getContentParamsSize() {
    return (this.contentParams == null) ? 0 : this.contentParams.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.String> getContentParamsIterator() {
    return (this.contentParams == null) ? null : this.contentParams.iterator();
  }

  public void addToContentParams(java.lang.String elem) {
    if (this.contentParams == null) {
      this.contentParams = new java.util.ArrayList<java.lang.String>();
    }
    this.contentParams.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.String> getContentParams() {
    return this.contentParams;
  }

  public PsAllianceRankEmail setContentParams(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> contentParams) {
    this.contentParams = contentParams;
    return this;
  }

  public void unsetContentParams() {
    this.contentParams = null;
  }

  /** Returns true if field contentParams is set (has been assigned a value) and false otherwise */
  public boolean isSetContentParams() {
    return this.contentParams != null;
  }

  public void setContentParamsIsSet(boolean value) {
    if (!value) {
      this.contentParams = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getContent() {
    return this.content;
  }

  public PsAllianceRankEmail setContent(@org.apache.thrift.annotation.Nullable java.lang.String content) {
    this.content = content;
    return this;
  }

  public void unsetContent() {
    this.content = null;
  }

  /** Returns true if field content is set (has been assigned a value) and false otherwise */
  public boolean isSetContent() {
    return this.content != null;
  }

  public void setContentIsSet(boolean value) {
    if (!value) {
      this.content = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getBanner() {
    return this.banner;
  }

  public PsAllianceRankEmail setBanner(@org.apache.thrift.annotation.Nullable java.lang.String banner) {
    this.banner = banner;
    return this;
  }

  public void unsetBanner() {
    this.banner = null;
  }

  /** Returns true if field banner is set (has been assigned a value) and false otherwise */
  public boolean isSetBanner() {
    return this.banner != null;
  }

  public void setBannerIsSet(boolean value) {
    if (!value) {
      this.banner = null;
    }
  }

  public int getMapperSize() {
    return (this.mapper == null) ? 0 : this.mapper.size();
  }

  public void putToMapper(int key, java.util.List<PsSimpleItem> val) {
    if (this.mapper == null) {
      this.mapper = new java.util.HashMap<java.lang.Integer,java.util.List<PsSimpleItem>>();
    }
    this.mapper.put(key, val);
  }

  /**
   * 掉落的物品映射，PsRankAlliance.rewardGroup 优先上此处查询
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.Integer,java.util.List<PsSimpleItem>> getMapper() {
    return this.mapper;
  }

  /**
   * 掉落的物品映射，PsRankAlliance.rewardGroup 优先上此处查询
   */
  public PsAllianceRankEmail setMapper(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.Integer,java.util.List<PsSimpleItem>> mapper) {
    this.mapper = mapper;
    return this;
  }

  public void unsetMapper() {
    this.mapper = null;
  }

  /** Returns true if field mapper is set (has been assigned a value) and false otherwise */
  public boolean isSetMapper() {
    return this.mapper != null;
  }

  public void setMapperIsSet(boolean value) {
    if (!value) {
      this.mapper = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((java.lang.String)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((java.lang.Integer)value);
      }
      break;

    case TIME:
      if (value == null) {
        unsetTime();
      } else {
        setTime((java.lang.Long)value);
      }
      break;

    case INFO:
      if (value == null) {
        unsetInfo();
      } else {
        setInfo((java.util.List<PsRankAlliance>)value);
      }
      break;

    case SERVER_ID:
      if (value == null) {
        unsetServerId();
      } else {
        setServerId((java.lang.Integer)value);
      }
      break;

    case REWARDS:
      if (value == null) {
        unsetRewards();
      } else {
        setRewards((java.util.List<PsSimpleItem>)value);
      }
      break;

    case CONTENT_PARAMS:
      if (value == null) {
        unsetContentParams();
      } else {
        setContentParams((java.util.List<java.lang.String>)value);
      }
      break;

    case CONTENT:
      if (value == null) {
        unsetContent();
      } else {
        setContent((java.lang.String)value);
      }
      break;

    case BANNER:
      if (value == null) {
        unsetBanner();
      } else {
        setBanner((java.lang.String)value);
      }
      break;

    case MAPPER:
      if (value == null) {
        unsetMapper();
      } else {
        setMapper((java.util.Map<java.lang.Integer,java.util.List<PsSimpleItem>>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case STATUS:
      return getStatus();

    case TIME:
      return getTime();

    case INFO:
      return getInfo();

    case SERVER_ID:
      return getServerId();

    case REWARDS:
      return getRewards();

    case CONTENT_PARAMS:
      return getContentParams();

    case CONTENT:
      return getContent();

    case BANNER:
      return getBanner();

    case MAPPER:
      return getMapper();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case STATUS:
      return isSetStatus();
    case TIME:
      return isSetTime();
    case INFO:
      return isSetInfo();
    case SERVER_ID:
      return isSetServerId();
    case REWARDS:
      return isSetRewards();
    case CONTENT_PARAMS:
      return isSetContentParams();
    case CONTENT:
      return isSetContent();
    case BANNER:
      return isSetBanner();
    case MAPPER:
      return isSetMapper();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsAllianceRankEmail)
      return this.equals((PsAllianceRankEmail)that);
    return false;
  }

  public boolean equals(PsAllianceRankEmail that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_id = true && this.isSetId();
    boolean that_present_id = true && that.isSetId();
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (!this.id.equals(that.id))
        return false;
    }

    boolean this_present_status = true;
    boolean that_present_status = true;
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_time = true;
    boolean that_present_time = true;
    if (this_present_time || that_present_time) {
      if (!(this_present_time && that_present_time))
        return false;
      if (this.time != that.time)
        return false;
    }

    boolean this_present_info = true && this.isSetInfo();
    boolean that_present_info = true && that.isSetInfo();
    if (this_present_info || that_present_info) {
      if (!(this_present_info && that_present_info))
        return false;
      if (!this.info.equals(that.info))
        return false;
    }

    boolean this_present_serverId = true && this.isSetServerId();
    boolean that_present_serverId = true && that.isSetServerId();
    if (this_present_serverId || that_present_serverId) {
      if (!(this_present_serverId && that_present_serverId))
        return false;
      if (this.serverId != that.serverId)
        return false;
    }

    boolean this_present_rewards = true && this.isSetRewards();
    boolean that_present_rewards = true && that.isSetRewards();
    if (this_present_rewards || that_present_rewards) {
      if (!(this_present_rewards && that_present_rewards))
        return false;
      if (!this.rewards.equals(that.rewards))
        return false;
    }

    boolean this_present_contentParams = true && this.isSetContentParams();
    boolean that_present_contentParams = true && that.isSetContentParams();
    if (this_present_contentParams || that_present_contentParams) {
      if (!(this_present_contentParams && that_present_contentParams))
        return false;
      if (!this.contentParams.equals(that.contentParams))
        return false;
    }

    boolean this_present_content = true && this.isSetContent();
    boolean that_present_content = true && that.isSetContent();
    if (this_present_content || that_present_content) {
      if (!(this_present_content && that_present_content))
        return false;
      if (!this.content.equals(that.content))
        return false;
    }

    boolean this_present_banner = true && this.isSetBanner();
    boolean that_present_banner = true && that.isSetBanner();
    if (this_present_banner || that_present_banner) {
      if (!(this_present_banner && that_present_banner))
        return false;
      if (!this.banner.equals(that.banner))
        return false;
    }

    boolean this_present_mapper = true && this.isSetMapper();
    boolean that_present_mapper = true && that.isSetMapper();
    if (this_present_mapper || that_present_mapper) {
      if (!(this_present_mapper && that_present_mapper))
        return false;
      if (!this.mapper.equals(that.mapper))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetId()) ? 131071 : 524287);
    if (isSetId())
      hashCode = hashCode * 8191 + id.hashCode();

    hashCode = hashCode * 8191 + status;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(time);

    hashCode = hashCode * 8191 + ((isSetInfo()) ? 131071 : 524287);
    if (isSetInfo())
      hashCode = hashCode * 8191 + info.hashCode();

    hashCode = hashCode * 8191 + ((isSetServerId()) ? 131071 : 524287);
    if (isSetServerId())
      hashCode = hashCode * 8191 + serverId;

    hashCode = hashCode * 8191 + ((isSetRewards()) ? 131071 : 524287);
    if (isSetRewards())
      hashCode = hashCode * 8191 + rewards.hashCode();

    hashCode = hashCode * 8191 + ((isSetContentParams()) ? 131071 : 524287);
    if (isSetContentParams())
      hashCode = hashCode * 8191 + contentParams.hashCode();

    hashCode = hashCode * 8191 + ((isSetContent()) ? 131071 : 524287);
    if (isSetContent())
      hashCode = hashCode * 8191 + content.hashCode();

    hashCode = hashCode * 8191 + ((isSetBanner()) ? 131071 : 524287);
    if (isSetBanner())
      hashCode = hashCode * 8191 + banner.hashCode();

    hashCode = hashCode * 8191 + ((isSetMapper()) ? 131071 : 524287);
    if (isSetMapper())
      hashCode = hashCode * 8191 + mapper.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(PsAllianceRankEmail other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetId(), other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStatus(), other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTime(), other.isSetTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.time, other.time);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetInfo(), other.isSetInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.info, other.info);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetServerId(), other.isSetServerId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetServerId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.serverId, other.serverId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRewards(), other.isSetRewards());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRewards()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rewards, other.rewards);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetContentParams(), other.isSetContentParams());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetContentParams()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.contentParams, other.contentParams);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetContent(), other.isSetContent());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetContent()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.content, other.content);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBanner(), other.isSetBanner());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBanner()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.banner, other.banner);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMapper(), other.isSetMapper());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMapper()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mapper, other.mapper);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsAllianceRankEmail(");
    boolean first = true;

    sb.append("id:");
    if (this.id == null) {
      sb.append("null");
    } else {
      sb.append(this.id);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("status:");
    sb.append(this.status);
    first = false;
    if (!first) sb.append(", ");
    sb.append("time:");
    sb.append(this.time);
    first = false;
    if (isSetInfo()) {
      if (!first) sb.append(", ");
      sb.append("info:");
      if (this.info == null) {
        sb.append("null");
      } else {
        sb.append(this.info);
      }
      first = false;
    }
    if (isSetServerId()) {
      if (!first) sb.append(", ");
      sb.append("serverId:");
      sb.append(this.serverId);
      first = false;
    }
    if (isSetRewards()) {
      if (!first) sb.append(", ");
      sb.append("rewards:");
      if (this.rewards == null) {
        sb.append("null");
      } else {
        sb.append(this.rewards);
      }
      first = false;
    }
    if (isSetContentParams()) {
      if (!first) sb.append(", ");
      sb.append("contentParams:");
      if (this.contentParams == null) {
        sb.append("null");
      } else {
        sb.append(this.contentParams);
      }
      first = false;
    }
    if (isSetContent()) {
      if (!first) sb.append(", ");
      sb.append("content:");
      if (this.content == null) {
        sb.append("null");
      } else {
        sb.append(this.content);
      }
      first = false;
    }
    if (isSetBanner()) {
      if (!first) sb.append(", ");
      sb.append("banner:");
      if (this.banner == null) {
        sb.append("null");
      } else {
        sb.append(this.banner);
      }
      first = false;
    }
    if (isSetMapper()) {
      if (!first) sb.append(", ");
      sb.append("mapper:");
      if (this.mapper == null) {
        sb.append("null");
      } else {
        sb.append(this.mapper);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (id == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'id' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'status' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'time' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsAllianceRankEmailStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsAllianceRankEmailStandardScheme getScheme() {
      return new PsAllianceRankEmailStandardScheme();
    }
  }

  private static class PsAllianceRankEmailStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsAllianceRankEmail> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsAllianceRankEmail struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.id = iprot.readString();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.status = iprot.readI32();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.time = iprot.readI64();
              struct.setTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.info = new java.util.ArrayList<PsRankAlliance>(_list0.size);
                @org.apache.thrift.annotation.Nullable PsRankAlliance _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new PsRankAlliance();
                  _elem1.read(iprot);
                  struct.info.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SERVER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.serverId = iprot.readI32();
              struct.setServerIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // REWARDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.rewards = new java.util.ArrayList<PsSimpleItem>(_list3.size);
                @org.apache.thrift.annotation.Nullable PsSimpleItem _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = new PsSimpleItem();
                  _elem4.read(iprot);
                  struct.rewards.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setRewardsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // CONTENT_PARAMS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list6 = iprot.readListBegin();
                struct.contentParams = new java.util.ArrayList<java.lang.String>(_list6.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _elem7;
                for (int _i8 = 0; _i8 < _list6.size; ++_i8)
                {
                  _elem7 = iprot.readString();
                  struct.contentParams.add(_elem7);
                }
                iprot.readListEnd();
              }
              struct.setContentParamsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // CONTENT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.content = iprot.readString();
              struct.setContentIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // BANNER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.banner = iprot.readString();
              struct.setBannerIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // MAPPER
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map9 = iprot.readMapBegin();
                struct.mapper = new java.util.HashMap<java.lang.Integer,java.util.List<PsSimpleItem>>(2*_map9.size);
                int _key10;
                @org.apache.thrift.annotation.Nullable java.util.List<PsSimpleItem> _val11;
                for (int _i12 = 0; _i12 < _map9.size; ++_i12)
                {
                  _key10 = iprot.readI32();
                  {
                    org.apache.thrift.protocol.TList _list13 = iprot.readListBegin();
                    _val11 = new java.util.ArrayList<PsSimpleItem>(_list13.size);
                    @org.apache.thrift.annotation.Nullable PsSimpleItem _elem14;
                    for (int _i15 = 0; _i15 < _list13.size; ++_i15)
                    {
                      _elem14 = new PsSimpleItem();
                      _elem14.read(iprot);
                      _val11.add(_elem14);
                    }
                    iprot.readListEnd();
                  }
                  struct.mapper.put(_key10, _val11);
                }
                iprot.readMapEnd();
              }
              struct.setMapperIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetStatus()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'status' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'time' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsAllianceRankEmail struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.id != null) {
        oprot.writeFieldBegin(ID_FIELD_DESC);
        oprot.writeString(struct.id);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(STATUS_FIELD_DESC);
      oprot.writeI32(struct.status);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TIME_FIELD_DESC);
      oprot.writeI64(struct.time);
      oprot.writeFieldEnd();
      if (struct.info != null) {
        if (struct.isSetInfo()) {
          oprot.writeFieldBegin(INFO_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.info.size()));
            for (PsRankAlliance _iter16 : struct.info)
            {
              _iter16.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetServerId()) {
        oprot.writeFieldBegin(SERVER_ID_FIELD_DESC);
        oprot.writeI32(struct.serverId);
        oprot.writeFieldEnd();
      }
      if (struct.rewards != null) {
        if (struct.isSetRewards()) {
          oprot.writeFieldBegin(REWARDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.rewards.size()));
            for (PsSimpleItem _iter17 : struct.rewards)
            {
              _iter17.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.contentParams != null) {
        if (struct.isSetContentParams()) {
          oprot.writeFieldBegin(CONTENT_PARAMS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.contentParams.size()));
            for (java.lang.String _iter18 : struct.contentParams)
            {
              oprot.writeString(_iter18);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.content != null) {
        if (struct.isSetContent()) {
          oprot.writeFieldBegin(CONTENT_FIELD_DESC);
          oprot.writeString(struct.content);
          oprot.writeFieldEnd();
        }
      }
      if (struct.banner != null) {
        if (struct.isSetBanner()) {
          oprot.writeFieldBegin(BANNER_FIELD_DESC);
          oprot.writeString(struct.banner);
          oprot.writeFieldEnd();
        }
      }
      if (struct.mapper != null) {
        if (struct.isSetMapper()) {
          oprot.writeFieldBegin(MAPPER_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.LIST, struct.mapper.size()));
            for (java.util.Map.Entry<java.lang.Integer, java.util.List<PsSimpleItem>> _iter19 : struct.mapper.entrySet())
            {
              oprot.writeI32(_iter19.getKey());
              {
                oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, _iter19.getValue().size()));
                for (PsSimpleItem _iter20 : _iter19.getValue())
                {
                  _iter20.write(oprot);
                }
                oprot.writeListEnd();
              }
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsAllianceRankEmailTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsAllianceRankEmailTupleScheme getScheme() {
      return new PsAllianceRankEmailTupleScheme();
    }
  }

  private static class PsAllianceRankEmailTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsAllianceRankEmail> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsAllianceRankEmail struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.id);
      oprot.writeI32(struct.status);
      oprot.writeI64(struct.time);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetInfo()) {
        optionals.set(0);
      }
      if (struct.isSetServerId()) {
        optionals.set(1);
      }
      if (struct.isSetRewards()) {
        optionals.set(2);
      }
      if (struct.isSetContentParams()) {
        optionals.set(3);
      }
      if (struct.isSetContent()) {
        optionals.set(4);
      }
      if (struct.isSetBanner()) {
        optionals.set(5);
      }
      if (struct.isSetMapper()) {
        optionals.set(6);
      }
      oprot.writeBitSet(optionals, 7);
      if (struct.isSetInfo()) {
        {
          oprot.writeI32(struct.info.size());
          for (PsRankAlliance _iter21 : struct.info)
          {
            _iter21.write(oprot);
          }
        }
      }
      if (struct.isSetServerId()) {
        oprot.writeI32(struct.serverId);
      }
      if (struct.isSetRewards()) {
        {
          oprot.writeI32(struct.rewards.size());
          for (PsSimpleItem _iter22 : struct.rewards)
          {
            _iter22.write(oprot);
          }
        }
      }
      if (struct.isSetContentParams()) {
        {
          oprot.writeI32(struct.contentParams.size());
          for (java.lang.String _iter23 : struct.contentParams)
          {
            oprot.writeString(_iter23);
          }
        }
      }
      if (struct.isSetContent()) {
        oprot.writeString(struct.content);
      }
      if (struct.isSetBanner()) {
        oprot.writeString(struct.banner);
      }
      if (struct.isSetMapper()) {
        {
          oprot.writeI32(struct.mapper.size());
          for (java.util.Map.Entry<java.lang.Integer, java.util.List<PsSimpleItem>> _iter24 : struct.mapper.entrySet())
          {
            oprot.writeI32(_iter24.getKey());
            {
              oprot.writeI32(_iter24.getValue().size());
              for (PsSimpleItem _iter25 : _iter24.getValue())
              {
                _iter25.write(oprot);
              }
            }
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsAllianceRankEmail struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.id = iprot.readString();
      struct.setIdIsSet(true);
      struct.status = iprot.readI32();
      struct.setStatusIsSet(true);
      struct.time = iprot.readI64();
      struct.setTimeIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(7);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list26 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.info = new java.util.ArrayList<PsRankAlliance>(_list26.size);
          @org.apache.thrift.annotation.Nullable PsRankAlliance _elem27;
          for (int _i28 = 0; _i28 < _list26.size; ++_i28)
          {
            _elem27 = new PsRankAlliance();
            _elem27.read(iprot);
            struct.info.add(_elem27);
          }
        }
        struct.setInfoIsSet(true);
      }
      if (incoming.get(1)) {
        struct.serverId = iprot.readI32();
        struct.setServerIdIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TList _list29 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.rewards = new java.util.ArrayList<PsSimpleItem>(_list29.size);
          @org.apache.thrift.annotation.Nullable PsSimpleItem _elem30;
          for (int _i31 = 0; _i31 < _list29.size; ++_i31)
          {
            _elem30 = new PsSimpleItem();
            _elem30.read(iprot);
            struct.rewards.add(_elem30);
          }
        }
        struct.setRewardsIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TList _list32 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRING);
          struct.contentParams = new java.util.ArrayList<java.lang.String>(_list32.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _elem33;
          for (int _i34 = 0; _i34 < _list32.size; ++_i34)
          {
            _elem33 = iprot.readString();
            struct.contentParams.add(_elem33);
          }
        }
        struct.setContentParamsIsSet(true);
      }
      if (incoming.get(4)) {
        struct.content = iprot.readString();
        struct.setContentIsSet(true);
      }
      if (incoming.get(5)) {
        struct.banner = iprot.readString();
        struct.setBannerIsSet(true);
      }
      if (incoming.get(6)) {
        {
          org.apache.thrift.protocol.TMap _map35 = iprot.readMapBegin(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.LIST); 
          struct.mapper = new java.util.HashMap<java.lang.Integer,java.util.List<PsSimpleItem>>(2*_map35.size);
          int _key36;
          @org.apache.thrift.annotation.Nullable java.util.List<PsSimpleItem> _val37;
          for (int _i38 = 0; _i38 < _map35.size; ++_i38)
          {
            _key36 = iprot.readI32();
            {
              org.apache.thrift.protocol.TList _list39 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
              _val37 = new java.util.ArrayList<PsSimpleItem>(_list39.size);
              @org.apache.thrift.annotation.Nullable PsSimpleItem _elem40;
              for (int _i41 = 0; _i41 < _list39.size; ++_i41)
              {
                _elem40 = new PsSimpleItem();
                _elem40.read(iprot);
                _val37.add(_elem40);
              }
            }
            struct.mapper.put(_key36, _val37);
          }
        }
        struct.setMapperIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

