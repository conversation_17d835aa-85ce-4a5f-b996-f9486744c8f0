/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 指挥官详情
 * @Message(3209)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcCommanderInfo implements org.apache.thrift.TBase<GcCommanderInfo, GcCommanderInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcCommanderInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcCommanderInfo");

  private static final org.apache.thrift.protocol.TField BATTLE_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("battleNum", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField BATTLE_WIN_FIELD_DESC = new org.apache.thrift.protocol.TField("battleWin", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField BATTLE_KILL_PVP_ENEMY_FIELD_DESC = new org.apache.thrift.protocol.TField("battleKillPvpEnemy", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField BATTLE_DEAD_ENEMY_FIELD_DESC = new org.apache.thrift.protocol.TField("battleDeadEnemy", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField CURE_SOLDIER_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("cureSoldierNum", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField BATTLE_KILL_PVE_ENEMY_FIELD_DESC = new org.apache.thrift.protocol.TField("battleKillPveEnemy", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField SCOUT_TIMES_FIELD_DESC = new org.apache.thrift.protocol.TField("scoutTimes", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField RESOURCE_GATHERED_FIELD_DESC = new org.apache.thrift.protocol.TField("resourceGathered", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField RESOURCE_ASSISTANCE_FIELD_DESC = new org.apache.thrift.protocol.TField("resourceAssistance", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField ALLIANCE_HELP_TIMES_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceHelpTimes", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField PVP_WIN_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("pvpWinCount", org.apache.thrift.protocol.TType.I64, (short)11);
  private static final org.apache.thrift.protocol.TField PVP_FALSE_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("pvpFalseCount", org.apache.thrift.protocol.TType.I64, (short)12);
  private static final org.apache.thrift.protocol.TField PVE_WIN_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("pveWinCount", org.apache.thrift.protocol.TType.I64, (short)13);
  private static final org.apache.thrift.protocol.TField PVE_FALSE_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("pveFalseCount", org.apache.thrift.protocol.TType.I64, (short)14);
  private static final org.apache.thrift.protocol.TField DESPOIL_AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("despoilAmount", org.apache.thrift.protocol.TType.I64, (short)15);
  private static final org.apache.thrift.protocol.TField RES_HELP_AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("resHelpAmount", org.apache.thrift.protocol.TType.I64, (short)16);
  private static final org.apache.thrift.protocol.TField RES_HELP_RECEIVED_AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("resHelpReceivedAmount", org.apache.thrift.protocol.TType.I64, (short)17);
  private static final org.apache.thrift.protocol.TField PVP_OFFENSE_WIN_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("pvpOffenseWinCount", org.apache.thrift.protocol.TType.I64, (short)19);
  private static final org.apache.thrift.protocol.TField PVP_OFFENSE_LOSE_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("pvpOffenseLoseCount", org.apache.thrift.protocol.TType.I64, (short)20);
  private static final org.apache.thrift.protocol.TField PVP_DEFENSE_WIN_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("pvpDefenseWinCount", org.apache.thrift.protocol.TType.I64, (short)21);
  private static final org.apache.thrift.protocol.TField PVP_DEFENSE_LOSE_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("pvpDefenseLoseCount", org.apache.thrift.protocol.TType.I64, (short)22);
  private static final org.apache.thrift.protocol.TField PVP_WIN_RATIO_FIELD_DESC = new org.apache.thrift.protocol.TField("pvpWinRatio", org.apache.thrift.protocol.TType.DOUBLE, (short)23);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcCommanderInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcCommanderInfoTupleSchemeFactory();

  /**
   * 战斗总次数：发生战斗次数总和（包含PVE+PVP)
   */
  public long battleNum; // optional
  /**
   * 战斗胜利次数：战斗胜利次数总和（包含PVE+PVP）
   */
  public long battleWin; // optional
  /**
   * 击败部队数量：对方死兵和伤兵都算
   */
  public long battleKillPvpEnemy; // optional
  /**
   * 部队阵亡数量：自己阵亡士兵数量
   */
  public long battleDeadEnemy; // optional
  /**
   * 部队治疗数量：医疗帐篷治疗的士兵数量
   */
  public long cureSoldierNum; // optional
  /**
   * 击败生化人数量：PVE击败野怪的次数。（大地图营地、各种可攻击的单位都算野怪）
   */
  public long battleKillPveEnemy; // optional
  /**
   * 侦查次数
   */
  public long scoutTimes; // required
  /**
   * 资源采集
   */
  public long resourceGathered; // required
  /**
   * 资源援助
   */
  public long resourceAssistance; // required
  /**
   * 联盟帮助
   */
  public long allianceHelpTimes; // required
  /**
   * PVP战斗胜利总次数
   */
  public long pvpWinCount; // optional
  /**
   * PVP战斗失败总次数
   */
  public long pvpFalseCount; // optional
  /**
   * PVE战斗胜利总次数
   */
  public long pveWinCount; // optional
  /**
   * PVE战斗失败总次数
   */
  public long pveFalseCount; // optional
  /**
   * 掠夺资源总数
   */
  public long despoilAmount; // optional
  /**
   * 援助资源总数
   */
  public long resHelpAmount; // optional
  /**
   * 被援助资源总数
   */
  public long resHelpReceivedAmount; // optional
  /**
   * pvp 进攻胜利次数
   */
  public long pvpOffenseWinCount; // optional
  /**
   * pvp 进攻失败次数
   */
  public long pvpOffenseLoseCount; // optional
  /**
   * pvp 防守胜利
   */
  public long pvpDefenseWinCount; // optional
  /**
   * pvp 防守失败 次数
   */
  public long pvpDefenseLoseCount; // optional
  /**
   * pvp 战斗胜率 pvpWinCount / (pvpFalseCount + pvpWinCount)
   */
  public double pvpWinRatio; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 战斗总次数：发生战斗次数总和（包含PVE+PVP)
     */
    BATTLE_NUM((short)1, "battleNum"),
    /**
     * 战斗胜利次数：战斗胜利次数总和（包含PVE+PVP）
     */
    BATTLE_WIN((short)2, "battleWin"),
    /**
     * 击败部队数量：对方死兵和伤兵都算
     */
    BATTLE_KILL_PVP_ENEMY((short)3, "battleKillPvpEnemy"),
    /**
     * 部队阵亡数量：自己阵亡士兵数量
     */
    BATTLE_DEAD_ENEMY((short)4, "battleDeadEnemy"),
    /**
     * 部队治疗数量：医疗帐篷治疗的士兵数量
     */
    CURE_SOLDIER_NUM((short)5, "cureSoldierNum"),
    /**
     * 击败生化人数量：PVE击败野怪的次数。（大地图营地、各种可攻击的单位都算野怪）
     */
    BATTLE_KILL_PVE_ENEMY((short)6, "battleKillPveEnemy"),
    /**
     * 侦查次数
     */
    SCOUT_TIMES((short)7, "scoutTimes"),
    /**
     * 资源采集
     */
    RESOURCE_GATHERED((short)8, "resourceGathered"),
    /**
     * 资源援助
     */
    RESOURCE_ASSISTANCE((short)9, "resourceAssistance"),
    /**
     * 联盟帮助
     */
    ALLIANCE_HELP_TIMES((short)10, "allianceHelpTimes"),
    /**
     * PVP战斗胜利总次数
     */
    PVP_WIN_COUNT((short)11, "pvpWinCount"),
    /**
     * PVP战斗失败总次数
     */
    PVP_FALSE_COUNT((short)12, "pvpFalseCount"),
    /**
     * PVE战斗胜利总次数
     */
    PVE_WIN_COUNT((short)13, "pveWinCount"),
    /**
     * PVE战斗失败总次数
     */
    PVE_FALSE_COUNT((short)14, "pveFalseCount"),
    /**
     * 掠夺资源总数
     */
    DESPOIL_AMOUNT((short)15, "despoilAmount"),
    /**
     * 援助资源总数
     */
    RES_HELP_AMOUNT((short)16, "resHelpAmount"),
    /**
     * 被援助资源总数
     */
    RES_HELP_RECEIVED_AMOUNT((short)17, "resHelpReceivedAmount"),
    /**
     * pvp 进攻胜利次数
     */
    PVP_OFFENSE_WIN_COUNT((short)19, "pvpOffenseWinCount"),
    /**
     * pvp 进攻失败次数
     */
    PVP_OFFENSE_LOSE_COUNT((short)20, "pvpOffenseLoseCount"),
    /**
     * pvp 防守胜利
     */
    PVP_DEFENSE_WIN_COUNT((short)21, "pvpDefenseWinCount"),
    /**
     * pvp 防守失败 次数
     */
    PVP_DEFENSE_LOSE_COUNT((short)22, "pvpDefenseLoseCount"),
    /**
     * pvp 战斗胜率 pvpWinCount / (pvpFalseCount + pvpWinCount)
     */
    PVP_WIN_RATIO((short)23, "pvpWinRatio");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BATTLE_NUM
          return BATTLE_NUM;
        case 2: // BATTLE_WIN
          return BATTLE_WIN;
        case 3: // BATTLE_KILL_PVP_ENEMY
          return BATTLE_KILL_PVP_ENEMY;
        case 4: // BATTLE_DEAD_ENEMY
          return BATTLE_DEAD_ENEMY;
        case 5: // CURE_SOLDIER_NUM
          return CURE_SOLDIER_NUM;
        case 6: // BATTLE_KILL_PVE_ENEMY
          return BATTLE_KILL_PVE_ENEMY;
        case 7: // SCOUT_TIMES
          return SCOUT_TIMES;
        case 8: // RESOURCE_GATHERED
          return RESOURCE_GATHERED;
        case 9: // RESOURCE_ASSISTANCE
          return RESOURCE_ASSISTANCE;
        case 10: // ALLIANCE_HELP_TIMES
          return ALLIANCE_HELP_TIMES;
        case 11: // PVP_WIN_COUNT
          return PVP_WIN_COUNT;
        case 12: // PVP_FALSE_COUNT
          return PVP_FALSE_COUNT;
        case 13: // PVE_WIN_COUNT
          return PVE_WIN_COUNT;
        case 14: // PVE_FALSE_COUNT
          return PVE_FALSE_COUNT;
        case 15: // DESPOIL_AMOUNT
          return DESPOIL_AMOUNT;
        case 16: // RES_HELP_AMOUNT
          return RES_HELP_AMOUNT;
        case 17: // RES_HELP_RECEIVED_AMOUNT
          return RES_HELP_RECEIVED_AMOUNT;
        case 19: // PVP_OFFENSE_WIN_COUNT
          return PVP_OFFENSE_WIN_COUNT;
        case 20: // PVP_OFFENSE_LOSE_COUNT
          return PVP_OFFENSE_LOSE_COUNT;
        case 21: // PVP_DEFENSE_WIN_COUNT
          return PVP_DEFENSE_WIN_COUNT;
        case 22: // PVP_DEFENSE_LOSE_COUNT
          return PVP_DEFENSE_LOSE_COUNT;
        case 23: // PVP_WIN_RATIO
          return PVP_WIN_RATIO;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BATTLENUM_ISSET_ID = 0;
  private static final int __BATTLEWIN_ISSET_ID = 1;
  private static final int __BATTLEKILLPVPENEMY_ISSET_ID = 2;
  private static final int __BATTLEDEADENEMY_ISSET_ID = 3;
  private static final int __CURESOLDIERNUM_ISSET_ID = 4;
  private static final int __BATTLEKILLPVEENEMY_ISSET_ID = 5;
  private static final int __SCOUTTIMES_ISSET_ID = 6;
  private static final int __RESOURCEGATHERED_ISSET_ID = 7;
  private static final int __RESOURCEASSISTANCE_ISSET_ID = 8;
  private static final int __ALLIANCEHELPTIMES_ISSET_ID = 9;
  private static final int __PVPWINCOUNT_ISSET_ID = 10;
  private static final int __PVPFALSECOUNT_ISSET_ID = 11;
  private static final int __PVEWINCOUNT_ISSET_ID = 12;
  private static final int __PVEFALSECOUNT_ISSET_ID = 13;
  private static final int __DESPOILAMOUNT_ISSET_ID = 14;
  private static final int __RESHELPAMOUNT_ISSET_ID = 15;
  private static final int __RESHELPRECEIVEDAMOUNT_ISSET_ID = 16;
  private static final int __PVPOFFENSEWINCOUNT_ISSET_ID = 17;
  private static final int __PVPOFFENSELOSECOUNT_ISSET_ID = 18;
  private static final int __PVPDEFENSEWINCOUNT_ISSET_ID = 19;
  private static final int __PVPDEFENSELOSECOUNT_ISSET_ID = 20;
  private static final int __PVPWINRATIO_ISSET_ID = 21;
  private int __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.BATTLE_NUM,_Fields.BATTLE_WIN,_Fields.BATTLE_KILL_PVP_ENEMY,_Fields.BATTLE_DEAD_ENEMY,_Fields.CURE_SOLDIER_NUM,_Fields.BATTLE_KILL_PVE_ENEMY,_Fields.PVP_WIN_COUNT,_Fields.PVP_FALSE_COUNT,_Fields.PVE_WIN_COUNT,_Fields.PVE_FALSE_COUNT,_Fields.DESPOIL_AMOUNT,_Fields.RES_HELP_AMOUNT,_Fields.RES_HELP_RECEIVED_AMOUNT,_Fields.PVP_OFFENSE_WIN_COUNT,_Fields.PVP_OFFENSE_LOSE_COUNT,_Fields.PVP_DEFENSE_WIN_COUNT,_Fields.PVP_DEFENSE_LOSE_COUNT,_Fields.PVP_WIN_RATIO};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BATTLE_NUM, new org.apache.thrift.meta_data.FieldMetaData("battleNum", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BATTLE_WIN, new org.apache.thrift.meta_data.FieldMetaData("battleWin", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BATTLE_KILL_PVP_ENEMY, new org.apache.thrift.meta_data.FieldMetaData("battleKillPvpEnemy", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BATTLE_DEAD_ENEMY, new org.apache.thrift.meta_data.FieldMetaData("battleDeadEnemy", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CURE_SOLDIER_NUM, new org.apache.thrift.meta_data.FieldMetaData("cureSoldierNum", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BATTLE_KILL_PVE_ENEMY, new org.apache.thrift.meta_data.FieldMetaData("battleKillPveEnemy", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SCOUT_TIMES, new org.apache.thrift.meta_data.FieldMetaData("scoutTimes", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RESOURCE_GATHERED, new org.apache.thrift.meta_data.FieldMetaData("resourceGathered", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RESOURCE_ASSISTANCE, new org.apache.thrift.meta_data.FieldMetaData("resourceAssistance", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ALLIANCE_HELP_TIMES, new org.apache.thrift.meta_data.FieldMetaData("allianceHelpTimes", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PVP_WIN_COUNT, new org.apache.thrift.meta_data.FieldMetaData("pvpWinCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PVP_FALSE_COUNT, new org.apache.thrift.meta_data.FieldMetaData("pvpFalseCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PVE_WIN_COUNT, new org.apache.thrift.meta_data.FieldMetaData("pveWinCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PVE_FALSE_COUNT, new org.apache.thrift.meta_data.FieldMetaData("pveFalseCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DESPOIL_AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("despoilAmount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RES_HELP_AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("resHelpAmount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RES_HELP_RECEIVED_AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("resHelpReceivedAmount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PVP_OFFENSE_WIN_COUNT, new org.apache.thrift.meta_data.FieldMetaData("pvpOffenseWinCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PVP_OFFENSE_LOSE_COUNT, new org.apache.thrift.meta_data.FieldMetaData("pvpOffenseLoseCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PVP_DEFENSE_WIN_COUNT, new org.apache.thrift.meta_data.FieldMetaData("pvpDefenseWinCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PVP_DEFENSE_LOSE_COUNT, new org.apache.thrift.meta_data.FieldMetaData("pvpDefenseLoseCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PVP_WIN_RATIO, new org.apache.thrift.meta_data.FieldMetaData("pvpWinRatio", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcCommanderInfo.class, metaDataMap);
  }

  public GcCommanderInfo() {
  }

  public GcCommanderInfo(
    long scoutTimes,
    long resourceGathered,
    long resourceAssistance,
    long allianceHelpTimes)
  {
    this();
    this.scoutTimes = scoutTimes;
    setScoutTimesIsSet(true);
    this.resourceGathered = resourceGathered;
    setResourceGatheredIsSet(true);
    this.resourceAssistance = resourceAssistance;
    setResourceAssistanceIsSet(true);
    this.allianceHelpTimes = allianceHelpTimes;
    setAllianceHelpTimesIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcCommanderInfo(GcCommanderInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.battleNum = other.battleNum;
    this.battleWin = other.battleWin;
    this.battleKillPvpEnemy = other.battleKillPvpEnemy;
    this.battleDeadEnemy = other.battleDeadEnemy;
    this.cureSoldierNum = other.cureSoldierNum;
    this.battleKillPveEnemy = other.battleKillPveEnemy;
    this.scoutTimes = other.scoutTimes;
    this.resourceGathered = other.resourceGathered;
    this.resourceAssistance = other.resourceAssistance;
    this.allianceHelpTimes = other.allianceHelpTimes;
    this.pvpWinCount = other.pvpWinCount;
    this.pvpFalseCount = other.pvpFalseCount;
    this.pveWinCount = other.pveWinCount;
    this.pveFalseCount = other.pveFalseCount;
    this.despoilAmount = other.despoilAmount;
    this.resHelpAmount = other.resHelpAmount;
    this.resHelpReceivedAmount = other.resHelpReceivedAmount;
    this.pvpOffenseWinCount = other.pvpOffenseWinCount;
    this.pvpOffenseLoseCount = other.pvpOffenseLoseCount;
    this.pvpDefenseWinCount = other.pvpDefenseWinCount;
    this.pvpDefenseLoseCount = other.pvpDefenseLoseCount;
    this.pvpWinRatio = other.pvpWinRatio;
  }

  public GcCommanderInfo deepCopy() {
    return new GcCommanderInfo(this);
  }

  @Override
  public void clear() {
    setBattleNumIsSet(false);
    this.battleNum = 0;
    setBattleWinIsSet(false);
    this.battleWin = 0;
    setBattleKillPvpEnemyIsSet(false);
    this.battleKillPvpEnemy = 0;
    setBattleDeadEnemyIsSet(false);
    this.battleDeadEnemy = 0;
    setCureSoldierNumIsSet(false);
    this.cureSoldierNum = 0;
    setBattleKillPveEnemyIsSet(false);
    this.battleKillPveEnemy = 0;
    setScoutTimesIsSet(false);
    this.scoutTimes = 0;
    setResourceGatheredIsSet(false);
    this.resourceGathered = 0;
    setResourceAssistanceIsSet(false);
    this.resourceAssistance = 0;
    setAllianceHelpTimesIsSet(false);
    this.allianceHelpTimes = 0;
    setPvpWinCountIsSet(false);
    this.pvpWinCount = 0;
    setPvpFalseCountIsSet(false);
    this.pvpFalseCount = 0;
    setPveWinCountIsSet(false);
    this.pveWinCount = 0;
    setPveFalseCountIsSet(false);
    this.pveFalseCount = 0;
    setDespoilAmountIsSet(false);
    this.despoilAmount = 0;
    setResHelpAmountIsSet(false);
    this.resHelpAmount = 0;
    setResHelpReceivedAmountIsSet(false);
    this.resHelpReceivedAmount = 0;
    setPvpOffenseWinCountIsSet(false);
    this.pvpOffenseWinCount = 0;
    setPvpOffenseLoseCountIsSet(false);
    this.pvpOffenseLoseCount = 0;
    setPvpDefenseWinCountIsSet(false);
    this.pvpDefenseWinCount = 0;
    setPvpDefenseLoseCountIsSet(false);
    this.pvpDefenseLoseCount = 0;
    setPvpWinRatioIsSet(false);
    this.pvpWinRatio = 0.0;
  }

  /**
   * 战斗总次数：发生战斗次数总和（包含PVE+PVP)
   */
  public long getBattleNum() {
    return this.battleNum;
  }

  /**
   * 战斗总次数：发生战斗次数总和（包含PVE+PVP)
   */
  public GcCommanderInfo setBattleNum(long battleNum) {
    this.battleNum = battleNum;
    setBattleNumIsSet(true);
    return this;
  }

  public void unsetBattleNum() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BATTLENUM_ISSET_ID);
  }

  /** Returns true if field battleNum is set (has been assigned a value) and false otherwise */
  public boolean isSetBattleNum() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BATTLENUM_ISSET_ID);
  }

  public void setBattleNumIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BATTLENUM_ISSET_ID, value);
  }

  /**
   * 战斗胜利次数：战斗胜利次数总和（包含PVE+PVP）
   */
  public long getBattleWin() {
    return this.battleWin;
  }

  /**
   * 战斗胜利次数：战斗胜利次数总和（包含PVE+PVP）
   */
  public GcCommanderInfo setBattleWin(long battleWin) {
    this.battleWin = battleWin;
    setBattleWinIsSet(true);
    return this;
  }

  public void unsetBattleWin() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BATTLEWIN_ISSET_ID);
  }

  /** Returns true if field battleWin is set (has been assigned a value) and false otherwise */
  public boolean isSetBattleWin() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BATTLEWIN_ISSET_ID);
  }

  public void setBattleWinIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BATTLEWIN_ISSET_ID, value);
  }

  /**
   * 击败部队数量：对方死兵和伤兵都算
   */
  public long getBattleKillPvpEnemy() {
    return this.battleKillPvpEnemy;
  }

  /**
   * 击败部队数量：对方死兵和伤兵都算
   */
  public GcCommanderInfo setBattleKillPvpEnemy(long battleKillPvpEnemy) {
    this.battleKillPvpEnemy = battleKillPvpEnemy;
    setBattleKillPvpEnemyIsSet(true);
    return this;
  }

  public void unsetBattleKillPvpEnemy() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BATTLEKILLPVPENEMY_ISSET_ID);
  }

  /** Returns true if field battleKillPvpEnemy is set (has been assigned a value) and false otherwise */
  public boolean isSetBattleKillPvpEnemy() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BATTLEKILLPVPENEMY_ISSET_ID);
  }

  public void setBattleKillPvpEnemyIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BATTLEKILLPVPENEMY_ISSET_ID, value);
  }

  /**
   * 部队阵亡数量：自己阵亡士兵数量
   */
  public long getBattleDeadEnemy() {
    return this.battleDeadEnemy;
  }

  /**
   * 部队阵亡数量：自己阵亡士兵数量
   */
  public GcCommanderInfo setBattleDeadEnemy(long battleDeadEnemy) {
    this.battleDeadEnemy = battleDeadEnemy;
    setBattleDeadEnemyIsSet(true);
    return this;
  }

  public void unsetBattleDeadEnemy() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BATTLEDEADENEMY_ISSET_ID);
  }

  /** Returns true if field battleDeadEnemy is set (has been assigned a value) and false otherwise */
  public boolean isSetBattleDeadEnemy() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BATTLEDEADENEMY_ISSET_ID);
  }

  public void setBattleDeadEnemyIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BATTLEDEADENEMY_ISSET_ID, value);
  }

  /**
   * 部队治疗数量：医疗帐篷治疗的士兵数量
   */
  public long getCureSoldierNum() {
    return this.cureSoldierNum;
  }

  /**
   * 部队治疗数量：医疗帐篷治疗的士兵数量
   */
  public GcCommanderInfo setCureSoldierNum(long cureSoldierNum) {
    this.cureSoldierNum = cureSoldierNum;
    setCureSoldierNumIsSet(true);
    return this;
  }

  public void unsetCureSoldierNum() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CURESOLDIERNUM_ISSET_ID);
  }

  /** Returns true if field cureSoldierNum is set (has been assigned a value) and false otherwise */
  public boolean isSetCureSoldierNum() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CURESOLDIERNUM_ISSET_ID);
  }

  public void setCureSoldierNumIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CURESOLDIERNUM_ISSET_ID, value);
  }

  /**
   * 击败生化人数量：PVE击败野怪的次数。（大地图营地、各种可攻击的单位都算野怪）
   */
  public long getBattleKillPveEnemy() {
    return this.battleKillPveEnemy;
  }

  /**
   * 击败生化人数量：PVE击败野怪的次数。（大地图营地、各种可攻击的单位都算野怪）
   */
  public GcCommanderInfo setBattleKillPveEnemy(long battleKillPveEnemy) {
    this.battleKillPveEnemy = battleKillPveEnemy;
    setBattleKillPveEnemyIsSet(true);
    return this;
  }

  public void unsetBattleKillPveEnemy() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BATTLEKILLPVEENEMY_ISSET_ID);
  }

  /** Returns true if field battleKillPveEnemy is set (has been assigned a value) and false otherwise */
  public boolean isSetBattleKillPveEnemy() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BATTLEKILLPVEENEMY_ISSET_ID);
  }

  public void setBattleKillPveEnemyIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BATTLEKILLPVEENEMY_ISSET_ID, value);
  }

  /**
   * 侦查次数
   */
  public long getScoutTimes() {
    return this.scoutTimes;
  }

  /**
   * 侦查次数
   */
  public GcCommanderInfo setScoutTimes(long scoutTimes) {
    this.scoutTimes = scoutTimes;
    setScoutTimesIsSet(true);
    return this;
  }

  public void unsetScoutTimes() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SCOUTTIMES_ISSET_ID);
  }

  /** Returns true if field scoutTimes is set (has been assigned a value) and false otherwise */
  public boolean isSetScoutTimes() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SCOUTTIMES_ISSET_ID);
  }

  public void setScoutTimesIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SCOUTTIMES_ISSET_ID, value);
  }

  /**
   * 资源采集
   */
  public long getResourceGathered() {
    return this.resourceGathered;
  }

  /**
   * 资源采集
   */
  public GcCommanderInfo setResourceGathered(long resourceGathered) {
    this.resourceGathered = resourceGathered;
    setResourceGatheredIsSet(true);
    return this;
  }

  public void unsetResourceGathered() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RESOURCEGATHERED_ISSET_ID);
  }

  /** Returns true if field resourceGathered is set (has been assigned a value) and false otherwise */
  public boolean isSetResourceGathered() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RESOURCEGATHERED_ISSET_ID);
  }

  public void setResourceGatheredIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RESOURCEGATHERED_ISSET_ID, value);
  }

  /**
   * 资源援助
   */
  public long getResourceAssistance() {
    return this.resourceAssistance;
  }

  /**
   * 资源援助
   */
  public GcCommanderInfo setResourceAssistance(long resourceAssistance) {
    this.resourceAssistance = resourceAssistance;
    setResourceAssistanceIsSet(true);
    return this;
  }

  public void unsetResourceAssistance() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RESOURCEASSISTANCE_ISSET_ID);
  }

  /** Returns true if field resourceAssistance is set (has been assigned a value) and false otherwise */
  public boolean isSetResourceAssistance() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RESOURCEASSISTANCE_ISSET_ID);
  }

  public void setResourceAssistanceIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RESOURCEASSISTANCE_ISSET_ID, value);
  }

  /**
   * 联盟帮助
   */
  public long getAllianceHelpTimes() {
    return this.allianceHelpTimes;
  }

  /**
   * 联盟帮助
   */
  public GcCommanderInfo setAllianceHelpTimes(long allianceHelpTimes) {
    this.allianceHelpTimes = allianceHelpTimes;
    setAllianceHelpTimesIsSet(true);
    return this;
  }

  public void unsetAllianceHelpTimes() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ALLIANCEHELPTIMES_ISSET_ID);
  }

  /** Returns true if field allianceHelpTimes is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceHelpTimes() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ALLIANCEHELPTIMES_ISSET_ID);
  }

  public void setAllianceHelpTimesIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ALLIANCEHELPTIMES_ISSET_ID, value);
  }

  /**
   * PVP战斗胜利总次数
   */
  public long getPvpWinCount() {
    return this.pvpWinCount;
  }

  /**
   * PVP战斗胜利总次数
   */
  public GcCommanderInfo setPvpWinCount(long pvpWinCount) {
    this.pvpWinCount = pvpWinCount;
    setPvpWinCountIsSet(true);
    return this;
  }

  public void unsetPvpWinCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PVPWINCOUNT_ISSET_ID);
  }

  /** Returns true if field pvpWinCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPvpWinCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PVPWINCOUNT_ISSET_ID);
  }

  public void setPvpWinCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PVPWINCOUNT_ISSET_ID, value);
  }

  /**
   * PVP战斗失败总次数
   */
  public long getPvpFalseCount() {
    return this.pvpFalseCount;
  }

  /**
   * PVP战斗失败总次数
   */
  public GcCommanderInfo setPvpFalseCount(long pvpFalseCount) {
    this.pvpFalseCount = pvpFalseCount;
    setPvpFalseCountIsSet(true);
    return this;
  }

  public void unsetPvpFalseCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PVPFALSECOUNT_ISSET_ID);
  }

  /** Returns true if field pvpFalseCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPvpFalseCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PVPFALSECOUNT_ISSET_ID);
  }

  public void setPvpFalseCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PVPFALSECOUNT_ISSET_ID, value);
  }

  /**
   * PVE战斗胜利总次数
   */
  public long getPveWinCount() {
    return this.pveWinCount;
  }

  /**
   * PVE战斗胜利总次数
   */
  public GcCommanderInfo setPveWinCount(long pveWinCount) {
    this.pveWinCount = pveWinCount;
    setPveWinCountIsSet(true);
    return this;
  }

  public void unsetPveWinCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PVEWINCOUNT_ISSET_ID);
  }

  /** Returns true if field pveWinCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPveWinCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PVEWINCOUNT_ISSET_ID);
  }

  public void setPveWinCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PVEWINCOUNT_ISSET_ID, value);
  }

  /**
   * PVE战斗失败总次数
   */
  public long getPveFalseCount() {
    return this.pveFalseCount;
  }

  /**
   * PVE战斗失败总次数
   */
  public GcCommanderInfo setPveFalseCount(long pveFalseCount) {
    this.pveFalseCount = pveFalseCount;
    setPveFalseCountIsSet(true);
    return this;
  }

  public void unsetPveFalseCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PVEFALSECOUNT_ISSET_ID);
  }

  /** Returns true if field pveFalseCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPveFalseCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PVEFALSECOUNT_ISSET_ID);
  }

  public void setPveFalseCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PVEFALSECOUNT_ISSET_ID, value);
  }

  /**
   * 掠夺资源总数
   */
  public long getDespoilAmount() {
    return this.despoilAmount;
  }

  /**
   * 掠夺资源总数
   */
  public GcCommanderInfo setDespoilAmount(long despoilAmount) {
    this.despoilAmount = despoilAmount;
    setDespoilAmountIsSet(true);
    return this;
  }

  public void unsetDespoilAmount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __DESPOILAMOUNT_ISSET_ID);
  }

  /** Returns true if field despoilAmount is set (has been assigned a value) and false otherwise */
  public boolean isSetDespoilAmount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __DESPOILAMOUNT_ISSET_ID);
  }

  public void setDespoilAmountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __DESPOILAMOUNT_ISSET_ID, value);
  }

  /**
   * 援助资源总数
   */
  public long getResHelpAmount() {
    return this.resHelpAmount;
  }

  /**
   * 援助资源总数
   */
  public GcCommanderInfo setResHelpAmount(long resHelpAmount) {
    this.resHelpAmount = resHelpAmount;
    setResHelpAmountIsSet(true);
    return this;
  }

  public void unsetResHelpAmount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RESHELPAMOUNT_ISSET_ID);
  }

  /** Returns true if field resHelpAmount is set (has been assigned a value) and false otherwise */
  public boolean isSetResHelpAmount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RESHELPAMOUNT_ISSET_ID);
  }

  public void setResHelpAmountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RESHELPAMOUNT_ISSET_ID, value);
  }

  /**
   * 被援助资源总数
   */
  public long getResHelpReceivedAmount() {
    return this.resHelpReceivedAmount;
  }

  /**
   * 被援助资源总数
   */
  public GcCommanderInfo setResHelpReceivedAmount(long resHelpReceivedAmount) {
    this.resHelpReceivedAmount = resHelpReceivedAmount;
    setResHelpReceivedAmountIsSet(true);
    return this;
  }

  public void unsetResHelpReceivedAmount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RESHELPRECEIVEDAMOUNT_ISSET_ID);
  }

  /** Returns true if field resHelpReceivedAmount is set (has been assigned a value) and false otherwise */
  public boolean isSetResHelpReceivedAmount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RESHELPRECEIVEDAMOUNT_ISSET_ID);
  }

  public void setResHelpReceivedAmountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RESHELPRECEIVEDAMOUNT_ISSET_ID, value);
  }

  /**
   * pvp 进攻胜利次数
   */
  public long getPvpOffenseWinCount() {
    return this.pvpOffenseWinCount;
  }

  /**
   * pvp 进攻胜利次数
   */
  public GcCommanderInfo setPvpOffenseWinCount(long pvpOffenseWinCount) {
    this.pvpOffenseWinCount = pvpOffenseWinCount;
    setPvpOffenseWinCountIsSet(true);
    return this;
  }

  public void unsetPvpOffenseWinCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PVPOFFENSEWINCOUNT_ISSET_ID);
  }

  /** Returns true if field pvpOffenseWinCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPvpOffenseWinCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PVPOFFENSEWINCOUNT_ISSET_ID);
  }

  public void setPvpOffenseWinCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PVPOFFENSEWINCOUNT_ISSET_ID, value);
  }

  /**
   * pvp 进攻失败次数
   */
  public long getPvpOffenseLoseCount() {
    return this.pvpOffenseLoseCount;
  }

  /**
   * pvp 进攻失败次数
   */
  public GcCommanderInfo setPvpOffenseLoseCount(long pvpOffenseLoseCount) {
    this.pvpOffenseLoseCount = pvpOffenseLoseCount;
    setPvpOffenseLoseCountIsSet(true);
    return this;
  }

  public void unsetPvpOffenseLoseCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PVPOFFENSELOSECOUNT_ISSET_ID);
  }

  /** Returns true if field pvpOffenseLoseCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPvpOffenseLoseCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PVPOFFENSELOSECOUNT_ISSET_ID);
  }

  public void setPvpOffenseLoseCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PVPOFFENSELOSECOUNT_ISSET_ID, value);
  }

  /**
   * pvp 防守胜利
   */
  public long getPvpDefenseWinCount() {
    return this.pvpDefenseWinCount;
  }

  /**
   * pvp 防守胜利
   */
  public GcCommanderInfo setPvpDefenseWinCount(long pvpDefenseWinCount) {
    this.pvpDefenseWinCount = pvpDefenseWinCount;
    setPvpDefenseWinCountIsSet(true);
    return this;
  }

  public void unsetPvpDefenseWinCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PVPDEFENSEWINCOUNT_ISSET_ID);
  }

  /** Returns true if field pvpDefenseWinCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPvpDefenseWinCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PVPDEFENSEWINCOUNT_ISSET_ID);
  }

  public void setPvpDefenseWinCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PVPDEFENSEWINCOUNT_ISSET_ID, value);
  }

  /**
   * pvp 防守失败 次数
   */
  public long getPvpDefenseLoseCount() {
    return this.pvpDefenseLoseCount;
  }

  /**
   * pvp 防守失败 次数
   */
  public GcCommanderInfo setPvpDefenseLoseCount(long pvpDefenseLoseCount) {
    this.pvpDefenseLoseCount = pvpDefenseLoseCount;
    setPvpDefenseLoseCountIsSet(true);
    return this;
  }

  public void unsetPvpDefenseLoseCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PVPDEFENSELOSECOUNT_ISSET_ID);
  }

  /** Returns true if field pvpDefenseLoseCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPvpDefenseLoseCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PVPDEFENSELOSECOUNT_ISSET_ID);
  }

  public void setPvpDefenseLoseCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PVPDEFENSELOSECOUNT_ISSET_ID, value);
  }

  /**
   * pvp 战斗胜率 pvpWinCount / (pvpFalseCount + pvpWinCount)
   */
  public double getPvpWinRatio() {
    return this.pvpWinRatio;
  }

  /**
   * pvp 战斗胜率 pvpWinCount / (pvpFalseCount + pvpWinCount)
   */
  public GcCommanderInfo setPvpWinRatio(double pvpWinRatio) {
    this.pvpWinRatio = pvpWinRatio;
    setPvpWinRatioIsSet(true);
    return this;
  }

  public void unsetPvpWinRatio() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PVPWINRATIO_ISSET_ID);
  }

  /** Returns true if field pvpWinRatio is set (has been assigned a value) and false otherwise */
  public boolean isSetPvpWinRatio() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PVPWINRATIO_ISSET_ID);
  }

  public void setPvpWinRatioIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PVPWINRATIO_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case BATTLE_NUM:
      if (value == null) {
        unsetBattleNum();
      } else {
        setBattleNum((java.lang.Long)value);
      }
      break;

    case BATTLE_WIN:
      if (value == null) {
        unsetBattleWin();
      } else {
        setBattleWin((java.lang.Long)value);
      }
      break;

    case BATTLE_KILL_PVP_ENEMY:
      if (value == null) {
        unsetBattleKillPvpEnemy();
      } else {
        setBattleKillPvpEnemy((java.lang.Long)value);
      }
      break;

    case BATTLE_DEAD_ENEMY:
      if (value == null) {
        unsetBattleDeadEnemy();
      } else {
        setBattleDeadEnemy((java.lang.Long)value);
      }
      break;

    case CURE_SOLDIER_NUM:
      if (value == null) {
        unsetCureSoldierNum();
      } else {
        setCureSoldierNum((java.lang.Long)value);
      }
      break;

    case BATTLE_KILL_PVE_ENEMY:
      if (value == null) {
        unsetBattleKillPveEnemy();
      } else {
        setBattleKillPveEnemy((java.lang.Long)value);
      }
      break;

    case SCOUT_TIMES:
      if (value == null) {
        unsetScoutTimes();
      } else {
        setScoutTimes((java.lang.Long)value);
      }
      break;

    case RESOURCE_GATHERED:
      if (value == null) {
        unsetResourceGathered();
      } else {
        setResourceGathered((java.lang.Long)value);
      }
      break;

    case RESOURCE_ASSISTANCE:
      if (value == null) {
        unsetResourceAssistance();
      } else {
        setResourceAssistance((java.lang.Long)value);
      }
      break;

    case ALLIANCE_HELP_TIMES:
      if (value == null) {
        unsetAllianceHelpTimes();
      } else {
        setAllianceHelpTimes((java.lang.Long)value);
      }
      break;

    case PVP_WIN_COUNT:
      if (value == null) {
        unsetPvpWinCount();
      } else {
        setPvpWinCount((java.lang.Long)value);
      }
      break;

    case PVP_FALSE_COUNT:
      if (value == null) {
        unsetPvpFalseCount();
      } else {
        setPvpFalseCount((java.lang.Long)value);
      }
      break;

    case PVE_WIN_COUNT:
      if (value == null) {
        unsetPveWinCount();
      } else {
        setPveWinCount((java.lang.Long)value);
      }
      break;

    case PVE_FALSE_COUNT:
      if (value == null) {
        unsetPveFalseCount();
      } else {
        setPveFalseCount((java.lang.Long)value);
      }
      break;

    case DESPOIL_AMOUNT:
      if (value == null) {
        unsetDespoilAmount();
      } else {
        setDespoilAmount((java.lang.Long)value);
      }
      break;

    case RES_HELP_AMOUNT:
      if (value == null) {
        unsetResHelpAmount();
      } else {
        setResHelpAmount((java.lang.Long)value);
      }
      break;

    case RES_HELP_RECEIVED_AMOUNT:
      if (value == null) {
        unsetResHelpReceivedAmount();
      } else {
        setResHelpReceivedAmount((java.lang.Long)value);
      }
      break;

    case PVP_OFFENSE_WIN_COUNT:
      if (value == null) {
        unsetPvpOffenseWinCount();
      } else {
        setPvpOffenseWinCount((java.lang.Long)value);
      }
      break;

    case PVP_OFFENSE_LOSE_COUNT:
      if (value == null) {
        unsetPvpOffenseLoseCount();
      } else {
        setPvpOffenseLoseCount((java.lang.Long)value);
      }
      break;

    case PVP_DEFENSE_WIN_COUNT:
      if (value == null) {
        unsetPvpDefenseWinCount();
      } else {
        setPvpDefenseWinCount((java.lang.Long)value);
      }
      break;

    case PVP_DEFENSE_LOSE_COUNT:
      if (value == null) {
        unsetPvpDefenseLoseCount();
      } else {
        setPvpDefenseLoseCount((java.lang.Long)value);
      }
      break;

    case PVP_WIN_RATIO:
      if (value == null) {
        unsetPvpWinRatio();
      } else {
        setPvpWinRatio((java.lang.Double)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case BATTLE_NUM:
      return getBattleNum();

    case BATTLE_WIN:
      return getBattleWin();

    case BATTLE_KILL_PVP_ENEMY:
      return getBattleKillPvpEnemy();

    case BATTLE_DEAD_ENEMY:
      return getBattleDeadEnemy();

    case CURE_SOLDIER_NUM:
      return getCureSoldierNum();

    case BATTLE_KILL_PVE_ENEMY:
      return getBattleKillPveEnemy();

    case SCOUT_TIMES:
      return getScoutTimes();

    case RESOURCE_GATHERED:
      return getResourceGathered();

    case RESOURCE_ASSISTANCE:
      return getResourceAssistance();

    case ALLIANCE_HELP_TIMES:
      return getAllianceHelpTimes();

    case PVP_WIN_COUNT:
      return getPvpWinCount();

    case PVP_FALSE_COUNT:
      return getPvpFalseCount();

    case PVE_WIN_COUNT:
      return getPveWinCount();

    case PVE_FALSE_COUNT:
      return getPveFalseCount();

    case DESPOIL_AMOUNT:
      return getDespoilAmount();

    case RES_HELP_AMOUNT:
      return getResHelpAmount();

    case RES_HELP_RECEIVED_AMOUNT:
      return getResHelpReceivedAmount();

    case PVP_OFFENSE_WIN_COUNT:
      return getPvpOffenseWinCount();

    case PVP_OFFENSE_LOSE_COUNT:
      return getPvpOffenseLoseCount();

    case PVP_DEFENSE_WIN_COUNT:
      return getPvpDefenseWinCount();

    case PVP_DEFENSE_LOSE_COUNT:
      return getPvpDefenseLoseCount();

    case PVP_WIN_RATIO:
      return getPvpWinRatio();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case BATTLE_NUM:
      return isSetBattleNum();
    case BATTLE_WIN:
      return isSetBattleWin();
    case BATTLE_KILL_PVP_ENEMY:
      return isSetBattleKillPvpEnemy();
    case BATTLE_DEAD_ENEMY:
      return isSetBattleDeadEnemy();
    case CURE_SOLDIER_NUM:
      return isSetCureSoldierNum();
    case BATTLE_KILL_PVE_ENEMY:
      return isSetBattleKillPveEnemy();
    case SCOUT_TIMES:
      return isSetScoutTimes();
    case RESOURCE_GATHERED:
      return isSetResourceGathered();
    case RESOURCE_ASSISTANCE:
      return isSetResourceAssistance();
    case ALLIANCE_HELP_TIMES:
      return isSetAllianceHelpTimes();
    case PVP_WIN_COUNT:
      return isSetPvpWinCount();
    case PVP_FALSE_COUNT:
      return isSetPvpFalseCount();
    case PVE_WIN_COUNT:
      return isSetPveWinCount();
    case PVE_FALSE_COUNT:
      return isSetPveFalseCount();
    case DESPOIL_AMOUNT:
      return isSetDespoilAmount();
    case RES_HELP_AMOUNT:
      return isSetResHelpAmount();
    case RES_HELP_RECEIVED_AMOUNT:
      return isSetResHelpReceivedAmount();
    case PVP_OFFENSE_WIN_COUNT:
      return isSetPvpOffenseWinCount();
    case PVP_OFFENSE_LOSE_COUNT:
      return isSetPvpOffenseLoseCount();
    case PVP_DEFENSE_WIN_COUNT:
      return isSetPvpDefenseWinCount();
    case PVP_DEFENSE_LOSE_COUNT:
      return isSetPvpDefenseLoseCount();
    case PVP_WIN_RATIO:
      return isSetPvpWinRatio();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcCommanderInfo)
      return this.equals((GcCommanderInfo)that);
    return false;
  }

  public boolean equals(GcCommanderInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_battleNum = true && this.isSetBattleNum();
    boolean that_present_battleNum = true && that.isSetBattleNum();
    if (this_present_battleNum || that_present_battleNum) {
      if (!(this_present_battleNum && that_present_battleNum))
        return false;
      if (this.battleNum != that.battleNum)
        return false;
    }

    boolean this_present_battleWin = true && this.isSetBattleWin();
    boolean that_present_battleWin = true && that.isSetBattleWin();
    if (this_present_battleWin || that_present_battleWin) {
      if (!(this_present_battleWin && that_present_battleWin))
        return false;
      if (this.battleWin != that.battleWin)
        return false;
    }

    boolean this_present_battleKillPvpEnemy = true && this.isSetBattleKillPvpEnemy();
    boolean that_present_battleKillPvpEnemy = true && that.isSetBattleKillPvpEnemy();
    if (this_present_battleKillPvpEnemy || that_present_battleKillPvpEnemy) {
      if (!(this_present_battleKillPvpEnemy && that_present_battleKillPvpEnemy))
        return false;
      if (this.battleKillPvpEnemy != that.battleKillPvpEnemy)
        return false;
    }

    boolean this_present_battleDeadEnemy = true && this.isSetBattleDeadEnemy();
    boolean that_present_battleDeadEnemy = true && that.isSetBattleDeadEnemy();
    if (this_present_battleDeadEnemy || that_present_battleDeadEnemy) {
      if (!(this_present_battleDeadEnemy && that_present_battleDeadEnemy))
        return false;
      if (this.battleDeadEnemy != that.battleDeadEnemy)
        return false;
    }

    boolean this_present_cureSoldierNum = true && this.isSetCureSoldierNum();
    boolean that_present_cureSoldierNum = true && that.isSetCureSoldierNum();
    if (this_present_cureSoldierNum || that_present_cureSoldierNum) {
      if (!(this_present_cureSoldierNum && that_present_cureSoldierNum))
        return false;
      if (this.cureSoldierNum != that.cureSoldierNum)
        return false;
    }

    boolean this_present_battleKillPveEnemy = true && this.isSetBattleKillPveEnemy();
    boolean that_present_battleKillPveEnemy = true && that.isSetBattleKillPveEnemy();
    if (this_present_battleKillPveEnemy || that_present_battleKillPveEnemy) {
      if (!(this_present_battleKillPveEnemy && that_present_battleKillPveEnemy))
        return false;
      if (this.battleKillPveEnemy != that.battleKillPveEnemy)
        return false;
    }

    boolean this_present_scoutTimes = true;
    boolean that_present_scoutTimes = true;
    if (this_present_scoutTimes || that_present_scoutTimes) {
      if (!(this_present_scoutTimes && that_present_scoutTimes))
        return false;
      if (this.scoutTimes != that.scoutTimes)
        return false;
    }

    boolean this_present_resourceGathered = true;
    boolean that_present_resourceGathered = true;
    if (this_present_resourceGathered || that_present_resourceGathered) {
      if (!(this_present_resourceGathered && that_present_resourceGathered))
        return false;
      if (this.resourceGathered != that.resourceGathered)
        return false;
    }

    boolean this_present_resourceAssistance = true;
    boolean that_present_resourceAssistance = true;
    if (this_present_resourceAssistance || that_present_resourceAssistance) {
      if (!(this_present_resourceAssistance && that_present_resourceAssistance))
        return false;
      if (this.resourceAssistance != that.resourceAssistance)
        return false;
    }

    boolean this_present_allianceHelpTimes = true;
    boolean that_present_allianceHelpTimes = true;
    if (this_present_allianceHelpTimes || that_present_allianceHelpTimes) {
      if (!(this_present_allianceHelpTimes && that_present_allianceHelpTimes))
        return false;
      if (this.allianceHelpTimes != that.allianceHelpTimes)
        return false;
    }

    boolean this_present_pvpWinCount = true && this.isSetPvpWinCount();
    boolean that_present_pvpWinCount = true && that.isSetPvpWinCount();
    if (this_present_pvpWinCount || that_present_pvpWinCount) {
      if (!(this_present_pvpWinCount && that_present_pvpWinCount))
        return false;
      if (this.pvpWinCount != that.pvpWinCount)
        return false;
    }

    boolean this_present_pvpFalseCount = true && this.isSetPvpFalseCount();
    boolean that_present_pvpFalseCount = true && that.isSetPvpFalseCount();
    if (this_present_pvpFalseCount || that_present_pvpFalseCount) {
      if (!(this_present_pvpFalseCount && that_present_pvpFalseCount))
        return false;
      if (this.pvpFalseCount != that.pvpFalseCount)
        return false;
    }

    boolean this_present_pveWinCount = true && this.isSetPveWinCount();
    boolean that_present_pveWinCount = true && that.isSetPveWinCount();
    if (this_present_pveWinCount || that_present_pveWinCount) {
      if (!(this_present_pveWinCount && that_present_pveWinCount))
        return false;
      if (this.pveWinCount != that.pveWinCount)
        return false;
    }

    boolean this_present_pveFalseCount = true && this.isSetPveFalseCount();
    boolean that_present_pveFalseCount = true && that.isSetPveFalseCount();
    if (this_present_pveFalseCount || that_present_pveFalseCount) {
      if (!(this_present_pveFalseCount && that_present_pveFalseCount))
        return false;
      if (this.pveFalseCount != that.pveFalseCount)
        return false;
    }

    boolean this_present_despoilAmount = true && this.isSetDespoilAmount();
    boolean that_present_despoilAmount = true && that.isSetDespoilAmount();
    if (this_present_despoilAmount || that_present_despoilAmount) {
      if (!(this_present_despoilAmount && that_present_despoilAmount))
        return false;
      if (this.despoilAmount != that.despoilAmount)
        return false;
    }

    boolean this_present_resHelpAmount = true && this.isSetResHelpAmount();
    boolean that_present_resHelpAmount = true && that.isSetResHelpAmount();
    if (this_present_resHelpAmount || that_present_resHelpAmount) {
      if (!(this_present_resHelpAmount && that_present_resHelpAmount))
        return false;
      if (this.resHelpAmount != that.resHelpAmount)
        return false;
    }

    boolean this_present_resHelpReceivedAmount = true && this.isSetResHelpReceivedAmount();
    boolean that_present_resHelpReceivedAmount = true && that.isSetResHelpReceivedAmount();
    if (this_present_resHelpReceivedAmount || that_present_resHelpReceivedAmount) {
      if (!(this_present_resHelpReceivedAmount && that_present_resHelpReceivedAmount))
        return false;
      if (this.resHelpReceivedAmount != that.resHelpReceivedAmount)
        return false;
    }

    boolean this_present_pvpOffenseWinCount = true && this.isSetPvpOffenseWinCount();
    boolean that_present_pvpOffenseWinCount = true && that.isSetPvpOffenseWinCount();
    if (this_present_pvpOffenseWinCount || that_present_pvpOffenseWinCount) {
      if (!(this_present_pvpOffenseWinCount && that_present_pvpOffenseWinCount))
        return false;
      if (this.pvpOffenseWinCount != that.pvpOffenseWinCount)
        return false;
    }

    boolean this_present_pvpOffenseLoseCount = true && this.isSetPvpOffenseLoseCount();
    boolean that_present_pvpOffenseLoseCount = true && that.isSetPvpOffenseLoseCount();
    if (this_present_pvpOffenseLoseCount || that_present_pvpOffenseLoseCount) {
      if (!(this_present_pvpOffenseLoseCount && that_present_pvpOffenseLoseCount))
        return false;
      if (this.pvpOffenseLoseCount != that.pvpOffenseLoseCount)
        return false;
    }

    boolean this_present_pvpDefenseWinCount = true && this.isSetPvpDefenseWinCount();
    boolean that_present_pvpDefenseWinCount = true && that.isSetPvpDefenseWinCount();
    if (this_present_pvpDefenseWinCount || that_present_pvpDefenseWinCount) {
      if (!(this_present_pvpDefenseWinCount && that_present_pvpDefenseWinCount))
        return false;
      if (this.pvpDefenseWinCount != that.pvpDefenseWinCount)
        return false;
    }

    boolean this_present_pvpDefenseLoseCount = true && this.isSetPvpDefenseLoseCount();
    boolean that_present_pvpDefenseLoseCount = true && that.isSetPvpDefenseLoseCount();
    if (this_present_pvpDefenseLoseCount || that_present_pvpDefenseLoseCount) {
      if (!(this_present_pvpDefenseLoseCount && that_present_pvpDefenseLoseCount))
        return false;
      if (this.pvpDefenseLoseCount != that.pvpDefenseLoseCount)
        return false;
    }

    boolean this_present_pvpWinRatio = true && this.isSetPvpWinRatio();
    boolean that_present_pvpWinRatio = true && that.isSetPvpWinRatio();
    if (this_present_pvpWinRatio || that_present_pvpWinRatio) {
      if (!(this_present_pvpWinRatio && that_present_pvpWinRatio))
        return false;
      if (this.pvpWinRatio != that.pvpWinRatio)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetBattleNum()) ? 131071 : 524287);
    if (isSetBattleNum())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(battleNum);

    hashCode = hashCode * 8191 + ((isSetBattleWin()) ? 131071 : 524287);
    if (isSetBattleWin())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(battleWin);

    hashCode = hashCode * 8191 + ((isSetBattleKillPvpEnemy()) ? 131071 : 524287);
    if (isSetBattleKillPvpEnemy())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(battleKillPvpEnemy);

    hashCode = hashCode * 8191 + ((isSetBattleDeadEnemy()) ? 131071 : 524287);
    if (isSetBattleDeadEnemy())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(battleDeadEnemy);

    hashCode = hashCode * 8191 + ((isSetCureSoldierNum()) ? 131071 : 524287);
    if (isSetCureSoldierNum())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(cureSoldierNum);

    hashCode = hashCode * 8191 + ((isSetBattleKillPveEnemy()) ? 131071 : 524287);
    if (isSetBattleKillPveEnemy())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(battleKillPveEnemy);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(scoutTimes);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(resourceGathered);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(resourceAssistance);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(allianceHelpTimes);

    hashCode = hashCode * 8191 + ((isSetPvpWinCount()) ? 131071 : 524287);
    if (isSetPvpWinCount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pvpWinCount);

    hashCode = hashCode * 8191 + ((isSetPvpFalseCount()) ? 131071 : 524287);
    if (isSetPvpFalseCount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pvpFalseCount);

    hashCode = hashCode * 8191 + ((isSetPveWinCount()) ? 131071 : 524287);
    if (isSetPveWinCount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pveWinCount);

    hashCode = hashCode * 8191 + ((isSetPveFalseCount()) ? 131071 : 524287);
    if (isSetPveFalseCount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pveFalseCount);

    hashCode = hashCode * 8191 + ((isSetDespoilAmount()) ? 131071 : 524287);
    if (isSetDespoilAmount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(despoilAmount);

    hashCode = hashCode * 8191 + ((isSetResHelpAmount()) ? 131071 : 524287);
    if (isSetResHelpAmount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(resHelpAmount);

    hashCode = hashCode * 8191 + ((isSetResHelpReceivedAmount()) ? 131071 : 524287);
    if (isSetResHelpReceivedAmount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(resHelpReceivedAmount);

    hashCode = hashCode * 8191 + ((isSetPvpOffenseWinCount()) ? 131071 : 524287);
    if (isSetPvpOffenseWinCount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pvpOffenseWinCount);

    hashCode = hashCode * 8191 + ((isSetPvpOffenseLoseCount()) ? 131071 : 524287);
    if (isSetPvpOffenseLoseCount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pvpOffenseLoseCount);

    hashCode = hashCode * 8191 + ((isSetPvpDefenseWinCount()) ? 131071 : 524287);
    if (isSetPvpDefenseWinCount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pvpDefenseWinCount);

    hashCode = hashCode * 8191 + ((isSetPvpDefenseLoseCount()) ? 131071 : 524287);
    if (isSetPvpDefenseLoseCount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pvpDefenseLoseCount);

    hashCode = hashCode * 8191 + ((isSetPvpWinRatio()) ? 131071 : 524287);
    if (isSetPvpWinRatio())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pvpWinRatio);

    return hashCode;
  }

  @Override
  public int compareTo(GcCommanderInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetBattleNum(), other.isSetBattleNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBattleNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.battleNum, other.battleNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBattleWin(), other.isSetBattleWin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBattleWin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.battleWin, other.battleWin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBattleKillPvpEnemy(), other.isSetBattleKillPvpEnemy());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBattleKillPvpEnemy()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.battleKillPvpEnemy, other.battleKillPvpEnemy);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBattleDeadEnemy(), other.isSetBattleDeadEnemy());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBattleDeadEnemy()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.battleDeadEnemy, other.battleDeadEnemy);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCureSoldierNum(), other.isSetCureSoldierNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCureSoldierNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cureSoldierNum, other.cureSoldierNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBattleKillPveEnemy(), other.isSetBattleKillPveEnemy());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBattleKillPveEnemy()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.battleKillPveEnemy, other.battleKillPveEnemy);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetScoutTimes(), other.isSetScoutTimes());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetScoutTimes()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.scoutTimes, other.scoutTimes);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetResourceGathered(), other.isSetResourceGathered());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResourceGathered()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.resourceGathered, other.resourceGathered);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetResourceAssistance(), other.isSetResourceAssistance());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResourceAssistance()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.resourceAssistance, other.resourceAssistance);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAllianceHelpTimes(), other.isSetAllianceHelpTimes());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceHelpTimes()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceHelpTimes, other.allianceHelpTimes);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPvpWinCount(), other.isSetPvpWinCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPvpWinCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pvpWinCount, other.pvpWinCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPvpFalseCount(), other.isSetPvpFalseCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPvpFalseCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pvpFalseCount, other.pvpFalseCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPveWinCount(), other.isSetPveWinCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPveWinCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pveWinCount, other.pveWinCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPveFalseCount(), other.isSetPveFalseCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPveFalseCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pveFalseCount, other.pveFalseCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetDespoilAmount(), other.isSetDespoilAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDespoilAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.despoilAmount, other.despoilAmount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetResHelpAmount(), other.isSetResHelpAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResHelpAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.resHelpAmount, other.resHelpAmount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetResHelpReceivedAmount(), other.isSetResHelpReceivedAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResHelpReceivedAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.resHelpReceivedAmount, other.resHelpReceivedAmount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPvpOffenseWinCount(), other.isSetPvpOffenseWinCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPvpOffenseWinCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pvpOffenseWinCount, other.pvpOffenseWinCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPvpOffenseLoseCount(), other.isSetPvpOffenseLoseCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPvpOffenseLoseCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pvpOffenseLoseCount, other.pvpOffenseLoseCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPvpDefenseWinCount(), other.isSetPvpDefenseWinCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPvpDefenseWinCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pvpDefenseWinCount, other.pvpDefenseWinCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPvpDefenseLoseCount(), other.isSetPvpDefenseLoseCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPvpDefenseLoseCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pvpDefenseLoseCount, other.pvpDefenseLoseCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPvpWinRatio(), other.isSetPvpWinRatio());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPvpWinRatio()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pvpWinRatio, other.pvpWinRatio);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcCommanderInfo(");
    boolean first = true;

    if (isSetBattleNum()) {
      sb.append("battleNum:");
      sb.append(this.battleNum);
      first = false;
    }
    if (isSetBattleWin()) {
      if (!first) sb.append(", ");
      sb.append("battleWin:");
      sb.append(this.battleWin);
      first = false;
    }
    if (isSetBattleKillPvpEnemy()) {
      if (!first) sb.append(", ");
      sb.append("battleKillPvpEnemy:");
      sb.append(this.battleKillPvpEnemy);
      first = false;
    }
    if (isSetBattleDeadEnemy()) {
      if (!first) sb.append(", ");
      sb.append("battleDeadEnemy:");
      sb.append(this.battleDeadEnemy);
      first = false;
    }
    if (isSetCureSoldierNum()) {
      if (!first) sb.append(", ");
      sb.append("cureSoldierNum:");
      sb.append(this.cureSoldierNum);
      first = false;
    }
    if (isSetBattleKillPveEnemy()) {
      if (!first) sb.append(", ");
      sb.append("battleKillPveEnemy:");
      sb.append(this.battleKillPveEnemy);
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("scoutTimes:");
    sb.append(this.scoutTimes);
    first = false;
    if (!first) sb.append(", ");
    sb.append("resourceGathered:");
    sb.append(this.resourceGathered);
    first = false;
    if (!first) sb.append(", ");
    sb.append("resourceAssistance:");
    sb.append(this.resourceAssistance);
    first = false;
    if (!first) sb.append(", ");
    sb.append("allianceHelpTimes:");
    sb.append(this.allianceHelpTimes);
    first = false;
    if (isSetPvpWinCount()) {
      if (!first) sb.append(", ");
      sb.append("pvpWinCount:");
      sb.append(this.pvpWinCount);
      first = false;
    }
    if (isSetPvpFalseCount()) {
      if (!first) sb.append(", ");
      sb.append("pvpFalseCount:");
      sb.append(this.pvpFalseCount);
      first = false;
    }
    if (isSetPveWinCount()) {
      if (!first) sb.append(", ");
      sb.append("pveWinCount:");
      sb.append(this.pveWinCount);
      first = false;
    }
    if (isSetPveFalseCount()) {
      if (!first) sb.append(", ");
      sb.append("pveFalseCount:");
      sb.append(this.pveFalseCount);
      first = false;
    }
    if (isSetDespoilAmount()) {
      if (!first) sb.append(", ");
      sb.append("despoilAmount:");
      sb.append(this.despoilAmount);
      first = false;
    }
    if (isSetResHelpAmount()) {
      if (!first) sb.append(", ");
      sb.append("resHelpAmount:");
      sb.append(this.resHelpAmount);
      first = false;
    }
    if (isSetResHelpReceivedAmount()) {
      if (!first) sb.append(", ");
      sb.append("resHelpReceivedAmount:");
      sb.append(this.resHelpReceivedAmount);
      first = false;
    }
    if (isSetPvpOffenseWinCount()) {
      if (!first) sb.append(", ");
      sb.append("pvpOffenseWinCount:");
      sb.append(this.pvpOffenseWinCount);
      first = false;
    }
    if (isSetPvpOffenseLoseCount()) {
      if (!first) sb.append(", ");
      sb.append("pvpOffenseLoseCount:");
      sb.append(this.pvpOffenseLoseCount);
      first = false;
    }
    if (isSetPvpDefenseWinCount()) {
      if (!first) sb.append(", ");
      sb.append("pvpDefenseWinCount:");
      sb.append(this.pvpDefenseWinCount);
      first = false;
    }
    if (isSetPvpDefenseLoseCount()) {
      if (!first) sb.append(", ");
      sb.append("pvpDefenseLoseCount:");
      sb.append(this.pvpDefenseLoseCount);
      first = false;
    }
    if (isSetPvpWinRatio()) {
      if (!first) sb.append(", ");
      sb.append("pvpWinRatio:");
      sb.append(this.pvpWinRatio);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'scoutTimes' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'resourceGathered' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'resourceAssistance' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'allianceHelpTimes' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcCommanderInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcCommanderInfoStandardScheme getScheme() {
      return new GcCommanderInfoStandardScheme();
    }
  }

  private static class GcCommanderInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcCommanderInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcCommanderInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BATTLE_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.battleNum = iprot.readI64();
              struct.setBattleNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BATTLE_WIN
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.battleWin = iprot.readI64();
              struct.setBattleWinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // BATTLE_KILL_PVP_ENEMY
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.battleKillPvpEnemy = iprot.readI64();
              struct.setBattleKillPvpEnemyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // BATTLE_DEAD_ENEMY
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.battleDeadEnemy = iprot.readI64();
              struct.setBattleDeadEnemyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // CURE_SOLDIER_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.cureSoldierNum = iprot.readI64();
              struct.setCureSoldierNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // BATTLE_KILL_PVE_ENEMY
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.battleKillPveEnemy = iprot.readI64();
              struct.setBattleKillPveEnemyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // SCOUT_TIMES
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.scoutTimes = iprot.readI64();
              struct.setScoutTimesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // RESOURCE_GATHERED
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.resourceGathered = iprot.readI64();
              struct.setResourceGatheredIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // RESOURCE_ASSISTANCE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.resourceAssistance = iprot.readI64();
              struct.setResourceAssistanceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // ALLIANCE_HELP_TIMES
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.allianceHelpTimes = iprot.readI64();
              struct.setAllianceHelpTimesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // PVP_WIN_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.pvpWinCount = iprot.readI64();
              struct.setPvpWinCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // PVP_FALSE_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.pvpFalseCount = iprot.readI64();
              struct.setPvpFalseCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // PVE_WIN_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.pveWinCount = iprot.readI64();
              struct.setPveWinCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // PVE_FALSE_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.pveFalseCount = iprot.readI64();
              struct.setPveFalseCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // DESPOIL_AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.despoilAmount = iprot.readI64();
              struct.setDespoilAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // RES_HELP_AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.resHelpAmount = iprot.readI64();
              struct.setResHelpAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // RES_HELP_RECEIVED_AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.resHelpReceivedAmount = iprot.readI64();
              struct.setResHelpReceivedAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 19: // PVP_OFFENSE_WIN_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.pvpOffenseWinCount = iprot.readI64();
              struct.setPvpOffenseWinCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 20: // PVP_OFFENSE_LOSE_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.pvpOffenseLoseCount = iprot.readI64();
              struct.setPvpOffenseLoseCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 21: // PVP_DEFENSE_WIN_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.pvpDefenseWinCount = iprot.readI64();
              struct.setPvpDefenseWinCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 22: // PVP_DEFENSE_LOSE_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.pvpDefenseLoseCount = iprot.readI64();
              struct.setPvpDefenseLoseCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 23: // PVP_WIN_RATIO
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.pvpWinRatio = iprot.readDouble();
              struct.setPvpWinRatioIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetScoutTimes()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'scoutTimes' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetResourceGathered()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'resourceGathered' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetResourceAssistance()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'resourceAssistance' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAllianceHelpTimes()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'allianceHelpTimes' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcCommanderInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetBattleNum()) {
        oprot.writeFieldBegin(BATTLE_NUM_FIELD_DESC);
        oprot.writeI64(struct.battleNum);
        oprot.writeFieldEnd();
      }
      if (struct.isSetBattleWin()) {
        oprot.writeFieldBegin(BATTLE_WIN_FIELD_DESC);
        oprot.writeI64(struct.battleWin);
        oprot.writeFieldEnd();
      }
      if (struct.isSetBattleKillPvpEnemy()) {
        oprot.writeFieldBegin(BATTLE_KILL_PVP_ENEMY_FIELD_DESC);
        oprot.writeI64(struct.battleKillPvpEnemy);
        oprot.writeFieldEnd();
      }
      if (struct.isSetBattleDeadEnemy()) {
        oprot.writeFieldBegin(BATTLE_DEAD_ENEMY_FIELD_DESC);
        oprot.writeI64(struct.battleDeadEnemy);
        oprot.writeFieldEnd();
      }
      if (struct.isSetCureSoldierNum()) {
        oprot.writeFieldBegin(CURE_SOLDIER_NUM_FIELD_DESC);
        oprot.writeI64(struct.cureSoldierNum);
        oprot.writeFieldEnd();
      }
      if (struct.isSetBattleKillPveEnemy()) {
        oprot.writeFieldBegin(BATTLE_KILL_PVE_ENEMY_FIELD_DESC);
        oprot.writeI64(struct.battleKillPveEnemy);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SCOUT_TIMES_FIELD_DESC);
      oprot.writeI64(struct.scoutTimes);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RESOURCE_GATHERED_FIELD_DESC);
      oprot.writeI64(struct.resourceGathered);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RESOURCE_ASSISTANCE_FIELD_DESC);
      oprot.writeI64(struct.resourceAssistance);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ALLIANCE_HELP_TIMES_FIELD_DESC);
      oprot.writeI64(struct.allianceHelpTimes);
      oprot.writeFieldEnd();
      if (struct.isSetPvpWinCount()) {
        oprot.writeFieldBegin(PVP_WIN_COUNT_FIELD_DESC);
        oprot.writeI64(struct.pvpWinCount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPvpFalseCount()) {
        oprot.writeFieldBegin(PVP_FALSE_COUNT_FIELD_DESC);
        oprot.writeI64(struct.pvpFalseCount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPveWinCount()) {
        oprot.writeFieldBegin(PVE_WIN_COUNT_FIELD_DESC);
        oprot.writeI64(struct.pveWinCount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPveFalseCount()) {
        oprot.writeFieldBegin(PVE_FALSE_COUNT_FIELD_DESC);
        oprot.writeI64(struct.pveFalseCount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetDespoilAmount()) {
        oprot.writeFieldBegin(DESPOIL_AMOUNT_FIELD_DESC);
        oprot.writeI64(struct.despoilAmount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetResHelpAmount()) {
        oprot.writeFieldBegin(RES_HELP_AMOUNT_FIELD_DESC);
        oprot.writeI64(struct.resHelpAmount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetResHelpReceivedAmount()) {
        oprot.writeFieldBegin(RES_HELP_RECEIVED_AMOUNT_FIELD_DESC);
        oprot.writeI64(struct.resHelpReceivedAmount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPvpOffenseWinCount()) {
        oprot.writeFieldBegin(PVP_OFFENSE_WIN_COUNT_FIELD_DESC);
        oprot.writeI64(struct.pvpOffenseWinCount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPvpOffenseLoseCount()) {
        oprot.writeFieldBegin(PVP_OFFENSE_LOSE_COUNT_FIELD_DESC);
        oprot.writeI64(struct.pvpOffenseLoseCount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPvpDefenseWinCount()) {
        oprot.writeFieldBegin(PVP_DEFENSE_WIN_COUNT_FIELD_DESC);
        oprot.writeI64(struct.pvpDefenseWinCount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPvpDefenseLoseCount()) {
        oprot.writeFieldBegin(PVP_DEFENSE_LOSE_COUNT_FIELD_DESC);
        oprot.writeI64(struct.pvpDefenseLoseCount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPvpWinRatio()) {
        oprot.writeFieldBegin(PVP_WIN_RATIO_FIELD_DESC);
        oprot.writeDouble(struct.pvpWinRatio);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcCommanderInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcCommanderInfoTupleScheme getScheme() {
      return new GcCommanderInfoTupleScheme();
    }
  }

  private static class GcCommanderInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcCommanderInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcCommanderInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.scoutTimes);
      oprot.writeI64(struct.resourceGathered);
      oprot.writeI64(struct.resourceAssistance);
      oprot.writeI64(struct.allianceHelpTimes);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetBattleNum()) {
        optionals.set(0);
      }
      if (struct.isSetBattleWin()) {
        optionals.set(1);
      }
      if (struct.isSetBattleKillPvpEnemy()) {
        optionals.set(2);
      }
      if (struct.isSetBattleDeadEnemy()) {
        optionals.set(3);
      }
      if (struct.isSetCureSoldierNum()) {
        optionals.set(4);
      }
      if (struct.isSetBattleKillPveEnemy()) {
        optionals.set(5);
      }
      if (struct.isSetPvpWinCount()) {
        optionals.set(6);
      }
      if (struct.isSetPvpFalseCount()) {
        optionals.set(7);
      }
      if (struct.isSetPveWinCount()) {
        optionals.set(8);
      }
      if (struct.isSetPveFalseCount()) {
        optionals.set(9);
      }
      if (struct.isSetDespoilAmount()) {
        optionals.set(10);
      }
      if (struct.isSetResHelpAmount()) {
        optionals.set(11);
      }
      if (struct.isSetResHelpReceivedAmount()) {
        optionals.set(12);
      }
      if (struct.isSetPvpOffenseWinCount()) {
        optionals.set(13);
      }
      if (struct.isSetPvpOffenseLoseCount()) {
        optionals.set(14);
      }
      if (struct.isSetPvpDefenseWinCount()) {
        optionals.set(15);
      }
      if (struct.isSetPvpDefenseLoseCount()) {
        optionals.set(16);
      }
      if (struct.isSetPvpWinRatio()) {
        optionals.set(17);
      }
      oprot.writeBitSet(optionals, 18);
      if (struct.isSetBattleNum()) {
        oprot.writeI64(struct.battleNum);
      }
      if (struct.isSetBattleWin()) {
        oprot.writeI64(struct.battleWin);
      }
      if (struct.isSetBattleKillPvpEnemy()) {
        oprot.writeI64(struct.battleKillPvpEnemy);
      }
      if (struct.isSetBattleDeadEnemy()) {
        oprot.writeI64(struct.battleDeadEnemy);
      }
      if (struct.isSetCureSoldierNum()) {
        oprot.writeI64(struct.cureSoldierNum);
      }
      if (struct.isSetBattleKillPveEnemy()) {
        oprot.writeI64(struct.battleKillPveEnemy);
      }
      if (struct.isSetPvpWinCount()) {
        oprot.writeI64(struct.pvpWinCount);
      }
      if (struct.isSetPvpFalseCount()) {
        oprot.writeI64(struct.pvpFalseCount);
      }
      if (struct.isSetPveWinCount()) {
        oprot.writeI64(struct.pveWinCount);
      }
      if (struct.isSetPveFalseCount()) {
        oprot.writeI64(struct.pveFalseCount);
      }
      if (struct.isSetDespoilAmount()) {
        oprot.writeI64(struct.despoilAmount);
      }
      if (struct.isSetResHelpAmount()) {
        oprot.writeI64(struct.resHelpAmount);
      }
      if (struct.isSetResHelpReceivedAmount()) {
        oprot.writeI64(struct.resHelpReceivedAmount);
      }
      if (struct.isSetPvpOffenseWinCount()) {
        oprot.writeI64(struct.pvpOffenseWinCount);
      }
      if (struct.isSetPvpOffenseLoseCount()) {
        oprot.writeI64(struct.pvpOffenseLoseCount);
      }
      if (struct.isSetPvpDefenseWinCount()) {
        oprot.writeI64(struct.pvpDefenseWinCount);
      }
      if (struct.isSetPvpDefenseLoseCount()) {
        oprot.writeI64(struct.pvpDefenseLoseCount);
      }
      if (struct.isSetPvpWinRatio()) {
        oprot.writeDouble(struct.pvpWinRatio);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcCommanderInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.scoutTimes = iprot.readI64();
      struct.setScoutTimesIsSet(true);
      struct.resourceGathered = iprot.readI64();
      struct.setResourceGatheredIsSet(true);
      struct.resourceAssistance = iprot.readI64();
      struct.setResourceAssistanceIsSet(true);
      struct.allianceHelpTimes = iprot.readI64();
      struct.setAllianceHelpTimesIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(18);
      if (incoming.get(0)) {
        struct.battleNum = iprot.readI64();
        struct.setBattleNumIsSet(true);
      }
      if (incoming.get(1)) {
        struct.battleWin = iprot.readI64();
        struct.setBattleWinIsSet(true);
      }
      if (incoming.get(2)) {
        struct.battleKillPvpEnemy = iprot.readI64();
        struct.setBattleKillPvpEnemyIsSet(true);
      }
      if (incoming.get(3)) {
        struct.battleDeadEnemy = iprot.readI64();
        struct.setBattleDeadEnemyIsSet(true);
      }
      if (incoming.get(4)) {
        struct.cureSoldierNum = iprot.readI64();
        struct.setCureSoldierNumIsSet(true);
      }
      if (incoming.get(5)) {
        struct.battleKillPveEnemy = iprot.readI64();
        struct.setBattleKillPveEnemyIsSet(true);
      }
      if (incoming.get(6)) {
        struct.pvpWinCount = iprot.readI64();
        struct.setPvpWinCountIsSet(true);
      }
      if (incoming.get(7)) {
        struct.pvpFalseCount = iprot.readI64();
        struct.setPvpFalseCountIsSet(true);
      }
      if (incoming.get(8)) {
        struct.pveWinCount = iprot.readI64();
        struct.setPveWinCountIsSet(true);
      }
      if (incoming.get(9)) {
        struct.pveFalseCount = iprot.readI64();
        struct.setPveFalseCountIsSet(true);
      }
      if (incoming.get(10)) {
        struct.despoilAmount = iprot.readI64();
        struct.setDespoilAmountIsSet(true);
      }
      if (incoming.get(11)) {
        struct.resHelpAmount = iprot.readI64();
        struct.setResHelpAmountIsSet(true);
      }
      if (incoming.get(12)) {
        struct.resHelpReceivedAmount = iprot.readI64();
        struct.setResHelpReceivedAmountIsSet(true);
      }
      if (incoming.get(13)) {
        struct.pvpOffenseWinCount = iprot.readI64();
        struct.setPvpOffenseWinCountIsSet(true);
      }
      if (incoming.get(14)) {
        struct.pvpOffenseLoseCount = iprot.readI64();
        struct.setPvpOffenseLoseCountIsSet(true);
      }
      if (incoming.get(15)) {
        struct.pvpDefenseWinCount = iprot.readI64();
        struct.setPvpDefenseWinCountIsSet(true);
      }
      if (incoming.get(16)) {
        struct.pvpDefenseLoseCount = iprot.readI64();
        struct.setPvpDefenseLoseCountIsSet(true);
      }
      if (incoming.get(17)) {
        struct.pvpWinRatio = iprot.readDouble();
        struct.setPvpWinRatioIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

