/**
 * Autogenerated by <PERSON>hrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsAllianceBuyFeastRecord implements org.apache.thrift.TBase<PsAllianceBuyFeastRecord, PsAllianceBuyFeastRecord._Fields>, java.io.Serializable, Cloneable, Comparable<PsAllianceBuyFeastRecord> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsAllianceBuyFeastRecord");

  private static final org.apache.thrift.protocol.TField ROLE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("roleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField BUY_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("buyTime", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField FEAST_GROUP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("feastGroupId", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("num", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField ENTITY_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("entityId", org.apache.thrift.protocol.TType.I64, (short)5);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsAllianceBuyFeastRecordStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsAllianceBuyFeastRecordTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable PsRoleSimpleInfo roleInfo; // optional
  public long buyTime; // optional
  public int feastGroupId; // optional
  public int num; // optional
  public long entityId; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ROLE_INFO((short)1, "roleInfo"),
    BUY_TIME((short)2, "buyTime"),
    FEAST_GROUP_ID((short)3, "feastGroupId"),
    NUM((short)4, "num"),
    ENTITY_ID((short)5, "entityId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROLE_INFO
          return ROLE_INFO;
        case 2: // BUY_TIME
          return BUY_TIME;
        case 3: // FEAST_GROUP_ID
          return FEAST_GROUP_ID;
        case 4: // NUM
          return NUM;
        case 5: // ENTITY_ID
          return ENTITY_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BUYTIME_ISSET_ID = 0;
  private static final int __FEASTGROUPID_ISSET_ID = 1;
  private static final int __NUM_ISSET_ID = 2;
  private static final int __ENTITYID_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ROLE_INFO,_Fields.BUY_TIME,_Fields.FEAST_GROUP_ID,_Fields.NUM,_Fields.ENTITY_ID};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROLE_INFO, new org.apache.thrift.meta_data.FieldMetaData("roleInfo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsRoleSimpleInfo.class)));
    tmpMap.put(_Fields.BUY_TIME, new org.apache.thrift.meta_data.FieldMetaData("buyTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.FEAST_GROUP_ID, new org.apache.thrift.meta_data.FieldMetaData("feastGroupId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.NUM, new org.apache.thrift.meta_data.FieldMetaData("num", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ENTITY_ID, new org.apache.thrift.meta_data.FieldMetaData("entityId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsAllianceBuyFeastRecord.class, metaDataMap);
  }

  public PsAllianceBuyFeastRecord() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsAllianceBuyFeastRecord(PsAllianceBuyFeastRecord other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetRoleInfo()) {
      this.roleInfo = new PsRoleSimpleInfo(other.roleInfo);
    }
    this.buyTime = other.buyTime;
    this.feastGroupId = other.feastGroupId;
    this.num = other.num;
    this.entityId = other.entityId;
  }

  public PsAllianceBuyFeastRecord deepCopy() {
    return new PsAllianceBuyFeastRecord(this);
  }

  @Override
  public void clear() {
    this.roleInfo = null;
    setBuyTimeIsSet(false);
    this.buyTime = 0;
    setFeastGroupIdIsSet(false);
    this.feastGroupId = 0;
    setNumIsSet(false);
    this.num = 0;
    setEntityIdIsSet(false);
    this.entityId = 0;
  }

  @org.apache.thrift.annotation.Nullable
  public PsRoleSimpleInfo getRoleInfo() {
    return this.roleInfo;
  }

  public PsAllianceBuyFeastRecord setRoleInfo(@org.apache.thrift.annotation.Nullable PsRoleSimpleInfo roleInfo) {
    this.roleInfo = roleInfo;
    return this;
  }

  public void unsetRoleInfo() {
    this.roleInfo = null;
  }

  /** Returns true if field roleInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleInfo() {
    return this.roleInfo != null;
  }

  public void setRoleInfoIsSet(boolean value) {
    if (!value) {
      this.roleInfo = null;
    }
  }

  public long getBuyTime() {
    return this.buyTime;
  }

  public PsAllianceBuyFeastRecord setBuyTime(long buyTime) {
    this.buyTime = buyTime;
    setBuyTimeIsSet(true);
    return this;
  }

  public void unsetBuyTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BUYTIME_ISSET_ID);
  }

  /** Returns true if field buyTime is set (has been assigned a value) and false otherwise */
  public boolean isSetBuyTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BUYTIME_ISSET_ID);
  }

  public void setBuyTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BUYTIME_ISSET_ID, value);
  }

  public int getFeastGroupId() {
    return this.feastGroupId;
  }

  public PsAllianceBuyFeastRecord setFeastGroupId(int feastGroupId) {
    this.feastGroupId = feastGroupId;
    setFeastGroupIdIsSet(true);
    return this;
  }

  public void unsetFeastGroupId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __FEASTGROUPID_ISSET_ID);
  }

  /** Returns true if field feastGroupId is set (has been assigned a value) and false otherwise */
  public boolean isSetFeastGroupId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __FEASTGROUPID_ISSET_ID);
  }

  public void setFeastGroupIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __FEASTGROUPID_ISSET_ID, value);
  }

  public int getNum() {
    return this.num;
  }

  public PsAllianceBuyFeastRecord setNum(int num) {
    this.num = num;
    setNumIsSet(true);
    return this;
  }

  public void unsetNum() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __NUM_ISSET_ID);
  }

  /** Returns true if field num is set (has been assigned a value) and false otherwise */
  public boolean isSetNum() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __NUM_ISSET_ID);
  }

  public void setNumIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __NUM_ISSET_ID, value);
  }

  public long getEntityId() {
    return this.entityId;
  }

  public PsAllianceBuyFeastRecord setEntityId(long entityId) {
    this.entityId = entityId;
    setEntityIdIsSet(true);
    return this;
  }

  public void unsetEntityId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ENTITYID_ISSET_ID);
  }

  /** Returns true if field entityId is set (has been assigned a value) and false otherwise */
  public boolean isSetEntityId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ENTITYID_ISSET_ID);
  }

  public void setEntityIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ENTITYID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ROLE_INFO:
      if (value == null) {
        unsetRoleInfo();
      } else {
        setRoleInfo((PsRoleSimpleInfo)value);
      }
      break;

    case BUY_TIME:
      if (value == null) {
        unsetBuyTime();
      } else {
        setBuyTime((java.lang.Long)value);
      }
      break;

    case FEAST_GROUP_ID:
      if (value == null) {
        unsetFeastGroupId();
      } else {
        setFeastGroupId((java.lang.Integer)value);
      }
      break;

    case NUM:
      if (value == null) {
        unsetNum();
      } else {
        setNum((java.lang.Integer)value);
      }
      break;

    case ENTITY_ID:
      if (value == null) {
        unsetEntityId();
      } else {
        setEntityId((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ROLE_INFO:
      return getRoleInfo();

    case BUY_TIME:
      return getBuyTime();

    case FEAST_GROUP_ID:
      return getFeastGroupId();

    case NUM:
      return getNum();

    case ENTITY_ID:
      return getEntityId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ROLE_INFO:
      return isSetRoleInfo();
    case BUY_TIME:
      return isSetBuyTime();
    case FEAST_GROUP_ID:
      return isSetFeastGroupId();
    case NUM:
      return isSetNum();
    case ENTITY_ID:
      return isSetEntityId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsAllianceBuyFeastRecord)
      return this.equals((PsAllianceBuyFeastRecord)that);
    return false;
  }

  public boolean equals(PsAllianceBuyFeastRecord that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_roleInfo = true && this.isSetRoleInfo();
    boolean that_present_roleInfo = true && that.isSetRoleInfo();
    if (this_present_roleInfo || that_present_roleInfo) {
      if (!(this_present_roleInfo && that_present_roleInfo))
        return false;
      if (!this.roleInfo.equals(that.roleInfo))
        return false;
    }

    boolean this_present_buyTime = true && this.isSetBuyTime();
    boolean that_present_buyTime = true && that.isSetBuyTime();
    if (this_present_buyTime || that_present_buyTime) {
      if (!(this_present_buyTime && that_present_buyTime))
        return false;
      if (this.buyTime != that.buyTime)
        return false;
    }

    boolean this_present_feastGroupId = true && this.isSetFeastGroupId();
    boolean that_present_feastGroupId = true && that.isSetFeastGroupId();
    if (this_present_feastGroupId || that_present_feastGroupId) {
      if (!(this_present_feastGroupId && that_present_feastGroupId))
        return false;
      if (this.feastGroupId != that.feastGroupId)
        return false;
    }

    boolean this_present_num = true && this.isSetNum();
    boolean that_present_num = true && that.isSetNum();
    if (this_present_num || that_present_num) {
      if (!(this_present_num && that_present_num))
        return false;
      if (this.num != that.num)
        return false;
    }

    boolean this_present_entityId = true && this.isSetEntityId();
    boolean that_present_entityId = true && that.isSetEntityId();
    if (this_present_entityId || that_present_entityId) {
      if (!(this_present_entityId && that_present_entityId))
        return false;
      if (this.entityId != that.entityId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetRoleInfo()) ? 131071 : 524287);
    if (isSetRoleInfo())
      hashCode = hashCode * 8191 + roleInfo.hashCode();

    hashCode = hashCode * 8191 + ((isSetBuyTime()) ? 131071 : 524287);
    if (isSetBuyTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(buyTime);

    hashCode = hashCode * 8191 + ((isSetFeastGroupId()) ? 131071 : 524287);
    if (isSetFeastGroupId())
      hashCode = hashCode * 8191 + feastGroupId;

    hashCode = hashCode * 8191 + ((isSetNum()) ? 131071 : 524287);
    if (isSetNum())
      hashCode = hashCode * 8191 + num;

    hashCode = hashCode * 8191 + ((isSetEntityId()) ? 131071 : 524287);
    if (isSetEntityId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(entityId);

    return hashCode;
  }

  @Override
  public int compareTo(PsAllianceBuyFeastRecord other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRoleInfo(), other.isSetRoleInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleInfo, other.roleInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBuyTime(), other.isSetBuyTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBuyTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.buyTime, other.buyTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetFeastGroupId(), other.isSetFeastGroupId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFeastGroupId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.feastGroupId, other.feastGroupId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetNum(), other.isSetNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.num, other.num);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEntityId(), other.isSetEntityId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEntityId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.entityId, other.entityId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsAllianceBuyFeastRecord(");
    boolean first = true;

    if (isSetRoleInfo()) {
      sb.append("roleInfo:");
      if (this.roleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.roleInfo);
      }
      first = false;
    }
    if (isSetBuyTime()) {
      if (!first) sb.append(", ");
      sb.append("buyTime:");
      sb.append(this.buyTime);
      first = false;
    }
    if (isSetFeastGroupId()) {
      if (!first) sb.append(", ");
      sb.append("feastGroupId:");
      sb.append(this.feastGroupId);
      first = false;
    }
    if (isSetNum()) {
      if (!first) sb.append(", ");
      sb.append("num:");
      sb.append(this.num);
      first = false;
    }
    if (isSetEntityId()) {
      if (!first) sb.append(", ");
      sb.append("entityId:");
      sb.append(this.entityId);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (roleInfo != null) {
      roleInfo.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsAllianceBuyFeastRecordStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsAllianceBuyFeastRecordStandardScheme getScheme() {
      return new PsAllianceBuyFeastRecordStandardScheme();
    }
  }

  private static class PsAllianceBuyFeastRecordStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsAllianceBuyFeastRecord> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsAllianceBuyFeastRecord struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROLE_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.roleInfo = new PsRoleSimpleInfo();
              struct.roleInfo.read(iprot);
              struct.setRoleInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BUY_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.buyTime = iprot.readI64();
              struct.setBuyTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // FEAST_GROUP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.feastGroupId = iprot.readI32();
              struct.setFeastGroupIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.num = iprot.readI32();
              struct.setNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ENTITY_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.entityId = iprot.readI64();
              struct.setEntityIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsAllianceBuyFeastRecord struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.roleInfo != null) {
        if (struct.isSetRoleInfo()) {
          oprot.writeFieldBegin(ROLE_INFO_FIELD_DESC);
          struct.roleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetBuyTime()) {
        oprot.writeFieldBegin(BUY_TIME_FIELD_DESC);
        oprot.writeI64(struct.buyTime);
        oprot.writeFieldEnd();
      }
      if (struct.isSetFeastGroupId()) {
        oprot.writeFieldBegin(FEAST_GROUP_ID_FIELD_DESC);
        oprot.writeI32(struct.feastGroupId);
        oprot.writeFieldEnd();
      }
      if (struct.isSetNum()) {
        oprot.writeFieldBegin(NUM_FIELD_DESC);
        oprot.writeI32(struct.num);
        oprot.writeFieldEnd();
      }
      if (struct.isSetEntityId()) {
        oprot.writeFieldBegin(ENTITY_ID_FIELD_DESC);
        oprot.writeI64(struct.entityId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsAllianceBuyFeastRecordTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsAllianceBuyFeastRecordTupleScheme getScheme() {
      return new PsAllianceBuyFeastRecordTupleScheme();
    }
  }

  private static class PsAllianceBuyFeastRecordTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsAllianceBuyFeastRecord> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsAllianceBuyFeastRecord struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRoleInfo()) {
        optionals.set(0);
      }
      if (struct.isSetBuyTime()) {
        optionals.set(1);
      }
      if (struct.isSetFeastGroupId()) {
        optionals.set(2);
      }
      if (struct.isSetNum()) {
        optionals.set(3);
      }
      if (struct.isSetEntityId()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetRoleInfo()) {
        struct.roleInfo.write(oprot);
      }
      if (struct.isSetBuyTime()) {
        oprot.writeI64(struct.buyTime);
      }
      if (struct.isSetFeastGroupId()) {
        oprot.writeI32(struct.feastGroupId);
      }
      if (struct.isSetNum()) {
        oprot.writeI32(struct.num);
      }
      if (struct.isSetEntityId()) {
        oprot.writeI64(struct.entityId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsAllianceBuyFeastRecord struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.roleInfo = new PsRoleSimpleInfo();
        struct.roleInfo.read(iprot);
        struct.setRoleInfoIsSet(true);
      }
      if (incoming.get(1)) {
        struct.buyTime = iprot.readI64();
        struct.setBuyTimeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.feastGroupId = iprot.readI32();
        struct.setFeastGroupIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.num = iprot.readI32();
        struct.setNumIsSet(true);
      }
      if (incoming.get(4)) {
        struct.entityId = iprot.readI64();
        struct.setEntityIdIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

