/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 约战：创建
 * @Message(3967)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcEngageCreateRoom implements org.apache.thrift.TBase<GcEngageCreateRoom, GcEngageCreateRoom._Fields>, java.io.Serializable, Cloneable, Comparable<GcEngageCreateRoom> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcEngageCreateRoom");

  private static final org.apache.thrift.protocol.TField INDEXS_FIELD_DESC = new org.apache.thrift.protocol.TField("indexs", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField RESULTS_FIELD_DESC = new org.apache.thrift.protocol.TField("results", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcEngageCreateRoomStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcEngageCreateRoomTupleSchemeFactory();

  /**
   * 场次
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.Integer> indexs; // optional
  /**
   * 每一场的结果
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode> results; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 场次
     */
    INDEXS((short)1, "indexs"),
    /**
     * 每一场的结果
     */
    RESULTS((short)2, "results");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // INDEXS
          return INDEXS;
        case 2: // RESULTS
          return RESULTS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.INDEXS,_Fields.RESULTS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.INDEXS, new org.apache.thrift.meta_data.FieldMetaData("indexs", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    tmpMap.put(_Fields.RESULTS, new org.apache.thrift.meta_data.FieldMetaData("results", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsRZEErrorCode.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcEngageCreateRoom.class, metaDataMap);
  }

  public GcEngageCreateRoom() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcEngageCreateRoom(GcEngageCreateRoom other) {
    if (other.isSetIndexs()) {
      java.util.List<java.lang.Integer> __this__indexs = new java.util.ArrayList<java.lang.Integer>(other.indexs);
      this.indexs = __this__indexs;
    }
    if (other.isSetResults()) {
      java.util.List<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode> __this__results = new java.util.ArrayList<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode>(other.results.size());
      for (com.lc.billion.icefire.protocol.constant.PsRZEErrorCode other_element : other.results) {
        __this__results.add(other_element);
      }
      this.results = __this__results;
    }
  }

  public GcEngageCreateRoom deepCopy() {
    return new GcEngageCreateRoom(this);
  }

  @Override
  public void clear() {
    this.indexs = null;
    this.results = null;
  }

  public int getIndexsSize() {
    return (this.indexs == null) ? 0 : this.indexs.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.Integer> getIndexsIterator() {
    return (this.indexs == null) ? null : this.indexs.iterator();
  }

  public void addToIndexs(int elem) {
    if (this.indexs == null) {
      this.indexs = new java.util.ArrayList<java.lang.Integer>();
    }
    this.indexs.add(elem);
  }

  /**
   * 场次
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.Integer> getIndexs() {
    return this.indexs;
  }

  /**
   * 场次
   */
  public GcEngageCreateRoom setIndexs(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.Integer> indexs) {
    this.indexs = indexs;
    return this;
  }

  public void unsetIndexs() {
    this.indexs = null;
  }

  /** Returns true if field indexs is set (has been assigned a value) and false otherwise */
  public boolean isSetIndexs() {
    return this.indexs != null;
  }

  public void setIndexsIsSet(boolean value) {
    if (!value) {
      this.indexs = null;
    }
  }

  public int getResultsSize() {
    return (this.results == null) ? 0 : this.results.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode> getResultsIterator() {
    return (this.results == null) ? null : this.results.iterator();
  }

  public void addToResults(com.lc.billion.icefire.protocol.constant.PsRZEErrorCode elem) {
    if (this.results == null) {
      this.results = new java.util.ArrayList<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode>();
    }
    this.results.add(elem);
  }

  /**
   * 每一场的结果
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode> getResults() {
    return this.results;
  }

  /**
   * 每一场的结果
   */
  public GcEngageCreateRoom setResults(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode> results) {
    this.results = results;
    return this;
  }

  public void unsetResults() {
    this.results = null;
  }

  /** Returns true if field results is set (has been assigned a value) and false otherwise */
  public boolean isSetResults() {
    return this.results != null;
  }

  public void setResultsIsSet(boolean value) {
    if (!value) {
      this.results = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case INDEXS:
      if (value == null) {
        unsetIndexs();
      } else {
        setIndexs((java.util.List<java.lang.Integer>)value);
      }
      break;

    case RESULTS:
      if (value == null) {
        unsetResults();
      } else {
        setResults((java.util.List<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case INDEXS:
      return getIndexs();

    case RESULTS:
      return getResults();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case INDEXS:
      return isSetIndexs();
    case RESULTS:
      return isSetResults();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcEngageCreateRoom)
      return this.equals((GcEngageCreateRoom)that);
    return false;
  }

  public boolean equals(GcEngageCreateRoom that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_indexs = true && this.isSetIndexs();
    boolean that_present_indexs = true && that.isSetIndexs();
    if (this_present_indexs || that_present_indexs) {
      if (!(this_present_indexs && that_present_indexs))
        return false;
      if (!this.indexs.equals(that.indexs))
        return false;
    }

    boolean this_present_results = true && this.isSetResults();
    boolean that_present_results = true && that.isSetResults();
    if (this_present_results || that_present_results) {
      if (!(this_present_results && that_present_results))
        return false;
      if (!this.results.equals(that.results))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetIndexs()) ? 131071 : 524287);
    if (isSetIndexs())
      hashCode = hashCode * 8191 + indexs.hashCode();

    hashCode = hashCode * 8191 + ((isSetResults()) ? 131071 : 524287);
    if (isSetResults())
      hashCode = hashCode * 8191 + results.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcEngageCreateRoom other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetIndexs(), other.isSetIndexs());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIndexs()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.indexs, other.indexs);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetResults(), other.isSetResults());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResults()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.results, other.results);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcEngageCreateRoom(");
    boolean first = true;

    if (isSetIndexs()) {
      sb.append("indexs:");
      if (this.indexs == null) {
        sb.append("null");
      } else {
        sb.append(this.indexs);
      }
      first = false;
    }
    if (isSetResults()) {
      if (!first) sb.append(", ");
      sb.append("results:");
      if (this.results == null) {
        sb.append("null");
      } else {
        sb.append(this.results);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcEngageCreateRoomStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcEngageCreateRoomStandardScheme getScheme() {
      return new GcEngageCreateRoomStandardScheme();
    }
  }

  private static class GcEngageCreateRoomStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcEngageCreateRoom> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcEngageCreateRoom struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // INDEXS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.indexs = new java.util.ArrayList<java.lang.Integer>(_list0.size);
                int _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = iprot.readI32();
                  struct.indexs.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setIndexsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RESULTS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.results = new java.util.ArrayList<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode>(_list3.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsRZEErrorCode _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = com.lc.billion.icefire.protocol.constant.PsRZEErrorCode.findByValue(iprot.readI32());
                  if (_elem4 != null)
                  {
                    struct.results.add(_elem4);
                  }
                }
                iprot.readListEnd();
              }
              struct.setResultsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcEngageCreateRoom struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.indexs != null) {
        if (struct.isSetIndexs()) {
          oprot.writeFieldBegin(INDEXS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, struct.indexs.size()));
            for (int _iter6 : struct.indexs)
            {
              oprot.writeI32(_iter6);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.results != null) {
        if (struct.isSetResults()) {
          oprot.writeFieldBegin(RESULTS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, struct.results.size()));
            for (com.lc.billion.icefire.protocol.constant.PsRZEErrorCode _iter7 : struct.results)
            {
              oprot.writeI32(_iter7.getValue());
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcEngageCreateRoomTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcEngageCreateRoomTupleScheme getScheme() {
      return new GcEngageCreateRoomTupleScheme();
    }
  }

  private static class GcEngageCreateRoomTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcEngageCreateRoom> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcEngageCreateRoom struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetIndexs()) {
        optionals.set(0);
      }
      if (struct.isSetResults()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetIndexs()) {
        {
          oprot.writeI32(struct.indexs.size());
          for (int _iter8 : struct.indexs)
          {
            oprot.writeI32(_iter8);
          }
        }
      }
      if (struct.isSetResults()) {
        {
          oprot.writeI32(struct.results.size());
          for (com.lc.billion.icefire.protocol.constant.PsRZEErrorCode _iter9 : struct.results)
          {
            oprot.writeI32(_iter9.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcEngageCreateRoom struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list10 = iprot.readListBegin(org.apache.thrift.protocol.TType.I32);
          struct.indexs = new java.util.ArrayList<java.lang.Integer>(_list10.size);
          int _elem11;
          for (int _i12 = 0; _i12 < _list10.size; ++_i12)
          {
            _elem11 = iprot.readI32();
            struct.indexs.add(_elem11);
          }
        }
        struct.setIndexsIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list13 = iprot.readListBegin(org.apache.thrift.protocol.TType.I32);
          struct.results = new java.util.ArrayList<com.lc.billion.icefire.protocol.constant.PsRZEErrorCode>(_list13.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsRZEErrorCode _elem14;
          for (int _i15 = 0; _i15 < _list13.size; ++_i15)
          {
            _elem14 = com.lc.billion.icefire.protocol.constant.PsRZEErrorCode.findByValue(iprot.readI32());
            if (_elem14 != null)
            {
              struct.results.add(_elem14);
            }
          }
        }
        struct.setResultsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

