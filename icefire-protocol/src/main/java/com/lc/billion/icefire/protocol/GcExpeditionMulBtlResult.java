/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 多队讨伐战斗返回
 * @Message(4673)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcExpeditionMulBtlResult implements org.apache.thrift.TBase<GcExpeditionMulBtlResult, GcExpeditionMulBtlResult._Fields>, java.io.Serializable, Cloneable, Comparable<GcExpeditionMulBtlResult> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcExpeditionMulBtlResult");

  private static final org.apache.thrift.protocol.TField ERROR_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("errorCode", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField IS_WIN_FIELD_DESC = new org.apache.thrift.protocol.TField("isWin", org.apache.thrift.protocol.TType.BOOL, (short)2);
  private static final org.apache.thrift.protocol.TField ITEMS_FIELD_DESC = new org.apache.thrift.protocol.TField("items", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField NEXT_LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("nextLevel", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField TEAM_NUMBER_FIELD_DESC = new org.apache.thrift.protocol.TField("teamNumber", org.apache.thrift.protocol.TType.I32, (short)5);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcExpeditionMulBtlResultStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcExpeditionMulBtlResultTupleSchemeFactory();

  public int errorCode; // required
  public boolean isWin; // required
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> items; // required
  public int nextLevel; // optional
  public int teamNumber; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ERROR_CODE((short)1, "errorCode"),
    IS_WIN((short)2, "isWin"),
    ITEMS((short)3, "items"),
    NEXT_LEVEL((short)4, "nextLevel"),
    TEAM_NUMBER((short)5, "teamNumber");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ERROR_CODE
          return ERROR_CODE;
        case 2: // IS_WIN
          return IS_WIN;
        case 3: // ITEMS
          return ITEMS;
        case 4: // NEXT_LEVEL
          return NEXT_LEVEL;
        case 5: // TEAM_NUMBER
          return TEAM_NUMBER;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ERRORCODE_ISSET_ID = 0;
  private static final int __ISWIN_ISSET_ID = 1;
  private static final int __NEXTLEVEL_ISSET_ID = 2;
  private static final int __TEAMNUMBER_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.NEXT_LEVEL,_Fields.TEAM_NUMBER};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ERROR_CODE, new org.apache.thrift.meta_data.FieldMetaData("errorCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IS_WIN, new org.apache.thrift.meta_data.FieldMetaData("isWin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.ITEMS, new org.apache.thrift.meta_data.FieldMetaData("items", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSimpleItem.class))));
    tmpMap.put(_Fields.NEXT_LEVEL, new org.apache.thrift.meta_data.FieldMetaData("nextLevel", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TEAM_NUMBER, new org.apache.thrift.meta_data.FieldMetaData("teamNumber", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcExpeditionMulBtlResult.class, metaDataMap);
  }

  public GcExpeditionMulBtlResult() {
  }

  public GcExpeditionMulBtlResult(
    int errorCode,
    boolean isWin,
    java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> items)
  {
    this();
    this.errorCode = errorCode;
    setErrorCodeIsSet(true);
    this.isWin = isWin;
    setIsWinIsSet(true);
    this.items = items;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcExpeditionMulBtlResult(GcExpeditionMulBtlResult other) {
    __isset_bitfield = other.__isset_bitfield;
    this.errorCode = other.errorCode;
    this.isWin = other.isWin;
    if (other.isSetItems()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> __this__items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(other.items.size());
      for (com.lc.billion.icefire.protocol.structure.PsSimpleItem other_element : other.items) {
        __this__items.add(new com.lc.billion.icefire.protocol.structure.PsSimpleItem(other_element));
      }
      this.items = __this__items;
    }
    this.nextLevel = other.nextLevel;
    this.teamNumber = other.teamNumber;
  }

  public GcExpeditionMulBtlResult deepCopy() {
    return new GcExpeditionMulBtlResult(this);
  }

  @Override
  public void clear() {
    setErrorCodeIsSet(false);
    this.errorCode = 0;
    setIsWinIsSet(false);
    this.isWin = false;
    this.items = null;
    setNextLevelIsSet(false);
    this.nextLevel = 0;
    setTeamNumberIsSet(false);
    this.teamNumber = 0;
  }

  public int getErrorCode() {
    return this.errorCode;
  }

  public GcExpeditionMulBtlResult setErrorCode(int errorCode) {
    this.errorCode = errorCode;
    setErrorCodeIsSet(true);
    return this;
  }

  public void unsetErrorCode() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ERRORCODE_ISSET_ID);
  }

  /** Returns true if field errorCode is set (has been assigned a value) and false otherwise */
  public boolean isSetErrorCode() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ERRORCODE_ISSET_ID);
  }

  public void setErrorCodeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ERRORCODE_ISSET_ID, value);
  }

  public boolean isIsWin() {
    return this.isWin;
  }

  public GcExpeditionMulBtlResult setIsWin(boolean isWin) {
    this.isWin = isWin;
    setIsWinIsSet(true);
    return this;
  }

  public void unsetIsWin() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ISWIN_ISSET_ID);
  }

  /** Returns true if field isWin is set (has been assigned a value) and false otherwise */
  public boolean isSetIsWin() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ISWIN_ISSET_ID);
  }

  public void setIsWinIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ISWIN_ISSET_ID, value);
  }

  public int getItemsSize() {
    return (this.items == null) ? 0 : this.items.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getItemsIterator() {
    return (this.items == null) ? null : this.items.iterator();
  }

  public void addToItems(com.lc.billion.icefire.protocol.structure.PsSimpleItem elem) {
    if (this.items == null) {
      this.items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>();
    }
    this.items.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getItems() {
    return this.items;
  }

  public GcExpeditionMulBtlResult setItems(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> items) {
    this.items = items;
    return this;
  }

  public void unsetItems() {
    this.items = null;
  }

  /** Returns true if field items is set (has been assigned a value) and false otherwise */
  public boolean isSetItems() {
    return this.items != null;
  }

  public void setItemsIsSet(boolean value) {
    if (!value) {
      this.items = null;
    }
  }

  public int getNextLevel() {
    return this.nextLevel;
  }

  public GcExpeditionMulBtlResult setNextLevel(int nextLevel) {
    this.nextLevel = nextLevel;
    setNextLevelIsSet(true);
    return this;
  }

  public void unsetNextLevel() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __NEXTLEVEL_ISSET_ID);
  }

  /** Returns true if field nextLevel is set (has been assigned a value) and false otherwise */
  public boolean isSetNextLevel() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __NEXTLEVEL_ISSET_ID);
  }

  public void setNextLevelIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __NEXTLEVEL_ISSET_ID, value);
  }

  public int getTeamNumber() {
    return this.teamNumber;
  }

  public GcExpeditionMulBtlResult setTeamNumber(int teamNumber) {
    this.teamNumber = teamNumber;
    setTeamNumberIsSet(true);
    return this;
  }

  public void unsetTeamNumber() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TEAMNUMBER_ISSET_ID);
  }

  /** Returns true if field teamNumber is set (has been assigned a value) and false otherwise */
  public boolean isSetTeamNumber() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TEAMNUMBER_ISSET_ID);
  }

  public void setTeamNumberIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TEAMNUMBER_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ERROR_CODE:
      if (value == null) {
        unsetErrorCode();
      } else {
        setErrorCode((java.lang.Integer)value);
      }
      break;

    case IS_WIN:
      if (value == null) {
        unsetIsWin();
      } else {
        setIsWin((java.lang.Boolean)value);
      }
      break;

    case ITEMS:
      if (value == null) {
        unsetItems();
      } else {
        setItems((java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>)value);
      }
      break;

    case NEXT_LEVEL:
      if (value == null) {
        unsetNextLevel();
      } else {
        setNextLevel((java.lang.Integer)value);
      }
      break;

    case TEAM_NUMBER:
      if (value == null) {
        unsetTeamNumber();
      } else {
        setTeamNumber((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ERROR_CODE:
      return getErrorCode();

    case IS_WIN:
      return isIsWin();

    case ITEMS:
      return getItems();

    case NEXT_LEVEL:
      return getNextLevel();

    case TEAM_NUMBER:
      return getTeamNumber();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ERROR_CODE:
      return isSetErrorCode();
    case IS_WIN:
      return isSetIsWin();
    case ITEMS:
      return isSetItems();
    case NEXT_LEVEL:
      return isSetNextLevel();
    case TEAM_NUMBER:
      return isSetTeamNumber();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcExpeditionMulBtlResult)
      return this.equals((GcExpeditionMulBtlResult)that);
    return false;
  }

  public boolean equals(GcExpeditionMulBtlResult that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_errorCode = true;
    boolean that_present_errorCode = true;
    if (this_present_errorCode || that_present_errorCode) {
      if (!(this_present_errorCode && that_present_errorCode))
        return false;
      if (this.errorCode != that.errorCode)
        return false;
    }

    boolean this_present_isWin = true;
    boolean that_present_isWin = true;
    if (this_present_isWin || that_present_isWin) {
      if (!(this_present_isWin && that_present_isWin))
        return false;
      if (this.isWin != that.isWin)
        return false;
    }

    boolean this_present_items = true && this.isSetItems();
    boolean that_present_items = true && that.isSetItems();
    if (this_present_items || that_present_items) {
      if (!(this_present_items && that_present_items))
        return false;
      if (!this.items.equals(that.items))
        return false;
    }

    boolean this_present_nextLevel = true && this.isSetNextLevel();
    boolean that_present_nextLevel = true && that.isSetNextLevel();
    if (this_present_nextLevel || that_present_nextLevel) {
      if (!(this_present_nextLevel && that_present_nextLevel))
        return false;
      if (this.nextLevel != that.nextLevel)
        return false;
    }

    boolean this_present_teamNumber = true && this.isSetTeamNumber();
    boolean that_present_teamNumber = true && that.isSetTeamNumber();
    if (this_present_teamNumber || that_present_teamNumber) {
      if (!(this_present_teamNumber && that_present_teamNumber))
        return false;
      if (this.teamNumber != that.teamNumber)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + errorCode;

    hashCode = hashCode * 8191 + ((isWin) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetItems()) ? 131071 : 524287);
    if (isSetItems())
      hashCode = hashCode * 8191 + items.hashCode();

    hashCode = hashCode * 8191 + ((isSetNextLevel()) ? 131071 : 524287);
    if (isSetNextLevel())
      hashCode = hashCode * 8191 + nextLevel;

    hashCode = hashCode * 8191 + ((isSetTeamNumber()) ? 131071 : 524287);
    if (isSetTeamNumber())
      hashCode = hashCode * 8191 + teamNumber;

    return hashCode;
  }

  @Override
  public int compareTo(GcExpeditionMulBtlResult other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetErrorCode(), other.isSetErrorCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetErrorCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.errorCode, other.errorCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetIsWin(), other.isSetIsWin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsWin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isWin, other.isWin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetItems(), other.isSetItems());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItems()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.items, other.items);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetNextLevel(), other.isSetNextLevel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNextLevel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nextLevel, other.nextLevel);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTeamNumber(), other.isSetTeamNumber());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTeamNumber()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.teamNumber, other.teamNumber);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcExpeditionMulBtlResult(");
    boolean first = true;

    sb.append("errorCode:");
    sb.append(this.errorCode);
    first = false;
    if (!first) sb.append(", ");
    sb.append("isWin:");
    sb.append(this.isWin);
    first = false;
    if (!first) sb.append(", ");
    sb.append("items:");
    if (this.items == null) {
      sb.append("null");
    } else {
      sb.append(this.items);
    }
    first = false;
    if (isSetNextLevel()) {
      if (!first) sb.append(", ");
      sb.append("nextLevel:");
      sb.append(this.nextLevel);
      first = false;
    }
    if (isSetTeamNumber()) {
      if (!first) sb.append(", ");
      sb.append("teamNumber:");
      sb.append(this.teamNumber);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'errorCode' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'isWin' because it's a primitive and you chose the non-beans generator.
    if (items == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'items' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcExpeditionMulBtlResultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcExpeditionMulBtlResultStandardScheme getScheme() {
      return new GcExpeditionMulBtlResultStandardScheme();
    }
  }

  private static class GcExpeditionMulBtlResultStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcExpeditionMulBtlResult> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcExpeditionMulBtlResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ERROR_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.errorCode = iprot.readI32();
              struct.setErrorCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // IS_WIN
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isWin = iprot.readBool();
              struct.setIsWinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ITEMS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                  _elem1.read(iprot);
                  struct.items.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setItemsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // NEXT_LEVEL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.nextLevel = iprot.readI32();
              struct.setNextLevelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // TEAM_NUMBER
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.teamNumber = iprot.readI32();
              struct.setTeamNumberIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetErrorCode()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'errorCode' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetIsWin()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'isWin' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcExpeditionMulBtlResult struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ERROR_CODE_FIELD_DESC);
      oprot.writeI32(struct.errorCode);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(IS_WIN_FIELD_DESC);
      oprot.writeBool(struct.isWin);
      oprot.writeFieldEnd();
      if (struct.items != null) {
        oprot.writeFieldBegin(ITEMS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.items.size()));
          for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter3 : struct.items)
          {
            _iter3.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.isSetNextLevel()) {
        oprot.writeFieldBegin(NEXT_LEVEL_FIELD_DESC);
        oprot.writeI32(struct.nextLevel);
        oprot.writeFieldEnd();
      }
      if (struct.isSetTeamNumber()) {
        oprot.writeFieldBegin(TEAM_NUMBER_FIELD_DESC);
        oprot.writeI32(struct.teamNumber);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcExpeditionMulBtlResultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcExpeditionMulBtlResultTupleScheme getScheme() {
      return new GcExpeditionMulBtlResultTupleScheme();
    }
  }

  private static class GcExpeditionMulBtlResultTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcExpeditionMulBtlResult> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcExpeditionMulBtlResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.errorCode);
      oprot.writeBool(struct.isWin);
      {
        oprot.writeI32(struct.items.size());
        for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter4 : struct.items)
        {
          _iter4.write(oprot);
        }
      }
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetNextLevel()) {
        optionals.set(0);
      }
      if (struct.isSetTeamNumber()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetNextLevel()) {
        oprot.writeI32(struct.nextLevel);
      }
      if (struct.isSetTeamNumber()) {
        oprot.writeI32(struct.teamNumber);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcExpeditionMulBtlResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.errorCode = iprot.readI32();
      struct.setErrorCodeIsSet(true);
      struct.isWin = iprot.readBool();
      struct.setIsWinIsSet(true);
      {
        org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list5.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem6;
        for (int _i7 = 0; _i7 < _list5.size; ++_i7)
        {
          _elem6 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
          _elem6.read(iprot);
          struct.items.add(_elem6);
        }
      }
      struct.setItemsIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.nextLevel = iprot.readI32();
        struct.setNextLevelIsSet(true);
      }
      if (incoming.get(1)) {
        struct.teamNumber = iprot.readI32();
        struct.setTeamNumberIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

