/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsActivityType implements org.apache.thrift.TEnum {
  /**
   * battle pass *
   */
  BATTLE_PASS(10),
  /**
   * 州府战
   */
  REGION_CAPITAL(13),
  /**
   * 联盟boss战 三英战吕布
   */
  ALLIANCE_BOSS_BATTLE(14),
  /**
   * 州府战-决战王城
   */
  REGION_CAPITAL_ROYAL(15),
  /**
   * 王者之路
   */
  CROWNED_KING(16),
  /**
   * 兑换活动 为伴生活动，可能同时开启多个
   */
  EXCHANGE(28),
  /**
   * 连续充值活动
   */
  DAILY_RECHARGE(18),
  /**
   * 低语者活动*
   */
  WHISPERERS(35),
  /**
   * 黑骑士活动*
   */
  DEAD_RISING(40),
  /**
   * GVG *
   */
  GVG(43),
  /**
   * GvG杯赛
   */
  GVG_CUP(50),
  /**
   * 跨服攻城
   */
  CROSS_SERVER_ATTACK(51),
  /**
   * 射击训练
   */
  SHOOTING_TRAINING(56),
  /**
   * 零元购
   */
  FREE_BUY(58),
  /**
   * 团购礼包
   */
  GROUP_BUY_LIBAO(64),
  /**
   * gvg约战
   */
  GVG_ENGAGEMENT(68),
  /**
   * Bingo周年庆活动
   */
  BINGO(71),
  /**
   * 新手七日签到活动
   */
  SEVEN_SIGN(72),
  /**
   * TVT activity
   */
  TVT(74),
  /**
   * 钻石累计消耗活动
   */
  DIAMOND_COUNT_CONSUMER(76),
  /**
   * 傲世群雄 新生的赞礼
   */
  NEW_PLAYER_GIFT(77),
  /**
   * 新最强领主
   */
  NEW_STRONGEST_LORD(78),
  /**
   * 常规提升活动
   */
  COMMON_INC(79),
  /**
   * 分享活动
   */
  SHARE(80),
  /**
   * 游戏圈任务活动
   */
  WECHAT_MISSION(85),
  /**
   * 游戏圈通用活动
   */
  WECHAT_COMMON(86),
  /**
   * 游戏圈通用热门活动
   */
  WECHAT_COMMON_HOT(87),
  /**
   * 微信添加小程序
   */
  WECHAT_COLLECTION(88),
  /**
   * 七擒孟获 *
   */
  SEVEN_CAPTURE(89),
  /**
   * 运营活动
   */
  OPERATIONAL_ACTIVITY(90),
  /**
   * 个人事务列表 *
   */
  AFFAIR_LIST(91),
  /**
   * 同盟角逐
   */
  ALLIANCE_BATTLE(92),
  /**
   * 赛季预热活动
   */
  SEASON_WARM_UP(93),
  /**
   * 司南寻宝（转盘） *
   */
  ICE_WHEEL(95),
  /**
   * 金兰结契 *
   */
  BROTHER_HOOD(96),
  /**
   * 自选礼包 *
   */
  SELECTION_PACK(98),
  /**
   * 连续充值5天 *
   */
  RECHARGE_5_DAYS(99),
  /**
   * 任务类活动 *
   */
  EVENT_MISSION(100),
  /**
   * bp任务类活动 *
   */
  BATTLE_PASS_MISSION(101),
  /**
   * 订阅有礼活动
   */
  WECHAT_SUBSCRIBE(102),
  /**
   * 贸易站活动
   */
  TRADE_POST(103),
  /**
   * 华为小程序
   */
  HUAWEI_COLLECTION(104),
  /**
   * 名驹养成
   */
  HORSE_CULTIVATE(105),
  /**
   * 代币活动
   */
  TOKEN_EVENT(106),
  /**
   * 冰雪节活动
   */
  SNOW_EVENT(107),
  /**
   * 累计充值
   */
  RECHARGE_SCORE(108),
  /**
   * 节日登录
   */
  FESTIVAL_LOGIN(109),
  /**
   * 签到活动
   */
  SIGN_IN(110),
  /**
   * 活动日历
   */
  ACTIVITY_CALENDAR(111),
  /**
   * 红包活动
   */
  REDPACK(113),
  /**
   * 开箱子（翻牌子）
   */
  FESTIVAL_LUCKYBAG(114),
  /**
   * 群雄讨董 *
   */
  WORLD_BOSS_DONGZHUO(200),
  /**
   * 诸侯争霸-争霸总览 *
   */
  VASSAL_OVERVIEW(201),
  /**
   * 诸侯争霸-人气投票 *
   */
  VASSAL_VOTE(202),
  /**
   * 诸侯争霸-兑换商店 *
   */
  VASSAL_EXCHANGE(203),
  /**
   * 支付宝任务活动
   */
  ALIPAY_MISSION(210),
  /**
   * 联盟宴会厅活动
   */
  ALLIANCE_FEAST_HALL(211),
  /**
   * 裂变活动-分享集道具
   */
  ACTIVITY_FISSION(212);

  private final int value;

  private PsActivityType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsActivityType findByValue(int value) { 
    switch (value) {
      case 10:
        return BATTLE_PASS;
      case 13:
        return REGION_CAPITAL;
      case 14:
        return ALLIANCE_BOSS_BATTLE;
      case 15:
        return REGION_CAPITAL_ROYAL;
      case 16:
        return CROWNED_KING;
      case 28:
        return EXCHANGE;
      case 18:
        return DAILY_RECHARGE;
      case 35:
        return WHISPERERS;
      case 40:
        return DEAD_RISING;
      case 43:
        return GVG;
      case 50:
        return GVG_CUP;
      case 51:
        return CROSS_SERVER_ATTACK;
      case 56:
        return SHOOTING_TRAINING;
      case 58:
        return FREE_BUY;
      case 64:
        return GROUP_BUY_LIBAO;
      case 68:
        return GVG_ENGAGEMENT;
      case 71:
        return BINGO;
      case 72:
        return SEVEN_SIGN;
      case 74:
        return TVT;
      case 76:
        return DIAMOND_COUNT_CONSUMER;
      case 77:
        return NEW_PLAYER_GIFT;
      case 78:
        return NEW_STRONGEST_LORD;
      case 79:
        return COMMON_INC;
      case 80:
        return SHARE;
      case 85:
        return WECHAT_MISSION;
      case 86:
        return WECHAT_COMMON;
      case 87:
        return WECHAT_COMMON_HOT;
      case 88:
        return WECHAT_COLLECTION;
      case 89:
        return SEVEN_CAPTURE;
      case 90:
        return OPERATIONAL_ACTIVITY;
      case 91:
        return AFFAIR_LIST;
      case 92:
        return ALLIANCE_BATTLE;
      case 93:
        return SEASON_WARM_UP;
      case 95:
        return ICE_WHEEL;
      case 96:
        return BROTHER_HOOD;
      case 98:
        return SELECTION_PACK;
      case 99:
        return RECHARGE_5_DAYS;
      case 100:
        return EVENT_MISSION;
      case 101:
        return BATTLE_PASS_MISSION;
      case 102:
        return WECHAT_SUBSCRIBE;
      case 103:
        return TRADE_POST;
      case 104:
        return HUAWEI_COLLECTION;
      case 105:
        return HORSE_CULTIVATE;
      case 106:
        return TOKEN_EVENT;
      case 107:
        return SNOW_EVENT;
      case 108:
        return RECHARGE_SCORE;
      case 109:
        return FESTIVAL_LOGIN;
      case 110:
        return SIGN_IN;
      case 111:
        return ACTIVITY_CALENDAR;
      case 113:
        return REDPACK;
      case 114:
        return FESTIVAL_LUCKYBAG;
      case 200:
        return WORLD_BOSS_DONGZHUO;
      case 201:
        return VASSAL_OVERVIEW;
      case 202:
        return VASSAL_VOTE;
      case 203:
        return VASSAL_EXCHANGE;
      case 210:
        return ALIPAY_MISSION;
      case 211:
        return ALLIANCE_FEAST_HALL;
      case 212:
        return ACTIVITY_FISSION;
      default:
        return null;
    }
  }
}
