/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


/**
 * 营救错误码
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsRescueErrorCode implements org.apache.thrift.TEnum {
  /**
   * 成功
   */
  SUCCESS(1),
  /**
   * 条件不满足
   */
  CONDITION_NOT_MET(2),
  /**
   * 奖励已经领取
   */
  ALREADY_GET_REWARD(3);

  private final int value;

  private PsRescueErrorCode(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsRescueErrorCode findByValue(int value) { 
    switch (value) {
      case 1:
        return SUCCESS;
      case 2:
        return CONDITION_NOT_MET;
      case 3:
        return ALREADY_GET_REWARD;
      default:
        return null;
    }
  }
}
