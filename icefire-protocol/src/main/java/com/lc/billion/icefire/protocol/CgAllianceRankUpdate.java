/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 修改阶级等级
 * @Message(837)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgAllianceRankUpdate implements org.apache.thrift.TBase<CgAllianceRankUpdate, CgAllianceRankUpdate._Fields>, java.io.Serializable, Cloneable, Comparable<CgAllianceRankUpdate> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgAllianceRankUpdate");

  private static final org.apache.thrift.protocol.TField ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roleId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField NEW_RANK_FIELD_DESC = new org.apache.thrift.protocol.TField("newRank", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField POSITION_FIELD_DESC = new org.apache.thrift.protocol.TField("position", org.apache.thrift.protocol.TType.I32, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgAllianceRankUpdateStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgAllianceRankUpdateTupleSchemeFactory();

  /**
   * 玩家ID
   */
  public long roleId; // required
  /**
   * 新等级
   */
  public int newRank; // required
  /**
   * 职位 R4必须设置
   */
  public int position; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 玩家ID
     */
    ROLE_ID((short)1, "roleId"),
    /**
     * 新等级
     */
    NEW_RANK((short)2, "newRank"),
    /**
     * 职位 R4必须设置
     */
    POSITION((short)3, "position");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROLE_ID
          return ROLE_ID;
        case 2: // NEW_RANK
          return NEW_RANK;
        case 3: // POSITION
          return POSITION;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ROLEID_ISSET_ID = 0;
  private static final int __NEWRANK_ISSET_ID = 1;
  private static final int __POSITION_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.POSITION};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("roleId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.NEW_RANK, new org.apache.thrift.meta_data.FieldMetaData("newRank", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.POSITION, new org.apache.thrift.meta_data.FieldMetaData("position", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgAllianceRankUpdate.class, metaDataMap);
  }

  public CgAllianceRankUpdate() {
  }

  public CgAllianceRankUpdate(
    long roleId,
    int newRank)
  {
    this();
    this.roleId = roleId;
    setRoleIdIsSet(true);
    this.newRank = newRank;
    setNewRankIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgAllianceRankUpdate(CgAllianceRankUpdate other) {
    __isset_bitfield = other.__isset_bitfield;
    this.roleId = other.roleId;
    this.newRank = other.newRank;
    this.position = other.position;
  }

  public CgAllianceRankUpdate deepCopy() {
    return new CgAllianceRankUpdate(this);
  }

  @Override
  public void clear() {
    setRoleIdIsSet(false);
    this.roleId = 0;
    setNewRankIsSet(false);
    this.newRank = 0;
    setPositionIsSet(false);
    this.position = 0;
  }

  /**
   * 玩家ID
   */
  public long getRoleId() {
    return this.roleId;
  }

  /**
   * 玩家ID
   */
  public CgAllianceRankUpdate setRoleId(long roleId) {
    this.roleId = roleId;
    setRoleIdIsSet(true);
    return this;
  }

  public void unsetRoleId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  /** Returns true if field roleId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  public void setRoleIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ROLEID_ISSET_ID, value);
  }

  /**
   * 新等级
   */
  public int getNewRank() {
    return this.newRank;
  }

  /**
   * 新等级
   */
  public CgAllianceRankUpdate setNewRank(int newRank) {
    this.newRank = newRank;
    setNewRankIsSet(true);
    return this;
  }

  public void unsetNewRank() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __NEWRANK_ISSET_ID);
  }

  /** Returns true if field newRank is set (has been assigned a value) and false otherwise */
  public boolean isSetNewRank() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __NEWRANK_ISSET_ID);
  }

  public void setNewRankIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __NEWRANK_ISSET_ID, value);
  }

  /**
   * 职位 R4必须设置
   */
  public int getPosition() {
    return this.position;
  }

  /**
   * 职位 R4必须设置
   */
  public CgAllianceRankUpdate setPosition(int position) {
    this.position = position;
    setPositionIsSet(true);
    return this;
  }

  public void unsetPosition() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __POSITION_ISSET_ID);
  }

  /** Returns true if field position is set (has been assigned a value) and false otherwise */
  public boolean isSetPosition() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __POSITION_ISSET_ID);
  }

  public void setPositionIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __POSITION_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ROLE_ID:
      if (value == null) {
        unsetRoleId();
      } else {
        setRoleId((java.lang.Long)value);
      }
      break;

    case NEW_RANK:
      if (value == null) {
        unsetNewRank();
      } else {
        setNewRank((java.lang.Integer)value);
      }
      break;

    case POSITION:
      if (value == null) {
        unsetPosition();
      } else {
        setPosition((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ROLE_ID:
      return getRoleId();

    case NEW_RANK:
      return getNewRank();

    case POSITION:
      return getPosition();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ROLE_ID:
      return isSetRoleId();
    case NEW_RANK:
      return isSetNewRank();
    case POSITION:
      return isSetPosition();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgAllianceRankUpdate)
      return this.equals((CgAllianceRankUpdate)that);
    return false;
  }

  public boolean equals(CgAllianceRankUpdate that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_roleId = true;
    boolean that_present_roleId = true;
    if (this_present_roleId || that_present_roleId) {
      if (!(this_present_roleId && that_present_roleId))
        return false;
      if (this.roleId != that.roleId)
        return false;
    }

    boolean this_present_newRank = true;
    boolean that_present_newRank = true;
    if (this_present_newRank || that_present_newRank) {
      if (!(this_present_newRank && that_present_newRank))
        return false;
      if (this.newRank != that.newRank)
        return false;
    }

    boolean this_present_position = true && this.isSetPosition();
    boolean that_present_position = true && that.isSetPosition();
    if (this_present_position || that_present_position) {
      if (!(this_present_position && that_present_position))
        return false;
      if (this.position != that.position)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(roleId);

    hashCode = hashCode * 8191 + newRank;

    hashCode = hashCode * 8191 + ((isSetPosition()) ? 131071 : 524287);
    if (isSetPosition())
      hashCode = hashCode * 8191 + position;

    return hashCode;
  }

  @Override
  public int compareTo(CgAllianceRankUpdate other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRoleId(), other.isSetRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleId, other.roleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetNewRank(), other.isSetNewRank());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNewRank()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.newRank, other.newRank);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPosition(), other.isSetPosition());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPosition()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.position, other.position);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgAllianceRankUpdate(");
    boolean first = true;

    sb.append("roleId:");
    sb.append(this.roleId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("newRank:");
    sb.append(this.newRank);
    first = false;
    if (isSetPosition()) {
      if (!first) sb.append(", ");
      sb.append("position:");
      sb.append(this.position);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'roleId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'newRank' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgAllianceRankUpdateStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgAllianceRankUpdateStandardScheme getScheme() {
      return new CgAllianceRankUpdateStandardScheme();
    }
  }

  private static class CgAllianceRankUpdateStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgAllianceRankUpdate> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgAllianceRankUpdate struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleId = iprot.readI64();
              struct.setRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // NEW_RANK
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.newRank = iprot.readI32();
              struct.setNewRankIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // POSITION
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.position = iprot.readI32();
              struct.setPositionIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetRoleId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'roleId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetNewRank()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'newRank' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgAllianceRankUpdate struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ROLE_ID_FIELD_DESC);
      oprot.writeI64(struct.roleId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(NEW_RANK_FIELD_DESC);
      oprot.writeI32(struct.newRank);
      oprot.writeFieldEnd();
      if (struct.isSetPosition()) {
        oprot.writeFieldBegin(POSITION_FIELD_DESC);
        oprot.writeI32(struct.position);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgAllianceRankUpdateTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgAllianceRankUpdateTupleScheme getScheme() {
      return new CgAllianceRankUpdateTupleScheme();
    }
  }

  private static class CgAllianceRankUpdateTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgAllianceRankUpdate> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgAllianceRankUpdate struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.roleId);
      oprot.writeI32(struct.newRank);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetPosition()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetPosition()) {
        oprot.writeI32(struct.position);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgAllianceRankUpdate struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.roleId = iprot.readI64();
      struct.setRoleIdIsSet(true);
      struct.newRank = iprot.readI32();
      struct.setNewRankIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.position = iprot.readI32();
        struct.setPositionIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

