/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 剧情事件领奖
 * @Message(158)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcStoryEventReward implements org.apache.thrift.TBase<GcStoryEventReward, GcStoryEventReward._Fields>, java.io.Serializable, Cloneable, Comparable<GcStoryEventReward> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcStoryEventReward");

  private static final org.apache.thrift.protocol.TField RESULT_FIELD_DESC = new org.apache.thrift.protocol.TField("result", org.apache.thrift.protocol.TType.BOOL, (short)1);
  private static final org.apache.thrift.protocol.TField ERR_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("errCode", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField ITEMS_FIELD_DESC = new org.apache.thrift.protocol.TField("items", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField PEOPLE_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("peopleIds", org.apache.thrift.protocol.TType.LIST, (short)5);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcStoryEventRewardStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcStoryEventRewardTupleSchemeFactory();

  /**
   * 奖励领取结果
   */
  public boolean result; // required
  /**
   * 错误码
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsErrorCode errCode; // required
  /**
   * 奖励
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> items; // optional
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.Integer> peopleIds; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 奖励领取结果
     */
    RESULT((short)1, "result"),
    /**
     * 错误码
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
     */
    ERR_CODE((short)2, "errCode"),
    /**
     * 奖励
     */
    ITEMS((short)3, "items"),
    PEOPLE_IDS((short)5, "peopleIds");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RESULT
          return RESULT;
        case 2: // ERR_CODE
          return ERR_CODE;
        case 3: // ITEMS
          return ITEMS;
        case 5: // PEOPLE_IDS
          return PEOPLE_IDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RESULT_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ITEMS,_Fields.PEOPLE_IDS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RESULT, new org.apache.thrift.meta_data.FieldMetaData("result", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.ERR_CODE, new org.apache.thrift.meta_data.FieldMetaData("errCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsErrorCode.class)));
    tmpMap.put(_Fields.ITEMS, new org.apache.thrift.meta_data.FieldMetaData("items", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSimpleItem.class))));
    tmpMap.put(_Fields.PEOPLE_IDS, new org.apache.thrift.meta_data.FieldMetaData("peopleIds", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcStoryEventReward.class, metaDataMap);
  }

  public GcStoryEventReward() {
  }

  public GcStoryEventReward(
    boolean result,
    com.lc.billion.icefire.protocol.constant.PsErrorCode errCode)
  {
    this();
    this.result = result;
    setResultIsSet(true);
    this.errCode = errCode;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcStoryEventReward(GcStoryEventReward other) {
    __isset_bitfield = other.__isset_bitfield;
    this.result = other.result;
    if (other.isSetErrCode()) {
      this.errCode = other.errCode;
    }
    if (other.isSetItems()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> __this__items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(other.items.size());
      for (com.lc.billion.icefire.protocol.structure.PsSimpleItem other_element : other.items) {
        __this__items.add(new com.lc.billion.icefire.protocol.structure.PsSimpleItem(other_element));
      }
      this.items = __this__items;
    }
    if (other.isSetPeopleIds()) {
      java.util.List<java.lang.Integer> __this__peopleIds = new java.util.ArrayList<java.lang.Integer>(other.peopleIds);
      this.peopleIds = __this__peopleIds;
    }
  }

  public GcStoryEventReward deepCopy() {
    return new GcStoryEventReward(this);
  }

  @Override
  public void clear() {
    setResultIsSet(false);
    this.result = false;
    this.errCode = null;
    this.items = null;
    this.peopleIds = null;
  }

  /**
   * 奖励领取结果
   */
  public boolean isResult() {
    return this.result;
  }

  /**
   * 奖励领取结果
   */
  public GcStoryEventReward setResult(boolean result) {
    this.result = result;
    setResultIsSet(true);
    return this;
  }

  public void unsetResult() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  /** Returns true if field result is set (has been assigned a value) and false otherwise */
  public boolean isSetResult() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  public void setResultIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RESULT_ISSET_ID, value);
  }

  /**
   * 错误码
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsErrorCode getErrCode() {
    return this.errCode;
  }

  /**
   * 错误码
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
   */
  public GcStoryEventReward setErrCode(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsErrorCode errCode) {
    this.errCode = errCode;
    return this;
  }

  public void unsetErrCode() {
    this.errCode = null;
  }

  /** Returns true if field errCode is set (has been assigned a value) and false otherwise */
  public boolean isSetErrCode() {
    return this.errCode != null;
  }

  public void setErrCodeIsSet(boolean value) {
    if (!value) {
      this.errCode = null;
    }
  }

  public int getItemsSize() {
    return (this.items == null) ? 0 : this.items.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getItemsIterator() {
    return (this.items == null) ? null : this.items.iterator();
  }

  public void addToItems(com.lc.billion.icefire.protocol.structure.PsSimpleItem elem) {
    if (this.items == null) {
      this.items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>();
    }
    this.items.add(elem);
  }

  /**
   * 奖励
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getItems() {
    return this.items;
  }

  /**
   * 奖励
   */
  public GcStoryEventReward setItems(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> items) {
    this.items = items;
    return this;
  }

  public void unsetItems() {
    this.items = null;
  }

  /** Returns true if field items is set (has been assigned a value) and false otherwise */
  public boolean isSetItems() {
    return this.items != null;
  }

  public void setItemsIsSet(boolean value) {
    if (!value) {
      this.items = null;
    }
  }

  public int getPeopleIdsSize() {
    return (this.peopleIds == null) ? 0 : this.peopleIds.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.Integer> getPeopleIdsIterator() {
    return (this.peopleIds == null) ? null : this.peopleIds.iterator();
  }

  public void addToPeopleIds(int elem) {
    if (this.peopleIds == null) {
      this.peopleIds = new java.util.ArrayList<java.lang.Integer>();
    }
    this.peopleIds.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.Integer> getPeopleIds() {
    return this.peopleIds;
  }

  public GcStoryEventReward setPeopleIds(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.Integer> peopleIds) {
    this.peopleIds = peopleIds;
    return this;
  }

  public void unsetPeopleIds() {
    this.peopleIds = null;
  }

  /** Returns true if field peopleIds is set (has been assigned a value) and false otherwise */
  public boolean isSetPeopleIds() {
    return this.peopleIds != null;
  }

  public void setPeopleIdsIsSet(boolean value) {
    if (!value) {
      this.peopleIds = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case RESULT:
      if (value == null) {
        unsetResult();
      } else {
        setResult((java.lang.Boolean)value);
      }
      break;

    case ERR_CODE:
      if (value == null) {
        unsetErrCode();
      } else {
        setErrCode((com.lc.billion.icefire.protocol.constant.PsErrorCode)value);
      }
      break;

    case ITEMS:
      if (value == null) {
        unsetItems();
      } else {
        setItems((java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>)value);
      }
      break;

    case PEOPLE_IDS:
      if (value == null) {
        unsetPeopleIds();
      } else {
        setPeopleIds((java.util.List<java.lang.Integer>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case RESULT:
      return isResult();

    case ERR_CODE:
      return getErrCode();

    case ITEMS:
      return getItems();

    case PEOPLE_IDS:
      return getPeopleIds();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case RESULT:
      return isSetResult();
    case ERR_CODE:
      return isSetErrCode();
    case ITEMS:
      return isSetItems();
    case PEOPLE_IDS:
      return isSetPeopleIds();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcStoryEventReward)
      return this.equals((GcStoryEventReward)that);
    return false;
  }

  public boolean equals(GcStoryEventReward that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_result = true;
    boolean that_present_result = true;
    if (this_present_result || that_present_result) {
      if (!(this_present_result && that_present_result))
        return false;
      if (this.result != that.result)
        return false;
    }

    boolean this_present_errCode = true && this.isSetErrCode();
    boolean that_present_errCode = true && that.isSetErrCode();
    if (this_present_errCode || that_present_errCode) {
      if (!(this_present_errCode && that_present_errCode))
        return false;
      if (!this.errCode.equals(that.errCode))
        return false;
    }

    boolean this_present_items = true && this.isSetItems();
    boolean that_present_items = true && that.isSetItems();
    if (this_present_items || that_present_items) {
      if (!(this_present_items && that_present_items))
        return false;
      if (!this.items.equals(that.items))
        return false;
    }

    boolean this_present_peopleIds = true && this.isSetPeopleIds();
    boolean that_present_peopleIds = true && that.isSetPeopleIds();
    if (this_present_peopleIds || that_present_peopleIds) {
      if (!(this_present_peopleIds && that_present_peopleIds))
        return false;
      if (!this.peopleIds.equals(that.peopleIds))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((result) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetErrCode()) ? 131071 : 524287);
    if (isSetErrCode())
      hashCode = hashCode * 8191 + errCode.getValue();

    hashCode = hashCode * 8191 + ((isSetItems()) ? 131071 : 524287);
    if (isSetItems())
      hashCode = hashCode * 8191 + items.hashCode();

    hashCode = hashCode * 8191 + ((isSetPeopleIds()) ? 131071 : 524287);
    if (isSetPeopleIds())
      hashCode = hashCode * 8191 + peopleIds.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcStoryEventReward other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetResult(), other.isSetResult());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResult()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.result, other.result);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetErrCode(), other.isSetErrCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetErrCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.errCode, other.errCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetItems(), other.isSetItems());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItems()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.items, other.items);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPeopleIds(), other.isSetPeopleIds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPeopleIds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.peopleIds, other.peopleIds);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcStoryEventReward(");
    boolean first = true;

    sb.append("result:");
    sb.append(this.result);
    first = false;
    if (!first) sb.append(", ");
    sb.append("errCode:");
    if (this.errCode == null) {
      sb.append("null");
    } else {
      sb.append(this.errCode);
    }
    first = false;
    if (isSetItems()) {
      if (!first) sb.append(", ");
      sb.append("items:");
      if (this.items == null) {
        sb.append("null");
      } else {
        sb.append(this.items);
      }
      first = false;
    }
    if (isSetPeopleIds()) {
      if (!first) sb.append(", ");
      sb.append("peopleIds:");
      if (this.peopleIds == null) {
        sb.append("null");
      } else {
        sb.append(this.peopleIds);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'result' because it's a primitive and you chose the non-beans generator.
    if (errCode == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'errCode' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcStoryEventRewardStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcStoryEventRewardStandardScheme getScheme() {
      return new GcStoryEventRewardStandardScheme();
    }
  }

  private static class GcStoryEventRewardStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcStoryEventReward> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcStoryEventReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RESULT
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.result = iprot.readBool();
              struct.setResultIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ERR_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.errCode = com.lc.billion.icefire.protocol.constant.PsErrorCode.findByValue(iprot.readI32());
              struct.setErrCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ITEMS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                  _elem1.read(iprot);
                  struct.items.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setItemsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // PEOPLE_IDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.peopleIds = new java.util.ArrayList<java.lang.Integer>(_list3.size);
                int _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = iprot.readI32();
                  struct.peopleIds.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setPeopleIdsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetResult()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'result' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcStoryEventReward struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RESULT_FIELD_DESC);
      oprot.writeBool(struct.result);
      oprot.writeFieldEnd();
      if (struct.errCode != null) {
        oprot.writeFieldBegin(ERR_CODE_FIELD_DESC);
        oprot.writeI32(struct.errCode.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.items != null) {
        if (struct.isSetItems()) {
          oprot.writeFieldBegin(ITEMS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.items.size()));
            for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter6 : struct.items)
            {
              _iter6.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.peopleIds != null) {
        if (struct.isSetPeopleIds()) {
          oprot.writeFieldBegin(PEOPLE_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, struct.peopleIds.size()));
            for (int _iter7 : struct.peopleIds)
            {
              oprot.writeI32(_iter7);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcStoryEventRewardTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcStoryEventRewardTupleScheme getScheme() {
      return new GcStoryEventRewardTupleScheme();
    }
  }

  private static class GcStoryEventRewardTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcStoryEventReward> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcStoryEventReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeBool(struct.result);
      oprot.writeI32(struct.errCode.getValue());
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetItems()) {
        optionals.set(0);
      }
      if (struct.isSetPeopleIds()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetItems()) {
        {
          oprot.writeI32(struct.items.size());
          for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter8 : struct.items)
          {
            _iter8.write(oprot);
          }
        }
      }
      if (struct.isSetPeopleIds()) {
        {
          oprot.writeI32(struct.peopleIds.size());
          for (int _iter9 : struct.peopleIds)
          {
            oprot.writeI32(_iter9);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcStoryEventReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.result = iprot.readBool();
      struct.setResultIsSet(true);
      struct.errCode = com.lc.billion.icefire.protocol.constant.PsErrorCode.findByValue(iprot.readI32());
      struct.setErrCodeIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list10 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list10.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem11;
          for (int _i12 = 0; _i12 < _list10.size; ++_i12)
          {
            _elem11 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
            _elem11.read(iprot);
            struct.items.add(_elem11);
          }
        }
        struct.setItemsIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list13 = iprot.readListBegin(org.apache.thrift.protocol.TType.I32);
          struct.peopleIds = new java.util.ArrayList<java.lang.Integer>(_list13.size);
          int _elem14;
          for (int _i15 = 0; _i15 < _list13.size; ++_i15)
          {
            _elem14 = iprot.readI32();
            struct.peopleIds.add(_elem14);
          }
        }
        struct.setPeopleIdsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

