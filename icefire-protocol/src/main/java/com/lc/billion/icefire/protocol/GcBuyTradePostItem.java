/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 贸易站 商品购买（兑换）
 * @Message(6728)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcBuyTradePostItem implements org.apache.thrift.TBase<GcBuyTradePostItem, GcBuyTradePostItem._Fields>, java.io.Serializable, Cloneable, Comparable<GcBuyTradePostItem> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcBuyTradePostItem");

  private static final org.apache.thrift.protocol.TField ACTIVITY_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("activityMetaId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.BOOL, (short)2);
  private static final org.apache.thrift.protocol.TField REWARDS_FIELD_DESC = new org.apache.thrift.protocol.TField("rewards", org.apache.thrift.protocol.TType.LIST, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcBuyTradePostItemStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcBuyTradePostItemTupleSchemeFactory();

  /**
   * 活动metaId
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String activityMetaId; // required
  public boolean success; // required
  /**
   * 购买（兑换）结果
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 活动metaId
     */
    ACTIVITY_META_ID((short)1, "activityMetaId"),
    SUCCESS((short)2, "success"),
    /**
     * 购买（兑换）结果
     */
    REWARDS((short)3, "rewards");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACTIVITY_META_ID
          return ACTIVITY_META_ID;
        case 2: // SUCCESS
          return SUCCESS;
        case 3: // REWARDS
          return REWARDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SUCCESS_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACTIVITY_META_ID, new org.apache.thrift.meta_data.FieldMetaData("activityMetaId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.REWARDS, new org.apache.thrift.meta_data.FieldMetaData("rewards", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSimpleItem.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcBuyTradePostItem.class, metaDataMap);
  }

  public GcBuyTradePostItem() {
  }

  public GcBuyTradePostItem(
    java.lang.String activityMetaId,
    boolean success,
    java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards)
  {
    this();
    this.activityMetaId = activityMetaId;
    this.success = success;
    setSuccessIsSet(true);
    this.rewards = rewards;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcBuyTradePostItem(GcBuyTradePostItem other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetActivityMetaId()) {
      this.activityMetaId = other.activityMetaId;
    }
    this.success = other.success;
    if (other.isSetRewards()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> __this__rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(other.rewards.size());
      for (com.lc.billion.icefire.protocol.structure.PsSimpleItem other_element : other.rewards) {
        __this__rewards.add(new com.lc.billion.icefire.protocol.structure.PsSimpleItem(other_element));
      }
      this.rewards = __this__rewards;
    }
  }

  public GcBuyTradePostItem deepCopy() {
    return new GcBuyTradePostItem(this);
  }

  @Override
  public void clear() {
    this.activityMetaId = null;
    setSuccessIsSet(false);
    this.success = false;
    this.rewards = null;
  }

  /**
   * 活动metaId
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getActivityMetaId() {
    return this.activityMetaId;
  }

  /**
   * 活动metaId
   */
  public GcBuyTradePostItem setActivityMetaId(@org.apache.thrift.annotation.Nullable java.lang.String activityMetaId) {
    this.activityMetaId = activityMetaId;
    return this;
  }

  public void unsetActivityMetaId() {
    this.activityMetaId = null;
  }

  /** Returns true if field activityMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetActivityMetaId() {
    return this.activityMetaId != null;
  }

  public void setActivityMetaIdIsSet(boolean value) {
    if (!value) {
      this.activityMetaId = null;
    }
  }

  public boolean isSuccess() {
    return this.success;
  }

  public GcBuyTradePostItem setSuccess(boolean success) {
    this.success = success;
    setSuccessIsSet(true);
    return this;
  }

  public void unsetSuccess() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SUCCESS_ISSET_ID);
  }

  /** Returns true if field success is set (has been assigned a value) and false otherwise */
  public boolean isSetSuccess() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SUCCESS_ISSET_ID);
  }

  public void setSuccessIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SUCCESS_ISSET_ID, value);
  }

  public int getRewardsSize() {
    return (this.rewards == null) ? 0 : this.rewards.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewardsIterator() {
    return (this.rewards == null) ? null : this.rewards.iterator();
  }

  public void addToRewards(com.lc.billion.icefire.protocol.structure.PsSimpleItem elem) {
    if (this.rewards == null) {
      this.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>();
    }
    this.rewards.add(elem);
  }

  /**
   * 购买（兑换）结果
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewards() {
    return this.rewards;
  }

  /**
   * 购买（兑换）结果
   */
  public GcBuyTradePostItem setRewards(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards) {
    this.rewards = rewards;
    return this;
  }

  public void unsetRewards() {
    this.rewards = null;
  }

  /** Returns true if field rewards is set (has been assigned a value) and false otherwise */
  public boolean isSetRewards() {
    return this.rewards != null;
  }

  public void setRewardsIsSet(boolean value) {
    if (!value) {
      this.rewards = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ACTIVITY_META_ID:
      if (value == null) {
        unsetActivityMetaId();
      } else {
        setActivityMetaId((java.lang.String)value);
      }
      break;

    case SUCCESS:
      if (value == null) {
        unsetSuccess();
      } else {
        setSuccess((java.lang.Boolean)value);
      }
      break;

    case REWARDS:
      if (value == null) {
        unsetRewards();
      } else {
        setRewards((java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ACTIVITY_META_ID:
      return getActivityMetaId();

    case SUCCESS:
      return isSuccess();

    case REWARDS:
      return getRewards();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ACTIVITY_META_ID:
      return isSetActivityMetaId();
    case SUCCESS:
      return isSetSuccess();
    case REWARDS:
      return isSetRewards();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcBuyTradePostItem)
      return this.equals((GcBuyTradePostItem)that);
    return false;
  }

  public boolean equals(GcBuyTradePostItem that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_activityMetaId = true && this.isSetActivityMetaId();
    boolean that_present_activityMetaId = true && that.isSetActivityMetaId();
    if (this_present_activityMetaId || that_present_activityMetaId) {
      if (!(this_present_activityMetaId && that_present_activityMetaId))
        return false;
      if (!this.activityMetaId.equals(that.activityMetaId))
        return false;
    }

    boolean this_present_success = true;
    boolean that_present_success = true;
    if (this_present_success || that_present_success) {
      if (!(this_present_success && that_present_success))
        return false;
      if (this.success != that.success)
        return false;
    }

    boolean this_present_rewards = true && this.isSetRewards();
    boolean that_present_rewards = true && that.isSetRewards();
    if (this_present_rewards || that_present_rewards) {
      if (!(this_present_rewards && that_present_rewards))
        return false;
      if (!this.rewards.equals(that.rewards))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetActivityMetaId()) ? 131071 : 524287);
    if (isSetActivityMetaId())
      hashCode = hashCode * 8191 + activityMetaId.hashCode();

    hashCode = hashCode * 8191 + ((success) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetRewards()) ? 131071 : 524287);
    if (isSetRewards())
      hashCode = hashCode * 8191 + rewards.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcBuyTradePostItem other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetActivityMetaId(), other.isSetActivityMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActivityMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.activityMetaId, other.activityMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSuccess(), other.isSetSuccess());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSuccess()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRewards(), other.isSetRewards());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRewards()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rewards, other.rewards);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcBuyTradePostItem(");
    boolean first = true;

    sb.append("activityMetaId:");
    if (this.activityMetaId == null) {
      sb.append("null");
    } else {
      sb.append(this.activityMetaId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("success:");
    sb.append(this.success);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rewards:");
    if (this.rewards == null) {
      sb.append("null");
    } else {
      sb.append(this.rewards);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (activityMetaId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'activityMetaId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'success' because it's a primitive and you chose the non-beans generator.
    if (rewards == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'rewards' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcBuyTradePostItemStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcBuyTradePostItemStandardScheme getScheme() {
      return new GcBuyTradePostItemStandardScheme();
    }
  }

  private static class GcBuyTradePostItemStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcBuyTradePostItem> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcBuyTradePostItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACTIVITY_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.activityMetaId = iprot.readString();
              struct.setActivityMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SUCCESS
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.success = iprot.readBool();
              struct.setSuccessIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REWARDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                  _elem1.read(iprot);
                  struct.rewards.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setRewardsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetSuccess()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'success' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcBuyTradePostItem struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.activityMetaId != null) {
        oprot.writeFieldBegin(ACTIVITY_META_ID_FIELD_DESC);
        oprot.writeString(struct.activityMetaId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
      oprot.writeBool(struct.success);
      oprot.writeFieldEnd();
      if (struct.rewards != null) {
        oprot.writeFieldBegin(REWARDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.rewards.size()));
          for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter3 : struct.rewards)
          {
            _iter3.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcBuyTradePostItemTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcBuyTradePostItemTupleScheme getScheme() {
      return new GcBuyTradePostItemTupleScheme();
    }
  }

  private static class GcBuyTradePostItemTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcBuyTradePostItem> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcBuyTradePostItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.activityMetaId);
      oprot.writeBool(struct.success);
      {
        oprot.writeI32(struct.rewards.size());
        for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter4 : struct.rewards)
        {
          _iter4.write(oprot);
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcBuyTradePostItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.activityMetaId = iprot.readString();
      struct.setActivityMetaIdIsSet(true);
      struct.success = iprot.readBool();
      struct.setSuccessIsSet(true);
      {
        org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list5.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem6;
        for (int _i7 = 0; _i7 < _list5.size; ++_i7)
        {
          _elem6 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
          _elem6.read(iprot);
          struct.rewards.add(_elem6);
        }
      }
      struct.setRewardsIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

