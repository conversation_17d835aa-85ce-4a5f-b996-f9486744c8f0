/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 诸侯争霸 -- 比分总览 -- 请求
 * @Message(7651)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgVassalOverview implements org.apache.thrift.TBase<CgVassalOverview, CgVassalOverview._Fields>, java.io.Serializable, Cloneable, Comparable<CgVassalOverview> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgVassalOverview");

  private static final org.apache.thrift.protocol.TField SEQ_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("seqNo", org.apache.thrift.protocol.TType.I32, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgVassalOverviewStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgVassalOverviewTupleSchemeFactory();

  public int seqNo; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SEQ_NO((short)1, "seqNo");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SEQ_NO
          return SEQ_NO;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SEQNO_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SEQ_NO, new org.apache.thrift.meta_data.FieldMetaData("seqNo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgVassalOverview.class, metaDataMap);
  }

  public CgVassalOverview() {
  }

  public CgVassalOverview(
    int seqNo)
  {
    this();
    this.seqNo = seqNo;
    setSeqNoIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgVassalOverview(CgVassalOverview other) {
    __isset_bitfield = other.__isset_bitfield;
    this.seqNo = other.seqNo;
  }

  public CgVassalOverview deepCopy() {
    return new CgVassalOverview(this);
  }

  @Override
  public void clear() {
    setSeqNoIsSet(false);
    this.seqNo = 0;
  }

  public int getSeqNo() {
    return this.seqNo;
  }

  public CgVassalOverview setSeqNo(int seqNo) {
    this.seqNo = seqNo;
    setSeqNoIsSet(true);
    return this;
  }

  public void unsetSeqNo() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SEQNO_ISSET_ID);
  }

  /** Returns true if field seqNo is set (has been assigned a value) and false otherwise */
  public boolean isSetSeqNo() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SEQNO_ISSET_ID);
  }

  public void setSeqNoIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SEQNO_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case SEQ_NO:
      if (value == null) {
        unsetSeqNo();
      } else {
        setSeqNo((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case SEQ_NO:
      return getSeqNo();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case SEQ_NO:
      return isSetSeqNo();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgVassalOverview)
      return this.equals((CgVassalOverview)that);
    return false;
  }

  public boolean equals(CgVassalOverview that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_seqNo = true;
    boolean that_present_seqNo = true;
    if (this_present_seqNo || that_present_seqNo) {
      if (!(this_present_seqNo && that_present_seqNo))
        return false;
      if (this.seqNo != that.seqNo)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + seqNo;

    return hashCode;
  }

  @Override
  public int compareTo(CgVassalOverview other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetSeqNo(), other.isSetSeqNo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeqNo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.seqNo, other.seqNo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgVassalOverview(");
    boolean first = true;

    sb.append("seqNo:");
    sb.append(this.seqNo);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'seqNo' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgVassalOverviewStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgVassalOverviewStandardScheme getScheme() {
      return new CgVassalOverviewStandardScheme();
    }
  }

  private static class CgVassalOverviewStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgVassalOverview> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgVassalOverview struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SEQ_NO
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.seqNo = iprot.readI32();
              struct.setSeqNoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetSeqNo()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'seqNo' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgVassalOverview struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(SEQ_NO_FIELD_DESC);
      oprot.writeI32(struct.seqNo);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgVassalOverviewTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgVassalOverviewTupleScheme getScheme() {
      return new CgVassalOverviewTupleScheme();
    }
  }

  private static class CgVassalOverviewTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgVassalOverview> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgVassalOverview struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.seqNo);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgVassalOverview struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.seqNo = iprot.readI32();
      struct.setSeqNoIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

