/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 诸侯争霸 -- 比分总览 -- 响应
 * @Message(7652)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcVassalOverview implements org.apache.thrift.TBase<GcVassalOverview, GcVassalOverview._Fields>, java.io.Serializable, Cloneable, Comparable<GcVassalOverview> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcVassalOverview");

  private static final org.apache.thrift.protocol.TField SEQ_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("seqNo", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField FINISHED_RED_WIN_FIELD_DESC = new org.apache.thrift.protocol.TField("finishedRedWin", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField FINISHED_BLUE_WIN_FIELD_DESC = new org.apache.thrift.protocol.TField("finishedBlueWin", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField DURING_RED_WIN_FIELD_DESC = new org.apache.thrift.protocol.TField("duringRedWin", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField DURING_BLUE_WIN_FIELD_DESC = new org.apache.thrift.protocol.TField("duringBlueWin", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField SERVER_DETAILS_FIELD_DESC = new org.apache.thrift.protocol.TField("serverDetails", org.apache.thrift.protocol.TType.MAP, (short)6);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcVassalOverviewStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcVassalOverviewTupleSchemeFactory();

  public int seqNo; // required
  public int finishedRedWin; // required
  public int finishedBlueWin; // required
  public int duringRedWin; // required
  public int duringBlueWin; // required
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.Integer,java.lang.Integer> serverDetails; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SEQ_NO((short)1, "seqNo"),
    FINISHED_RED_WIN((short)2, "finishedRedWin"),
    FINISHED_BLUE_WIN((short)3, "finishedBlueWin"),
    DURING_RED_WIN((short)4, "duringRedWin"),
    DURING_BLUE_WIN((short)5, "duringBlueWin"),
    SERVER_DETAILS((short)6, "serverDetails");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SEQ_NO
          return SEQ_NO;
        case 2: // FINISHED_RED_WIN
          return FINISHED_RED_WIN;
        case 3: // FINISHED_BLUE_WIN
          return FINISHED_BLUE_WIN;
        case 4: // DURING_RED_WIN
          return DURING_RED_WIN;
        case 5: // DURING_BLUE_WIN
          return DURING_BLUE_WIN;
        case 6: // SERVER_DETAILS
          return SERVER_DETAILS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SEQNO_ISSET_ID = 0;
  private static final int __FINISHEDREDWIN_ISSET_ID = 1;
  private static final int __FINISHEDBLUEWIN_ISSET_ID = 2;
  private static final int __DURINGREDWIN_ISSET_ID = 3;
  private static final int __DURINGBLUEWIN_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.SERVER_DETAILS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SEQ_NO, new org.apache.thrift.meta_data.FieldMetaData("seqNo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.FINISHED_RED_WIN, new org.apache.thrift.meta_data.FieldMetaData("finishedRedWin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.FINISHED_BLUE_WIN, new org.apache.thrift.meta_data.FieldMetaData("finishedBlueWin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.DURING_RED_WIN, new org.apache.thrift.meta_data.FieldMetaData("duringRedWin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.DURING_BLUE_WIN, new org.apache.thrift.meta_data.FieldMetaData("duringBlueWin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SERVER_DETAILS, new org.apache.thrift.meta_data.FieldMetaData("serverDetails", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcVassalOverview.class, metaDataMap);
  }

  public GcVassalOverview() {
  }

  public GcVassalOverview(
    int seqNo,
    int finishedRedWin,
    int finishedBlueWin,
    int duringRedWin,
    int duringBlueWin)
  {
    this();
    this.seqNo = seqNo;
    setSeqNoIsSet(true);
    this.finishedRedWin = finishedRedWin;
    setFinishedRedWinIsSet(true);
    this.finishedBlueWin = finishedBlueWin;
    setFinishedBlueWinIsSet(true);
    this.duringRedWin = duringRedWin;
    setDuringRedWinIsSet(true);
    this.duringBlueWin = duringBlueWin;
    setDuringBlueWinIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcVassalOverview(GcVassalOverview other) {
    __isset_bitfield = other.__isset_bitfield;
    this.seqNo = other.seqNo;
    this.finishedRedWin = other.finishedRedWin;
    this.finishedBlueWin = other.finishedBlueWin;
    this.duringRedWin = other.duringRedWin;
    this.duringBlueWin = other.duringBlueWin;
    if (other.isSetServerDetails()) {
      java.util.Map<java.lang.Integer,java.lang.Integer> __this__serverDetails = new java.util.HashMap<java.lang.Integer,java.lang.Integer>(other.serverDetails);
      this.serverDetails = __this__serverDetails;
    }
  }

  public GcVassalOverview deepCopy() {
    return new GcVassalOverview(this);
  }

  @Override
  public void clear() {
    setSeqNoIsSet(false);
    this.seqNo = 0;
    setFinishedRedWinIsSet(false);
    this.finishedRedWin = 0;
    setFinishedBlueWinIsSet(false);
    this.finishedBlueWin = 0;
    setDuringRedWinIsSet(false);
    this.duringRedWin = 0;
    setDuringBlueWinIsSet(false);
    this.duringBlueWin = 0;
    this.serverDetails = null;
  }

  public int getSeqNo() {
    return this.seqNo;
  }

  public GcVassalOverview setSeqNo(int seqNo) {
    this.seqNo = seqNo;
    setSeqNoIsSet(true);
    return this;
  }

  public void unsetSeqNo() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SEQNO_ISSET_ID);
  }

  /** Returns true if field seqNo is set (has been assigned a value) and false otherwise */
  public boolean isSetSeqNo() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SEQNO_ISSET_ID);
  }

  public void setSeqNoIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SEQNO_ISSET_ID, value);
  }

  public int getFinishedRedWin() {
    return this.finishedRedWin;
  }

  public GcVassalOverview setFinishedRedWin(int finishedRedWin) {
    this.finishedRedWin = finishedRedWin;
    setFinishedRedWinIsSet(true);
    return this;
  }

  public void unsetFinishedRedWin() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __FINISHEDREDWIN_ISSET_ID);
  }

  /** Returns true if field finishedRedWin is set (has been assigned a value) and false otherwise */
  public boolean isSetFinishedRedWin() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __FINISHEDREDWIN_ISSET_ID);
  }

  public void setFinishedRedWinIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __FINISHEDREDWIN_ISSET_ID, value);
  }

  public int getFinishedBlueWin() {
    return this.finishedBlueWin;
  }

  public GcVassalOverview setFinishedBlueWin(int finishedBlueWin) {
    this.finishedBlueWin = finishedBlueWin;
    setFinishedBlueWinIsSet(true);
    return this;
  }

  public void unsetFinishedBlueWin() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __FINISHEDBLUEWIN_ISSET_ID);
  }

  /** Returns true if field finishedBlueWin is set (has been assigned a value) and false otherwise */
  public boolean isSetFinishedBlueWin() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __FINISHEDBLUEWIN_ISSET_ID);
  }

  public void setFinishedBlueWinIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __FINISHEDBLUEWIN_ISSET_ID, value);
  }

  public int getDuringRedWin() {
    return this.duringRedWin;
  }

  public GcVassalOverview setDuringRedWin(int duringRedWin) {
    this.duringRedWin = duringRedWin;
    setDuringRedWinIsSet(true);
    return this;
  }

  public void unsetDuringRedWin() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __DURINGREDWIN_ISSET_ID);
  }

  /** Returns true if field duringRedWin is set (has been assigned a value) and false otherwise */
  public boolean isSetDuringRedWin() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __DURINGREDWIN_ISSET_ID);
  }

  public void setDuringRedWinIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __DURINGREDWIN_ISSET_ID, value);
  }

  public int getDuringBlueWin() {
    return this.duringBlueWin;
  }

  public GcVassalOverview setDuringBlueWin(int duringBlueWin) {
    this.duringBlueWin = duringBlueWin;
    setDuringBlueWinIsSet(true);
    return this;
  }

  public void unsetDuringBlueWin() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __DURINGBLUEWIN_ISSET_ID);
  }

  /** Returns true if field duringBlueWin is set (has been assigned a value) and false otherwise */
  public boolean isSetDuringBlueWin() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __DURINGBLUEWIN_ISSET_ID);
  }

  public void setDuringBlueWinIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __DURINGBLUEWIN_ISSET_ID, value);
  }

  public int getServerDetailsSize() {
    return (this.serverDetails == null) ? 0 : this.serverDetails.size();
  }

  public void putToServerDetails(int key, int val) {
    if (this.serverDetails == null) {
      this.serverDetails = new java.util.HashMap<java.lang.Integer,java.lang.Integer>();
    }
    this.serverDetails.put(key, val);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.Integer,java.lang.Integer> getServerDetails() {
    return this.serverDetails;
  }

  public GcVassalOverview setServerDetails(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.Integer,java.lang.Integer> serverDetails) {
    this.serverDetails = serverDetails;
    return this;
  }

  public void unsetServerDetails() {
    this.serverDetails = null;
  }

  /** Returns true if field serverDetails is set (has been assigned a value) and false otherwise */
  public boolean isSetServerDetails() {
    return this.serverDetails != null;
  }

  public void setServerDetailsIsSet(boolean value) {
    if (!value) {
      this.serverDetails = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case SEQ_NO:
      if (value == null) {
        unsetSeqNo();
      } else {
        setSeqNo((java.lang.Integer)value);
      }
      break;

    case FINISHED_RED_WIN:
      if (value == null) {
        unsetFinishedRedWin();
      } else {
        setFinishedRedWin((java.lang.Integer)value);
      }
      break;

    case FINISHED_BLUE_WIN:
      if (value == null) {
        unsetFinishedBlueWin();
      } else {
        setFinishedBlueWin((java.lang.Integer)value);
      }
      break;

    case DURING_RED_WIN:
      if (value == null) {
        unsetDuringRedWin();
      } else {
        setDuringRedWin((java.lang.Integer)value);
      }
      break;

    case DURING_BLUE_WIN:
      if (value == null) {
        unsetDuringBlueWin();
      } else {
        setDuringBlueWin((java.lang.Integer)value);
      }
      break;

    case SERVER_DETAILS:
      if (value == null) {
        unsetServerDetails();
      } else {
        setServerDetails((java.util.Map<java.lang.Integer,java.lang.Integer>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case SEQ_NO:
      return getSeqNo();

    case FINISHED_RED_WIN:
      return getFinishedRedWin();

    case FINISHED_BLUE_WIN:
      return getFinishedBlueWin();

    case DURING_RED_WIN:
      return getDuringRedWin();

    case DURING_BLUE_WIN:
      return getDuringBlueWin();

    case SERVER_DETAILS:
      return getServerDetails();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case SEQ_NO:
      return isSetSeqNo();
    case FINISHED_RED_WIN:
      return isSetFinishedRedWin();
    case FINISHED_BLUE_WIN:
      return isSetFinishedBlueWin();
    case DURING_RED_WIN:
      return isSetDuringRedWin();
    case DURING_BLUE_WIN:
      return isSetDuringBlueWin();
    case SERVER_DETAILS:
      return isSetServerDetails();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcVassalOverview)
      return this.equals((GcVassalOverview)that);
    return false;
  }

  public boolean equals(GcVassalOverview that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_seqNo = true;
    boolean that_present_seqNo = true;
    if (this_present_seqNo || that_present_seqNo) {
      if (!(this_present_seqNo && that_present_seqNo))
        return false;
      if (this.seqNo != that.seqNo)
        return false;
    }

    boolean this_present_finishedRedWin = true;
    boolean that_present_finishedRedWin = true;
    if (this_present_finishedRedWin || that_present_finishedRedWin) {
      if (!(this_present_finishedRedWin && that_present_finishedRedWin))
        return false;
      if (this.finishedRedWin != that.finishedRedWin)
        return false;
    }

    boolean this_present_finishedBlueWin = true;
    boolean that_present_finishedBlueWin = true;
    if (this_present_finishedBlueWin || that_present_finishedBlueWin) {
      if (!(this_present_finishedBlueWin && that_present_finishedBlueWin))
        return false;
      if (this.finishedBlueWin != that.finishedBlueWin)
        return false;
    }

    boolean this_present_duringRedWin = true;
    boolean that_present_duringRedWin = true;
    if (this_present_duringRedWin || that_present_duringRedWin) {
      if (!(this_present_duringRedWin && that_present_duringRedWin))
        return false;
      if (this.duringRedWin != that.duringRedWin)
        return false;
    }

    boolean this_present_duringBlueWin = true;
    boolean that_present_duringBlueWin = true;
    if (this_present_duringBlueWin || that_present_duringBlueWin) {
      if (!(this_present_duringBlueWin && that_present_duringBlueWin))
        return false;
      if (this.duringBlueWin != that.duringBlueWin)
        return false;
    }

    boolean this_present_serverDetails = true && this.isSetServerDetails();
    boolean that_present_serverDetails = true && that.isSetServerDetails();
    if (this_present_serverDetails || that_present_serverDetails) {
      if (!(this_present_serverDetails && that_present_serverDetails))
        return false;
      if (!this.serverDetails.equals(that.serverDetails))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + seqNo;

    hashCode = hashCode * 8191 + finishedRedWin;

    hashCode = hashCode * 8191 + finishedBlueWin;

    hashCode = hashCode * 8191 + duringRedWin;

    hashCode = hashCode * 8191 + duringBlueWin;

    hashCode = hashCode * 8191 + ((isSetServerDetails()) ? 131071 : 524287);
    if (isSetServerDetails())
      hashCode = hashCode * 8191 + serverDetails.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcVassalOverview other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetSeqNo(), other.isSetSeqNo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeqNo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.seqNo, other.seqNo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetFinishedRedWin(), other.isSetFinishedRedWin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFinishedRedWin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.finishedRedWin, other.finishedRedWin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetFinishedBlueWin(), other.isSetFinishedBlueWin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFinishedBlueWin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.finishedBlueWin, other.finishedBlueWin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetDuringRedWin(), other.isSetDuringRedWin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDuringRedWin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.duringRedWin, other.duringRedWin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetDuringBlueWin(), other.isSetDuringBlueWin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDuringBlueWin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.duringBlueWin, other.duringBlueWin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetServerDetails(), other.isSetServerDetails());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetServerDetails()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.serverDetails, other.serverDetails);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcVassalOverview(");
    boolean first = true;

    sb.append("seqNo:");
    sb.append(this.seqNo);
    first = false;
    if (!first) sb.append(", ");
    sb.append("finishedRedWin:");
    sb.append(this.finishedRedWin);
    first = false;
    if (!first) sb.append(", ");
    sb.append("finishedBlueWin:");
    sb.append(this.finishedBlueWin);
    first = false;
    if (!first) sb.append(", ");
    sb.append("duringRedWin:");
    sb.append(this.duringRedWin);
    first = false;
    if (!first) sb.append(", ");
    sb.append("duringBlueWin:");
    sb.append(this.duringBlueWin);
    first = false;
    if (isSetServerDetails()) {
      if (!first) sb.append(", ");
      sb.append("serverDetails:");
      if (this.serverDetails == null) {
        sb.append("null");
      } else {
        sb.append(this.serverDetails);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'seqNo' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'finishedRedWin' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'finishedBlueWin' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'duringRedWin' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'duringBlueWin' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcVassalOverviewStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcVassalOverviewStandardScheme getScheme() {
      return new GcVassalOverviewStandardScheme();
    }
  }

  private static class GcVassalOverviewStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcVassalOverview> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcVassalOverview struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SEQ_NO
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.seqNo = iprot.readI32();
              struct.setSeqNoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // FINISHED_RED_WIN
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.finishedRedWin = iprot.readI32();
              struct.setFinishedRedWinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // FINISHED_BLUE_WIN
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.finishedBlueWin = iprot.readI32();
              struct.setFinishedBlueWinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // DURING_RED_WIN
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.duringRedWin = iprot.readI32();
              struct.setDuringRedWinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // DURING_BLUE_WIN
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.duringBlueWin = iprot.readI32();
              struct.setDuringBlueWinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SERVER_DETAILS
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map0 = iprot.readMapBegin();
                struct.serverDetails = new java.util.HashMap<java.lang.Integer,java.lang.Integer>(2*_map0.size);
                int _key1;
                int _val2;
                for (int _i3 = 0; _i3 < _map0.size; ++_i3)
                {
                  _key1 = iprot.readI32();
                  _val2 = iprot.readI32();
                  struct.serverDetails.put(_key1, _val2);
                }
                iprot.readMapEnd();
              }
              struct.setServerDetailsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetSeqNo()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'seqNo' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetFinishedRedWin()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'finishedRedWin' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetFinishedBlueWin()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'finishedBlueWin' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetDuringRedWin()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'duringRedWin' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetDuringBlueWin()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'duringBlueWin' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcVassalOverview struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(SEQ_NO_FIELD_DESC);
      oprot.writeI32(struct.seqNo);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FINISHED_RED_WIN_FIELD_DESC);
      oprot.writeI32(struct.finishedRedWin);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FINISHED_BLUE_WIN_FIELD_DESC);
      oprot.writeI32(struct.finishedBlueWin);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(DURING_RED_WIN_FIELD_DESC);
      oprot.writeI32(struct.duringRedWin);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(DURING_BLUE_WIN_FIELD_DESC);
      oprot.writeI32(struct.duringBlueWin);
      oprot.writeFieldEnd();
      if (struct.serverDetails != null) {
        if (struct.isSetServerDetails()) {
          oprot.writeFieldBegin(SERVER_DETAILS_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.I32, struct.serverDetails.size()));
            for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> _iter4 : struct.serverDetails.entrySet())
            {
              oprot.writeI32(_iter4.getKey());
              oprot.writeI32(_iter4.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcVassalOverviewTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcVassalOverviewTupleScheme getScheme() {
      return new GcVassalOverviewTupleScheme();
    }
  }

  private static class GcVassalOverviewTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcVassalOverview> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcVassalOverview struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.seqNo);
      oprot.writeI32(struct.finishedRedWin);
      oprot.writeI32(struct.finishedBlueWin);
      oprot.writeI32(struct.duringRedWin);
      oprot.writeI32(struct.duringBlueWin);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetServerDetails()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetServerDetails()) {
        {
          oprot.writeI32(struct.serverDetails.size());
          for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> _iter5 : struct.serverDetails.entrySet())
          {
            oprot.writeI32(_iter5.getKey());
            oprot.writeI32(_iter5.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcVassalOverview struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.seqNo = iprot.readI32();
      struct.setSeqNoIsSet(true);
      struct.finishedRedWin = iprot.readI32();
      struct.setFinishedRedWinIsSet(true);
      struct.finishedBlueWin = iprot.readI32();
      struct.setFinishedBlueWinIsSet(true);
      struct.duringRedWin = iprot.readI32();
      struct.setDuringRedWinIsSet(true);
      struct.duringBlueWin = iprot.readI32();
      struct.setDuringBlueWinIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TMap _map6 = iprot.readMapBegin(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.I32); 
          struct.serverDetails = new java.util.HashMap<java.lang.Integer,java.lang.Integer>(2*_map6.size);
          int _key7;
          int _val8;
          for (int _i9 = 0; _i9 < _map6.size; ++_i9)
          {
            _key7 = iprot.readI32();
            _val8 = iprot.readI32();
            struct.serverDetails.put(_key7, _val8);
          }
        }
        struct.setServerDetailsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

