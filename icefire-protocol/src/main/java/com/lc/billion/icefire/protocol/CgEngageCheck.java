/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 约战：审核申请
 * @Message(3976)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgEngageCheck implements org.apache.thrift.TBase<CgEngageCheck, CgEngageCheck._Fields>, java.io.Serializable, Cloneable, Comparable<CgEngageCheck> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgEngageCheck");

  private static final org.apache.thrift.protocol.TField ROOM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roomId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField TARGET_ALLIANCE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("targetAllianceId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("type", org.apache.thrift.protocol.TType.I32, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgEngageCheckStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgEngageCheckTupleSchemeFactory();

  public long roomId; // optional
  /**
   * 申请的联盟Id
   */
  public long targetAllianceId; // optional
  /**
   * 类型1.同意 2.拒绝
   */
  public int type; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ROOM_ID((short)1, "roomId"),
    /**
     * 申请的联盟Id
     */
    TARGET_ALLIANCE_ID((short)2, "targetAllianceId"),
    /**
     * 类型1.同意 2.拒绝
     */
    TYPE((short)3, "type");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROOM_ID
          return ROOM_ID;
        case 2: // TARGET_ALLIANCE_ID
          return TARGET_ALLIANCE_ID;
        case 3: // TYPE
          return TYPE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ROOMID_ISSET_ID = 0;
  private static final int __TARGETALLIANCEID_ISSET_ID = 1;
  private static final int __TYPE_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ROOM_ID,_Fields.TARGET_ALLIANCE_ID,_Fields.TYPE};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROOM_ID, new org.apache.thrift.meta_data.FieldMetaData("roomId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TARGET_ALLIANCE_ID, new org.apache.thrift.meta_data.FieldMetaData("targetAllianceId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TYPE, new org.apache.thrift.meta_data.FieldMetaData("type", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgEngageCheck.class, metaDataMap);
  }

  public CgEngageCheck() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgEngageCheck(CgEngageCheck other) {
    __isset_bitfield = other.__isset_bitfield;
    this.roomId = other.roomId;
    this.targetAllianceId = other.targetAllianceId;
    this.type = other.type;
  }

  public CgEngageCheck deepCopy() {
    return new CgEngageCheck(this);
  }

  @Override
  public void clear() {
    setRoomIdIsSet(false);
    this.roomId = 0;
    setTargetAllianceIdIsSet(false);
    this.targetAllianceId = 0;
    setTypeIsSet(false);
    this.type = 0;
  }

  public long getRoomId() {
    return this.roomId;
  }

  public CgEngageCheck setRoomId(long roomId) {
    this.roomId = roomId;
    setRoomIdIsSet(true);
    return this;
  }

  public void unsetRoomId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ROOMID_ISSET_ID);
  }

  /** Returns true if field roomId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ROOMID_ISSET_ID);
  }

  public void setRoomIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ROOMID_ISSET_ID, value);
  }

  /**
   * 申请的联盟Id
   */
  public long getTargetAllianceId() {
    return this.targetAllianceId;
  }

  /**
   * 申请的联盟Id
   */
  public CgEngageCheck setTargetAllianceId(long targetAllianceId) {
    this.targetAllianceId = targetAllianceId;
    setTargetAllianceIdIsSet(true);
    return this;
  }

  public void unsetTargetAllianceId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TARGETALLIANCEID_ISSET_ID);
  }

  /** Returns true if field targetAllianceId is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetAllianceId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TARGETALLIANCEID_ISSET_ID);
  }

  public void setTargetAllianceIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TARGETALLIANCEID_ISSET_ID, value);
  }

  /**
   * 类型1.同意 2.拒绝
   */
  public int getType() {
    return this.type;
  }

  /**
   * 类型1.同意 2.拒绝
   */
  public CgEngageCheck setType(int type) {
    this.type = type;
    setTypeIsSet(true);
    return this;
  }

  public void unsetType() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TYPE_ISSET_ID);
  }

  /** Returns true if field type is set (has been assigned a value) and false otherwise */
  public boolean isSetType() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TYPE_ISSET_ID);
  }

  public void setTypeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TYPE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ROOM_ID:
      if (value == null) {
        unsetRoomId();
      } else {
        setRoomId((java.lang.Long)value);
      }
      break;

    case TARGET_ALLIANCE_ID:
      if (value == null) {
        unsetTargetAllianceId();
      } else {
        setTargetAllianceId((java.lang.Long)value);
      }
      break;

    case TYPE:
      if (value == null) {
        unsetType();
      } else {
        setType((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ROOM_ID:
      return getRoomId();

    case TARGET_ALLIANCE_ID:
      return getTargetAllianceId();

    case TYPE:
      return getType();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ROOM_ID:
      return isSetRoomId();
    case TARGET_ALLIANCE_ID:
      return isSetTargetAllianceId();
    case TYPE:
      return isSetType();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgEngageCheck)
      return this.equals((CgEngageCheck)that);
    return false;
  }

  public boolean equals(CgEngageCheck that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_roomId = true && this.isSetRoomId();
    boolean that_present_roomId = true && that.isSetRoomId();
    if (this_present_roomId || that_present_roomId) {
      if (!(this_present_roomId && that_present_roomId))
        return false;
      if (this.roomId != that.roomId)
        return false;
    }

    boolean this_present_targetAllianceId = true && this.isSetTargetAllianceId();
    boolean that_present_targetAllianceId = true && that.isSetTargetAllianceId();
    if (this_present_targetAllianceId || that_present_targetAllianceId) {
      if (!(this_present_targetAllianceId && that_present_targetAllianceId))
        return false;
      if (this.targetAllianceId != that.targetAllianceId)
        return false;
    }

    boolean this_present_type = true && this.isSetType();
    boolean that_present_type = true && that.isSetType();
    if (this_present_type || that_present_type) {
      if (!(this_present_type && that_present_type))
        return false;
      if (this.type != that.type)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetRoomId()) ? 131071 : 524287);
    if (isSetRoomId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(roomId);

    hashCode = hashCode * 8191 + ((isSetTargetAllianceId()) ? 131071 : 524287);
    if (isSetTargetAllianceId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(targetAllianceId);

    hashCode = hashCode * 8191 + ((isSetType()) ? 131071 : 524287);
    if (isSetType())
      hashCode = hashCode * 8191 + type;

    return hashCode;
  }

  @Override
  public int compareTo(CgEngageCheck other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRoomId(), other.isSetRoomId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomId, other.roomId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTargetAllianceId(), other.isSetTargetAllianceId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetAllianceId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetAllianceId, other.targetAllianceId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetType(), other.isSetType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.type, other.type);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgEngageCheck(");
    boolean first = true;

    if (isSetRoomId()) {
      sb.append("roomId:");
      sb.append(this.roomId);
      first = false;
    }
    if (isSetTargetAllianceId()) {
      if (!first) sb.append(", ");
      sb.append("targetAllianceId:");
      sb.append(this.targetAllianceId);
      first = false;
    }
    if (isSetType()) {
      if (!first) sb.append(", ");
      sb.append("type:");
      sb.append(this.type);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgEngageCheckStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgEngageCheckStandardScheme getScheme() {
      return new CgEngageCheckStandardScheme();
    }
  }

  private static class CgEngageCheckStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgEngageCheck> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgEngageCheck struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROOM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roomId = iprot.readI64();
              struct.setRoomIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // TARGET_ALLIANCE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.targetAllianceId = iprot.readI64();
              struct.setTargetAllianceIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.type = iprot.readI32();
              struct.setTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgEngageCheck struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetRoomId()) {
        oprot.writeFieldBegin(ROOM_ID_FIELD_DESC);
        oprot.writeI64(struct.roomId);
        oprot.writeFieldEnd();
      }
      if (struct.isSetTargetAllianceId()) {
        oprot.writeFieldBegin(TARGET_ALLIANCE_ID_FIELD_DESC);
        oprot.writeI64(struct.targetAllianceId);
        oprot.writeFieldEnd();
      }
      if (struct.isSetType()) {
        oprot.writeFieldBegin(TYPE_FIELD_DESC);
        oprot.writeI32(struct.type);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgEngageCheckTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgEngageCheckTupleScheme getScheme() {
      return new CgEngageCheckTupleScheme();
    }
  }

  private static class CgEngageCheckTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgEngageCheck> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgEngageCheck struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRoomId()) {
        optionals.set(0);
      }
      if (struct.isSetTargetAllianceId()) {
        optionals.set(1);
      }
      if (struct.isSetType()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetRoomId()) {
        oprot.writeI64(struct.roomId);
      }
      if (struct.isSetTargetAllianceId()) {
        oprot.writeI64(struct.targetAllianceId);
      }
      if (struct.isSetType()) {
        oprot.writeI32(struct.type);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgEngageCheck struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.roomId = iprot.readI64();
        struct.setRoomIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.targetAllianceId = iprot.readI64();
        struct.setTargetAllianceIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.type = iprot.readI32();
        struct.setTypeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

