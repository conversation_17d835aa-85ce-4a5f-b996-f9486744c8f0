/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 推送募兵信息
 * @Message(7300)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcEnlistmentInfo implements org.apache.thrift.TBase<GcEnlistmentInfo, GcEnlistmentInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcEnlistmentInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcEnlistmentInfo");

  private static final org.apache.thrift.protocol.TField INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("info", org.apache.thrift.protocol.TType.STRUCT, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcEnlistmentInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcEnlistmentInfoTupleSchemeFactory();

  /**
   * 配置
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsEnlistmentInfo info; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 配置
     */
    INFO((short)1, "info");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // INFO
          return INFO;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.INFO, new org.apache.thrift.meta_data.FieldMetaData("info", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsEnlistmentInfo.class)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcEnlistmentInfo.class, metaDataMap);
  }

  public GcEnlistmentInfo() {
  }

  public GcEnlistmentInfo(
    com.lc.billion.icefire.protocol.structure.PsEnlistmentInfo info)
  {
    this();
    this.info = info;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcEnlistmentInfo(GcEnlistmentInfo other) {
    if (other.isSetInfo()) {
      this.info = new com.lc.billion.icefire.protocol.structure.PsEnlistmentInfo(other.info);
    }
  }

  public GcEnlistmentInfo deepCopy() {
    return new GcEnlistmentInfo(this);
  }

  @Override
  public void clear() {
    this.info = null;
  }

  /**
   * 配置
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.structure.PsEnlistmentInfo getInfo() {
    return this.info;
  }

  /**
   * 配置
   */
  public GcEnlistmentInfo setInfo(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsEnlistmentInfo info) {
    this.info = info;
    return this;
  }

  public void unsetInfo() {
    this.info = null;
  }

  /** Returns true if field info is set (has been assigned a value) and false otherwise */
  public boolean isSetInfo() {
    return this.info != null;
  }

  public void setInfoIsSet(boolean value) {
    if (!value) {
      this.info = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case INFO:
      if (value == null) {
        unsetInfo();
      } else {
        setInfo((com.lc.billion.icefire.protocol.structure.PsEnlistmentInfo)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case INFO:
      return getInfo();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case INFO:
      return isSetInfo();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcEnlistmentInfo)
      return this.equals((GcEnlistmentInfo)that);
    return false;
  }

  public boolean equals(GcEnlistmentInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_info = true && this.isSetInfo();
    boolean that_present_info = true && that.isSetInfo();
    if (this_present_info || that_present_info) {
      if (!(this_present_info && that_present_info))
        return false;
      if (!this.info.equals(that.info))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetInfo()) ? 131071 : 524287);
    if (isSetInfo())
      hashCode = hashCode * 8191 + info.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcEnlistmentInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetInfo(), other.isSetInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.info, other.info);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcEnlistmentInfo(");
    boolean first = true;

    sb.append("info:");
    if (this.info == null) {
      sb.append("null");
    } else {
      sb.append(this.info);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (info == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'info' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
    if (info != null) {
      info.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcEnlistmentInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcEnlistmentInfoStandardScheme getScheme() {
      return new GcEnlistmentInfoStandardScheme();
    }
  }

  private static class GcEnlistmentInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcEnlistmentInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcEnlistmentInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.info = new com.lc.billion.icefire.protocol.structure.PsEnlistmentInfo();
              struct.info.read(iprot);
              struct.setInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcEnlistmentInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.info != null) {
        oprot.writeFieldBegin(INFO_FIELD_DESC);
        struct.info.write(oprot);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcEnlistmentInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcEnlistmentInfoTupleScheme getScheme() {
      return new GcEnlistmentInfoTupleScheme();
    }
  }

  private static class GcEnlistmentInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcEnlistmentInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcEnlistmentInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.info.write(oprot);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcEnlistmentInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.info = new com.lc.billion.icefire.protocol.structure.PsEnlistmentInfo();
      struct.info.read(iprot);
      struct.setInfoIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

