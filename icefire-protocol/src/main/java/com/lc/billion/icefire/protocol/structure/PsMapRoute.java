/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 大地图上的路线
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsMapRoute implements org.apache.thrift.TBase<PsMapRoute, PsMapRoute._Fields>, java.io.Serializable, Cloneable, Comparable<PsMapRoute> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsMapRoute");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField START_X_FIELD_DESC = new org.apache.thrift.protocol.TField("startX", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField START_Y_FIELD_DESC = new org.apache.thrift.protocol.TField("startY", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField END_X_FIELD_DESC = new org.apache.thrift.protocol.TField("endX", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField END_Y_FIELD_DESC = new org.apache.thrift.protocol.TField("endY", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField BATTLE_RELATION_FIELD_DESC = new org.apache.thrift.protocol.TField("battleRelation", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField SPEED_RATIO_FIELD_DESC = new org.apache.thrift.protocol.TField("speedRatio", org.apache.thrift.protocol.TType.DOUBLE, (short)7);
  private static final org.apache.thrift.protocol.TField ARMY_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("armyType", org.apache.thrift.protocol.TType.I32, (short)10);
  private static final org.apache.thrift.protocol.TField START_POINT_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("startPointSize", org.apache.thrift.protocol.TType.STRUCT, (short)11);
  private static final org.apache.thrift.protocol.TField END_POINT_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("endPointSize", org.apache.thrift.protocol.TType.STRUCT, (short)12);
  private static final org.apache.thrift.protocol.TField MID_POINT_SIZES_FIELD_DESC = new org.apache.thrift.protocol.TField("midPointSizes", org.apache.thrift.protocol.TType.LIST, (short)13);
  private static final org.apache.thrift.protocol.TField SOURE_ALLIANCE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("soureAllianceId", org.apache.thrift.protocol.TType.I64, (short)14);
  private static final org.apache.thrift.protocol.TField TARGET_ALLIANCE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("targetAllianceId", org.apache.thrift.protocol.TType.I64, (short)15);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsMapRouteStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsMapRouteTupleSchemeFactory();

  /**
   * id
   */
  public long id; // required
  /**
   * 起点x坐标
   */
  public int startX; // required
  /**
   * 起点y坐标
   */
  public int startY; // required
  /**
   * 终点x坐标
   */
  public int endX; // required
  /**
   * 终点y坐标
   */
  public int endY; // required
  /**
   * 战斗关系
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsBattleRelation
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsBattleRelation battleRelation; // required
  /**
   * 加速倍率
   */
  public double speedRatio; // required
  /**
   * 这条线的行军类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyType
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsArmyType armyType; // optional
  /**
   * 起点占地格信息
   */
  public @org.apache.thrift.annotation.Nullable PsPointSize startPointSize; // optional
  /**
   * 终点占地格信息
   */
  public @org.apache.thrift.annotation.Nullable PsPointSize endPointSize; // optional
  /**
   * 中点占地格信息
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<PsPointSize> midPointSizes; // optional
  /**
   * 源联盟
   */
  public long soureAllianceId; // optional
  /**
   * 目的联盟
   */
  public long targetAllianceId; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * id
     */
    ID((short)1, "id"),
    /**
     * 起点x坐标
     */
    START_X((short)2, "startX"),
    /**
     * 起点y坐标
     */
    START_Y((short)3, "startY"),
    /**
     * 终点x坐标
     */
    END_X((short)4, "endX"),
    /**
     * 终点y坐标
     */
    END_Y((short)5, "endY"),
    /**
     * 战斗关系
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsBattleRelation
     */
    BATTLE_RELATION((short)6, "battleRelation"),
    /**
     * 加速倍率
     */
    SPEED_RATIO((short)7, "speedRatio"),
    /**
     * 这条线的行军类型
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsArmyType
     */
    ARMY_TYPE((short)10, "armyType"),
    /**
     * 起点占地格信息
     */
    START_POINT_SIZE((short)11, "startPointSize"),
    /**
     * 终点占地格信息
     */
    END_POINT_SIZE((short)12, "endPointSize"),
    /**
     * 中点占地格信息
     */
    MID_POINT_SIZES((short)13, "midPointSizes"),
    /**
     * 源联盟
     */
    SOURE_ALLIANCE_ID((short)14, "soureAllianceId"),
    /**
     * 目的联盟
     */
    TARGET_ALLIANCE_ID((short)15, "targetAllianceId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // START_X
          return START_X;
        case 3: // START_Y
          return START_Y;
        case 4: // END_X
          return END_X;
        case 5: // END_Y
          return END_Y;
        case 6: // BATTLE_RELATION
          return BATTLE_RELATION;
        case 7: // SPEED_RATIO
          return SPEED_RATIO;
        case 10: // ARMY_TYPE
          return ARMY_TYPE;
        case 11: // START_POINT_SIZE
          return START_POINT_SIZE;
        case 12: // END_POINT_SIZE
          return END_POINT_SIZE;
        case 13: // MID_POINT_SIZES
          return MID_POINT_SIZES;
        case 14: // SOURE_ALLIANCE_ID
          return SOURE_ALLIANCE_ID;
        case 15: // TARGET_ALLIANCE_ID
          return TARGET_ALLIANCE_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ID_ISSET_ID = 0;
  private static final int __STARTX_ISSET_ID = 1;
  private static final int __STARTY_ISSET_ID = 2;
  private static final int __ENDX_ISSET_ID = 3;
  private static final int __ENDY_ISSET_ID = 4;
  private static final int __SPEEDRATIO_ISSET_ID = 5;
  private static final int __SOUREALLIANCEID_ISSET_ID = 6;
  private static final int __TARGETALLIANCEID_ISSET_ID = 7;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ARMY_TYPE,_Fields.START_POINT_SIZE,_Fields.END_POINT_SIZE,_Fields.MID_POINT_SIZES,_Fields.SOURE_ALLIANCE_ID,_Fields.TARGET_ALLIANCE_ID};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.START_X, new org.apache.thrift.meta_data.FieldMetaData("startX", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.START_Y, new org.apache.thrift.meta_data.FieldMetaData("startY", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.END_X, new org.apache.thrift.meta_data.FieldMetaData("endX", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.END_Y, new org.apache.thrift.meta_data.FieldMetaData("endY", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.BATTLE_RELATION, new org.apache.thrift.meta_data.FieldMetaData("battleRelation", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsBattleRelation.class)));
    tmpMap.put(_Fields.SPEED_RATIO, new org.apache.thrift.meta_data.FieldMetaData("speedRatio", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    tmpMap.put(_Fields.ARMY_TYPE, new org.apache.thrift.meta_data.FieldMetaData("armyType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsArmyType.class)));
    tmpMap.put(_Fields.START_POINT_SIZE, new org.apache.thrift.meta_data.FieldMetaData("startPointSize", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT        , "PsPointSize")));
    tmpMap.put(_Fields.END_POINT_SIZE, new org.apache.thrift.meta_data.FieldMetaData("endPointSize", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT        , "PsPointSize")));
    tmpMap.put(_Fields.MID_POINT_SIZES, new org.apache.thrift.meta_data.FieldMetaData("midPointSizes", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT            , "PsPointSize"))));
    tmpMap.put(_Fields.SOURE_ALLIANCE_ID, new org.apache.thrift.meta_data.FieldMetaData("soureAllianceId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TARGET_ALLIANCE_ID, new org.apache.thrift.meta_data.FieldMetaData("targetAllianceId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsMapRoute.class, metaDataMap);
  }

  public PsMapRoute() {
  }

  public PsMapRoute(
    long id,
    int startX,
    int startY,
    int endX,
    int endY,
    com.lc.billion.icefire.protocol.constant.PsBattleRelation battleRelation,
    double speedRatio)
  {
    this();
    this.id = id;
    setIdIsSet(true);
    this.startX = startX;
    setStartXIsSet(true);
    this.startY = startY;
    setStartYIsSet(true);
    this.endX = endX;
    setEndXIsSet(true);
    this.endY = endY;
    setEndYIsSet(true);
    this.battleRelation = battleRelation;
    this.speedRatio = speedRatio;
    setSpeedRatioIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsMapRoute(PsMapRoute other) {
    __isset_bitfield = other.__isset_bitfield;
    this.id = other.id;
    this.startX = other.startX;
    this.startY = other.startY;
    this.endX = other.endX;
    this.endY = other.endY;
    if (other.isSetBattleRelation()) {
      this.battleRelation = other.battleRelation;
    }
    this.speedRatio = other.speedRatio;
    if (other.isSetArmyType()) {
      this.armyType = other.armyType;
    }
    if (other.isSetStartPointSize()) {
      this.startPointSize = new PsPointSize(other.startPointSize);
    }
    if (other.isSetEndPointSize()) {
      this.endPointSize = new PsPointSize(other.endPointSize);
    }
    if (other.isSetMidPointSizes()) {
      java.util.List<PsPointSize> __this__midPointSizes = new java.util.ArrayList<PsPointSize>(other.midPointSizes.size());
      for (PsPointSize other_element : other.midPointSizes) {
        __this__midPointSizes.add(new PsPointSize(other_element));
      }
      this.midPointSizes = __this__midPointSizes;
    }
    this.soureAllianceId = other.soureAllianceId;
    this.targetAllianceId = other.targetAllianceId;
  }

  public PsMapRoute deepCopy() {
    return new PsMapRoute(this);
  }

  @Override
  public void clear() {
    setIdIsSet(false);
    this.id = 0;
    setStartXIsSet(false);
    this.startX = 0;
    setStartYIsSet(false);
    this.startY = 0;
    setEndXIsSet(false);
    this.endX = 0;
    setEndYIsSet(false);
    this.endY = 0;
    this.battleRelation = null;
    setSpeedRatioIsSet(false);
    this.speedRatio = 0.0;
    this.armyType = null;
    this.startPointSize = null;
    this.endPointSize = null;
    this.midPointSizes = null;
    setSoureAllianceIdIsSet(false);
    this.soureAllianceId = 0;
    setTargetAllianceIdIsSet(false);
    this.targetAllianceId = 0;
  }

  /**
   * id
   */
  public long getId() {
    return this.id;
  }

  /**
   * id
   */
  public PsMapRoute setId(long id) {
    this.id = id;
    setIdIsSet(true);
    return this;
  }

  public void unsetId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
  }

  public void setIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
  }

  /**
   * 起点x坐标
   */
  public int getStartX() {
    return this.startX;
  }

  /**
   * 起点x坐标
   */
  public PsMapRoute setStartX(int startX) {
    this.startX = startX;
    setStartXIsSet(true);
    return this;
  }

  public void unsetStartX() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STARTX_ISSET_ID);
  }

  /** Returns true if field startX is set (has been assigned a value) and false otherwise */
  public boolean isSetStartX() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STARTX_ISSET_ID);
  }

  public void setStartXIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STARTX_ISSET_ID, value);
  }

  /**
   * 起点y坐标
   */
  public int getStartY() {
    return this.startY;
  }

  /**
   * 起点y坐标
   */
  public PsMapRoute setStartY(int startY) {
    this.startY = startY;
    setStartYIsSet(true);
    return this;
  }

  public void unsetStartY() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STARTY_ISSET_ID);
  }

  /** Returns true if field startY is set (has been assigned a value) and false otherwise */
  public boolean isSetStartY() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STARTY_ISSET_ID);
  }

  public void setStartYIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STARTY_ISSET_ID, value);
  }

  /**
   * 终点x坐标
   */
  public int getEndX() {
    return this.endX;
  }

  /**
   * 终点x坐标
   */
  public PsMapRoute setEndX(int endX) {
    this.endX = endX;
    setEndXIsSet(true);
    return this;
  }

  public void unsetEndX() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ENDX_ISSET_ID);
  }

  /** Returns true if field endX is set (has been assigned a value) and false otherwise */
  public boolean isSetEndX() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ENDX_ISSET_ID);
  }

  public void setEndXIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ENDX_ISSET_ID, value);
  }

  /**
   * 终点y坐标
   */
  public int getEndY() {
    return this.endY;
  }

  /**
   * 终点y坐标
   */
  public PsMapRoute setEndY(int endY) {
    this.endY = endY;
    setEndYIsSet(true);
    return this;
  }

  public void unsetEndY() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ENDY_ISSET_ID);
  }

  /** Returns true if field endY is set (has been assigned a value) and false otherwise */
  public boolean isSetEndY() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ENDY_ISSET_ID);
  }

  public void setEndYIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ENDY_ISSET_ID, value);
  }

  /**
   * 战斗关系
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsBattleRelation
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsBattleRelation getBattleRelation() {
    return this.battleRelation;
  }

  /**
   * 战斗关系
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsBattleRelation
   */
  public PsMapRoute setBattleRelation(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsBattleRelation battleRelation) {
    this.battleRelation = battleRelation;
    return this;
  }

  public void unsetBattleRelation() {
    this.battleRelation = null;
  }

  /** Returns true if field battleRelation is set (has been assigned a value) and false otherwise */
  public boolean isSetBattleRelation() {
    return this.battleRelation != null;
  }

  public void setBattleRelationIsSet(boolean value) {
    if (!value) {
      this.battleRelation = null;
    }
  }

  /**
   * 加速倍率
   */
  public double getSpeedRatio() {
    return this.speedRatio;
  }

  /**
   * 加速倍率
   */
  public PsMapRoute setSpeedRatio(double speedRatio) {
    this.speedRatio = speedRatio;
    setSpeedRatioIsSet(true);
    return this;
  }

  public void unsetSpeedRatio() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SPEEDRATIO_ISSET_ID);
  }

  /** Returns true if field speedRatio is set (has been assigned a value) and false otherwise */
  public boolean isSetSpeedRatio() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SPEEDRATIO_ISSET_ID);
  }

  public void setSpeedRatioIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SPEEDRATIO_ISSET_ID, value);
  }

  /**
   * 这条线的行军类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyType
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsArmyType getArmyType() {
    return this.armyType;
  }

  /**
   * 这条线的行军类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyType
   */
  public PsMapRoute setArmyType(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsArmyType armyType) {
    this.armyType = armyType;
    return this;
  }

  public void unsetArmyType() {
    this.armyType = null;
  }

  /** Returns true if field armyType is set (has been assigned a value) and false otherwise */
  public boolean isSetArmyType() {
    return this.armyType != null;
  }

  public void setArmyTypeIsSet(boolean value) {
    if (!value) {
      this.armyType = null;
    }
  }

  /**
   * 起点占地格信息
   */
  @org.apache.thrift.annotation.Nullable
  public PsPointSize getStartPointSize() {
    return this.startPointSize;
  }

  /**
   * 起点占地格信息
   */
  public PsMapRoute setStartPointSize(@org.apache.thrift.annotation.Nullable PsPointSize startPointSize) {
    this.startPointSize = startPointSize;
    return this;
  }

  public void unsetStartPointSize() {
    this.startPointSize = null;
  }

  /** Returns true if field startPointSize is set (has been assigned a value) and false otherwise */
  public boolean isSetStartPointSize() {
    return this.startPointSize != null;
  }

  public void setStartPointSizeIsSet(boolean value) {
    if (!value) {
      this.startPointSize = null;
    }
  }

  /**
   * 终点占地格信息
   */
  @org.apache.thrift.annotation.Nullable
  public PsPointSize getEndPointSize() {
    return this.endPointSize;
  }

  /**
   * 终点占地格信息
   */
  public PsMapRoute setEndPointSize(@org.apache.thrift.annotation.Nullable PsPointSize endPointSize) {
    this.endPointSize = endPointSize;
    return this;
  }

  public void unsetEndPointSize() {
    this.endPointSize = null;
  }

  /** Returns true if field endPointSize is set (has been assigned a value) and false otherwise */
  public boolean isSetEndPointSize() {
    return this.endPointSize != null;
  }

  public void setEndPointSizeIsSet(boolean value) {
    if (!value) {
      this.endPointSize = null;
    }
  }

  public int getMidPointSizesSize() {
    return (this.midPointSizes == null) ? 0 : this.midPointSizes.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<PsPointSize> getMidPointSizesIterator() {
    return (this.midPointSizes == null) ? null : this.midPointSizes.iterator();
  }

  public void addToMidPointSizes(PsPointSize elem) {
    if (this.midPointSizes == null) {
      this.midPointSizes = new java.util.ArrayList<PsPointSize>();
    }
    this.midPointSizes.add(elem);
  }

  /**
   * 中点占地格信息
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<PsPointSize> getMidPointSizes() {
    return this.midPointSizes;
  }

  /**
   * 中点占地格信息
   */
  public PsMapRoute setMidPointSizes(@org.apache.thrift.annotation.Nullable java.util.List<PsPointSize> midPointSizes) {
    this.midPointSizes = midPointSizes;
    return this;
  }

  public void unsetMidPointSizes() {
    this.midPointSizes = null;
  }

  /** Returns true if field midPointSizes is set (has been assigned a value) and false otherwise */
  public boolean isSetMidPointSizes() {
    return this.midPointSizes != null;
  }

  public void setMidPointSizesIsSet(boolean value) {
    if (!value) {
      this.midPointSizes = null;
    }
  }

  /**
   * 源联盟
   */
  public long getSoureAllianceId() {
    return this.soureAllianceId;
  }

  /**
   * 源联盟
   */
  public PsMapRoute setSoureAllianceId(long soureAllianceId) {
    this.soureAllianceId = soureAllianceId;
    setSoureAllianceIdIsSet(true);
    return this;
  }

  public void unsetSoureAllianceId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SOUREALLIANCEID_ISSET_ID);
  }

  /** Returns true if field soureAllianceId is set (has been assigned a value) and false otherwise */
  public boolean isSetSoureAllianceId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SOUREALLIANCEID_ISSET_ID);
  }

  public void setSoureAllianceIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SOUREALLIANCEID_ISSET_ID, value);
  }

  /**
   * 目的联盟
   */
  public long getTargetAllianceId() {
    return this.targetAllianceId;
  }

  /**
   * 目的联盟
   */
  public PsMapRoute setTargetAllianceId(long targetAllianceId) {
    this.targetAllianceId = targetAllianceId;
    setTargetAllianceIdIsSet(true);
    return this;
  }

  public void unsetTargetAllianceId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TARGETALLIANCEID_ISSET_ID);
  }

  /** Returns true if field targetAllianceId is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetAllianceId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TARGETALLIANCEID_ISSET_ID);
  }

  public void setTargetAllianceIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TARGETALLIANCEID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((java.lang.Long)value);
      }
      break;

    case START_X:
      if (value == null) {
        unsetStartX();
      } else {
        setStartX((java.lang.Integer)value);
      }
      break;

    case START_Y:
      if (value == null) {
        unsetStartY();
      } else {
        setStartY((java.lang.Integer)value);
      }
      break;

    case END_X:
      if (value == null) {
        unsetEndX();
      } else {
        setEndX((java.lang.Integer)value);
      }
      break;

    case END_Y:
      if (value == null) {
        unsetEndY();
      } else {
        setEndY((java.lang.Integer)value);
      }
      break;

    case BATTLE_RELATION:
      if (value == null) {
        unsetBattleRelation();
      } else {
        setBattleRelation((com.lc.billion.icefire.protocol.constant.PsBattleRelation)value);
      }
      break;

    case SPEED_RATIO:
      if (value == null) {
        unsetSpeedRatio();
      } else {
        setSpeedRatio((java.lang.Double)value);
      }
      break;

    case ARMY_TYPE:
      if (value == null) {
        unsetArmyType();
      } else {
        setArmyType((com.lc.billion.icefire.protocol.constant.PsArmyType)value);
      }
      break;

    case START_POINT_SIZE:
      if (value == null) {
        unsetStartPointSize();
      } else {
        setStartPointSize((PsPointSize)value);
      }
      break;

    case END_POINT_SIZE:
      if (value == null) {
        unsetEndPointSize();
      } else {
        setEndPointSize((PsPointSize)value);
      }
      break;

    case MID_POINT_SIZES:
      if (value == null) {
        unsetMidPointSizes();
      } else {
        setMidPointSizes((java.util.List<PsPointSize>)value);
      }
      break;

    case SOURE_ALLIANCE_ID:
      if (value == null) {
        unsetSoureAllianceId();
      } else {
        setSoureAllianceId((java.lang.Long)value);
      }
      break;

    case TARGET_ALLIANCE_ID:
      if (value == null) {
        unsetTargetAllianceId();
      } else {
        setTargetAllianceId((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case START_X:
      return getStartX();

    case START_Y:
      return getStartY();

    case END_X:
      return getEndX();

    case END_Y:
      return getEndY();

    case BATTLE_RELATION:
      return getBattleRelation();

    case SPEED_RATIO:
      return getSpeedRatio();

    case ARMY_TYPE:
      return getArmyType();

    case START_POINT_SIZE:
      return getStartPointSize();

    case END_POINT_SIZE:
      return getEndPointSize();

    case MID_POINT_SIZES:
      return getMidPointSizes();

    case SOURE_ALLIANCE_ID:
      return getSoureAllianceId();

    case TARGET_ALLIANCE_ID:
      return getTargetAllianceId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case START_X:
      return isSetStartX();
    case START_Y:
      return isSetStartY();
    case END_X:
      return isSetEndX();
    case END_Y:
      return isSetEndY();
    case BATTLE_RELATION:
      return isSetBattleRelation();
    case SPEED_RATIO:
      return isSetSpeedRatio();
    case ARMY_TYPE:
      return isSetArmyType();
    case START_POINT_SIZE:
      return isSetStartPointSize();
    case END_POINT_SIZE:
      return isSetEndPointSize();
    case MID_POINT_SIZES:
      return isSetMidPointSizes();
    case SOURE_ALLIANCE_ID:
      return isSetSoureAllianceId();
    case TARGET_ALLIANCE_ID:
      return isSetTargetAllianceId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsMapRoute)
      return this.equals((PsMapRoute)that);
    return false;
  }

  public boolean equals(PsMapRoute that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_id = true;
    boolean that_present_id = true;
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (this.id != that.id)
        return false;
    }

    boolean this_present_startX = true;
    boolean that_present_startX = true;
    if (this_present_startX || that_present_startX) {
      if (!(this_present_startX && that_present_startX))
        return false;
      if (this.startX != that.startX)
        return false;
    }

    boolean this_present_startY = true;
    boolean that_present_startY = true;
    if (this_present_startY || that_present_startY) {
      if (!(this_present_startY && that_present_startY))
        return false;
      if (this.startY != that.startY)
        return false;
    }

    boolean this_present_endX = true;
    boolean that_present_endX = true;
    if (this_present_endX || that_present_endX) {
      if (!(this_present_endX && that_present_endX))
        return false;
      if (this.endX != that.endX)
        return false;
    }

    boolean this_present_endY = true;
    boolean that_present_endY = true;
    if (this_present_endY || that_present_endY) {
      if (!(this_present_endY && that_present_endY))
        return false;
      if (this.endY != that.endY)
        return false;
    }

    boolean this_present_battleRelation = true && this.isSetBattleRelation();
    boolean that_present_battleRelation = true && that.isSetBattleRelation();
    if (this_present_battleRelation || that_present_battleRelation) {
      if (!(this_present_battleRelation && that_present_battleRelation))
        return false;
      if (!this.battleRelation.equals(that.battleRelation))
        return false;
    }

    boolean this_present_speedRatio = true;
    boolean that_present_speedRatio = true;
    if (this_present_speedRatio || that_present_speedRatio) {
      if (!(this_present_speedRatio && that_present_speedRatio))
        return false;
      if (this.speedRatio != that.speedRatio)
        return false;
    }

    boolean this_present_armyType = true && this.isSetArmyType();
    boolean that_present_armyType = true && that.isSetArmyType();
    if (this_present_armyType || that_present_armyType) {
      if (!(this_present_armyType && that_present_armyType))
        return false;
      if (!this.armyType.equals(that.armyType))
        return false;
    }

    boolean this_present_startPointSize = true && this.isSetStartPointSize();
    boolean that_present_startPointSize = true && that.isSetStartPointSize();
    if (this_present_startPointSize || that_present_startPointSize) {
      if (!(this_present_startPointSize && that_present_startPointSize))
        return false;
      if (!this.startPointSize.equals(that.startPointSize))
        return false;
    }

    boolean this_present_endPointSize = true && this.isSetEndPointSize();
    boolean that_present_endPointSize = true && that.isSetEndPointSize();
    if (this_present_endPointSize || that_present_endPointSize) {
      if (!(this_present_endPointSize && that_present_endPointSize))
        return false;
      if (!this.endPointSize.equals(that.endPointSize))
        return false;
    }

    boolean this_present_midPointSizes = true && this.isSetMidPointSizes();
    boolean that_present_midPointSizes = true && that.isSetMidPointSizes();
    if (this_present_midPointSizes || that_present_midPointSizes) {
      if (!(this_present_midPointSizes && that_present_midPointSizes))
        return false;
      if (!this.midPointSizes.equals(that.midPointSizes))
        return false;
    }

    boolean this_present_soureAllianceId = true && this.isSetSoureAllianceId();
    boolean that_present_soureAllianceId = true && that.isSetSoureAllianceId();
    if (this_present_soureAllianceId || that_present_soureAllianceId) {
      if (!(this_present_soureAllianceId && that_present_soureAllianceId))
        return false;
      if (this.soureAllianceId != that.soureAllianceId)
        return false;
    }

    boolean this_present_targetAllianceId = true && this.isSetTargetAllianceId();
    boolean that_present_targetAllianceId = true && that.isSetTargetAllianceId();
    if (this_present_targetAllianceId || that_present_targetAllianceId) {
      if (!(this_present_targetAllianceId && that_present_targetAllianceId))
        return false;
      if (this.targetAllianceId != that.targetAllianceId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(id);

    hashCode = hashCode * 8191 + startX;

    hashCode = hashCode * 8191 + startY;

    hashCode = hashCode * 8191 + endX;

    hashCode = hashCode * 8191 + endY;

    hashCode = hashCode * 8191 + ((isSetBattleRelation()) ? 131071 : 524287);
    if (isSetBattleRelation())
      hashCode = hashCode * 8191 + battleRelation.getValue();

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(speedRatio);

    hashCode = hashCode * 8191 + ((isSetArmyType()) ? 131071 : 524287);
    if (isSetArmyType())
      hashCode = hashCode * 8191 + armyType.getValue();

    hashCode = hashCode * 8191 + ((isSetStartPointSize()) ? 131071 : 524287);
    if (isSetStartPointSize())
      hashCode = hashCode * 8191 + startPointSize.hashCode();

    hashCode = hashCode * 8191 + ((isSetEndPointSize()) ? 131071 : 524287);
    if (isSetEndPointSize())
      hashCode = hashCode * 8191 + endPointSize.hashCode();

    hashCode = hashCode * 8191 + ((isSetMidPointSizes()) ? 131071 : 524287);
    if (isSetMidPointSizes())
      hashCode = hashCode * 8191 + midPointSizes.hashCode();

    hashCode = hashCode * 8191 + ((isSetSoureAllianceId()) ? 131071 : 524287);
    if (isSetSoureAllianceId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(soureAllianceId);

    hashCode = hashCode * 8191 + ((isSetTargetAllianceId()) ? 131071 : 524287);
    if (isSetTargetAllianceId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(targetAllianceId);

    return hashCode;
  }

  @Override
  public int compareTo(PsMapRoute other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetId(), other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartX(), other.isSetStartX());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartX()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startX, other.startX);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartY(), other.isSetStartY());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartY()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startY, other.startY);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEndX(), other.isSetEndX());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndX()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endX, other.endX);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEndY(), other.isSetEndY());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndY()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endY, other.endY);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBattleRelation(), other.isSetBattleRelation());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBattleRelation()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.battleRelation, other.battleRelation);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSpeedRatio(), other.isSetSpeedRatio());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSpeedRatio()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.speedRatio, other.speedRatio);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetArmyType(), other.isSetArmyType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetArmyType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.armyType, other.armyType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartPointSize(), other.isSetStartPointSize());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartPointSize()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startPointSize, other.startPointSize);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEndPointSize(), other.isSetEndPointSize());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndPointSize()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endPointSize, other.endPointSize);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMidPointSizes(), other.isSetMidPointSizes());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMidPointSizes()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.midPointSizes, other.midPointSizes);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSoureAllianceId(), other.isSetSoureAllianceId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSoureAllianceId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.soureAllianceId, other.soureAllianceId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTargetAllianceId(), other.isSetTargetAllianceId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetAllianceId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetAllianceId, other.targetAllianceId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsMapRoute(");
    boolean first = true;

    sb.append("id:");
    sb.append(this.id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("startX:");
    sb.append(this.startX);
    first = false;
    if (!first) sb.append(", ");
    sb.append("startY:");
    sb.append(this.startY);
    first = false;
    if (!first) sb.append(", ");
    sb.append("endX:");
    sb.append(this.endX);
    first = false;
    if (!first) sb.append(", ");
    sb.append("endY:");
    sb.append(this.endY);
    first = false;
    if (!first) sb.append(", ");
    sb.append("battleRelation:");
    if (this.battleRelation == null) {
      sb.append("null");
    } else {
      sb.append(this.battleRelation);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("speedRatio:");
    sb.append(this.speedRatio);
    first = false;
    if (isSetArmyType()) {
      if (!first) sb.append(", ");
      sb.append("armyType:");
      if (this.armyType == null) {
        sb.append("null");
      } else {
        sb.append(this.armyType);
      }
      first = false;
    }
    if (isSetStartPointSize()) {
      if (!first) sb.append(", ");
      sb.append("startPointSize:");
      if (this.startPointSize == null) {
        sb.append("null");
      } else {
        sb.append(this.startPointSize);
      }
      first = false;
    }
    if (isSetEndPointSize()) {
      if (!first) sb.append(", ");
      sb.append("endPointSize:");
      if (this.endPointSize == null) {
        sb.append("null");
      } else {
        sb.append(this.endPointSize);
      }
      first = false;
    }
    if (isSetMidPointSizes()) {
      if (!first) sb.append(", ");
      sb.append("midPointSizes:");
      if (this.midPointSizes == null) {
        sb.append("null");
      } else {
        sb.append(this.midPointSizes);
      }
      first = false;
    }
    if (isSetSoureAllianceId()) {
      if (!first) sb.append(", ");
      sb.append("soureAllianceId:");
      sb.append(this.soureAllianceId);
      first = false;
    }
    if (isSetTargetAllianceId()) {
      if (!first) sb.append(", ");
      sb.append("targetAllianceId:");
      sb.append(this.targetAllianceId);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'id' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'startX' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'startY' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'endX' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'endY' because it's a primitive and you chose the non-beans generator.
    if (battleRelation == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'battleRelation' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'speedRatio' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsMapRouteStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsMapRouteStandardScheme getScheme() {
      return new PsMapRouteStandardScheme();
    }
  }

  private static class PsMapRouteStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsMapRoute> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsMapRoute struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.id = iprot.readI64();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // START_X
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.startX = iprot.readI32();
              struct.setStartXIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // START_Y
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.startY = iprot.readI32();
              struct.setStartYIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // END_X
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.endX = iprot.readI32();
              struct.setEndXIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // END_Y
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.endY = iprot.readI32();
              struct.setEndYIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // BATTLE_RELATION
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.battleRelation = com.lc.billion.icefire.protocol.constant.PsBattleRelation.findByValue(iprot.readI32());
              struct.setBattleRelationIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // SPEED_RATIO
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.speedRatio = iprot.readDouble();
              struct.setSpeedRatioIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // ARMY_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.armyType = com.lc.billion.icefire.protocol.constant.PsArmyType.findByValue(iprot.readI32());
              struct.setArmyTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // START_POINT_SIZE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.startPointSize = new PsPointSize();
              struct.startPointSize.read(iprot);
              struct.setStartPointSizeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // END_POINT_SIZE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.endPointSize = new PsPointSize();
              struct.endPointSize.read(iprot);
              struct.setEndPointSizeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // MID_POINT_SIZES
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.midPointSizes = new java.util.ArrayList<PsPointSize>(_list0.size);
                @org.apache.thrift.annotation.Nullable PsPointSize _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new PsPointSize();
                  _elem1.read(iprot);
                  struct.midPointSizes.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setMidPointSizesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // SOURE_ALLIANCE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.soureAllianceId = iprot.readI64();
              struct.setSoureAllianceIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // TARGET_ALLIANCE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.targetAllianceId = iprot.readI64();
              struct.setTargetAllianceIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'id' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetStartX()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'startX' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetStartY()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'startY' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetEndX()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'endX' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetEndY()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'endY' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetSpeedRatio()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'speedRatio' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsMapRoute struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ID_FIELD_DESC);
      oprot.writeI64(struct.id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(START_X_FIELD_DESC);
      oprot.writeI32(struct.startX);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(START_Y_FIELD_DESC);
      oprot.writeI32(struct.startY);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(END_X_FIELD_DESC);
      oprot.writeI32(struct.endX);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(END_Y_FIELD_DESC);
      oprot.writeI32(struct.endY);
      oprot.writeFieldEnd();
      if (struct.battleRelation != null) {
        oprot.writeFieldBegin(BATTLE_RELATION_FIELD_DESC);
        oprot.writeI32(struct.battleRelation.getValue());
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SPEED_RATIO_FIELD_DESC);
      oprot.writeDouble(struct.speedRatio);
      oprot.writeFieldEnd();
      if (struct.armyType != null) {
        if (struct.isSetArmyType()) {
          oprot.writeFieldBegin(ARMY_TYPE_FIELD_DESC);
          oprot.writeI32(struct.armyType.getValue());
          oprot.writeFieldEnd();
        }
      }
      if (struct.startPointSize != null) {
        if (struct.isSetStartPointSize()) {
          oprot.writeFieldBegin(START_POINT_SIZE_FIELD_DESC);
          struct.startPointSize.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.endPointSize != null) {
        if (struct.isSetEndPointSize()) {
          oprot.writeFieldBegin(END_POINT_SIZE_FIELD_DESC);
          struct.endPointSize.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.midPointSizes != null) {
        if (struct.isSetMidPointSizes()) {
          oprot.writeFieldBegin(MID_POINT_SIZES_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.midPointSizes.size()));
            for (PsPointSize _iter3 : struct.midPointSizes)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetSoureAllianceId()) {
        oprot.writeFieldBegin(SOURE_ALLIANCE_ID_FIELD_DESC);
        oprot.writeI64(struct.soureAllianceId);
        oprot.writeFieldEnd();
      }
      if (struct.isSetTargetAllianceId()) {
        oprot.writeFieldBegin(TARGET_ALLIANCE_ID_FIELD_DESC);
        oprot.writeI64(struct.targetAllianceId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsMapRouteTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsMapRouteTupleScheme getScheme() {
      return new PsMapRouteTupleScheme();
    }
  }

  private static class PsMapRouteTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsMapRoute> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsMapRoute struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.id);
      oprot.writeI32(struct.startX);
      oprot.writeI32(struct.startY);
      oprot.writeI32(struct.endX);
      oprot.writeI32(struct.endY);
      oprot.writeI32(struct.battleRelation.getValue());
      oprot.writeDouble(struct.speedRatio);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetArmyType()) {
        optionals.set(0);
      }
      if (struct.isSetStartPointSize()) {
        optionals.set(1);
      }
      if (struct.isSetEndPointSize()) {
        optionals.set(2);
      }
      if (struct.isSetMidPointSizes()) {
        optionals.set(3);
      }
      if (struct.isSetSoureAllianceId()) {
        optionals.set(4);
      }
      if (struct.isSetTargetAllianceId()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetArmyType()) {
        oprot.writeI32(struct.armyType.getValue());
      }
      if (struct.isSetStartPointSize()) {
        struct.startPointSize.write(oprot);
      }
      if (struct.isSetEndPointSize()) {
        struct.endPointSize.write(oprot);
      }
      if (struct.isSetMidPointSizes()) {
        {
          oprot.writeI32(struct.midPointSizes.size());
          for (PsPointSize _iter4 : struct.midPointSizes)
          {
            _iter4.write(oprot);
          }
        }
      }
      if (struct.isSetSoureAllianceId()) {
        oprot.writeI64(struct.soureAllianceId);
      }
      if (struct.isSetTargetAllianceId()) {
        oprot.writeI64(struct.targetAllianceId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsMapRoute struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.id = iprot.readI64();
      struct.setIdIsSet(true);
      struct.startX = iprot.readI32();
      struct.setStartXIsSet(true);
      struct.startY = iprot.readI32();
      struct.setStartYIsSet(true);
      struct.endX = iprot.readI32();
      struct.setEndXIsSet(true);
      struct.endY = iprot.readI32();
      struct.setEndYIsSet(true);
      struct.battleRelation = com.lc.billion.icefire.protocol.constant.PsBattleRelation.findByValue(iprot.readI32());
      struct.setBattleRelationIsSet(true);
      struct.speedRatio = iprot.readDouble();
      struct.setSpeedRatioIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.armyType = com.lc.billion.icefire.protocol.constant.PsArmyType.findByValue(iprot.readI32());
        struct.setArmyTypeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.startPointSize = new PsPointSize();
        struct.startPointSize.read(iprot);
        struct.setStartPointSizeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.endPointSize = new PsPointSize();
        struct.endPointSize.read(iprot);
        struct.setEndPointSizeIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.midPointSizes = new java.util.ArrayList<PsPointSize>(_list5.size);
          @org.apache.thrift.annotation.Nullable PsPointSize _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new PsPointSize();
            _elem6.read(iprot);
            struct.midPointSizes.add(_elem6);
          }
        }
        struct.setMidPointSizesIsSet(true);
      }
      if (incoming.get(4)) {
        struct.soureAllianceId = iprot.readI64();
        struct.setSoureAllianceIdIsSet(true);
      }
      if (incoming.get(5)) {
        struct.targetAllianceId = iprot.readI64();
        struct.setTargetAllianceIdIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

