/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgRescueResult implements org.apache.thrift.TBase<CgRescueResult, CgRescueResult._Fields>, java.io.Serializable, Cloneable, Comparable<CgRescueResult> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgRescueResult");

  private static final org.apache.thrift.protocol.TField ERROR_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("errorCode", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField CURRENT_FIELD_DESC = new org.apache.thrift.protocol.TField("current", org.apache.thrift.protocol.TType.I32, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgRescueResultStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgRescueResultTupleSchemeFactory();

  /**
   * 营救结果
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsRescueErrorCode
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsRescueErrorCode errorCode; // required
  /**
   * 当前进度
   */
  public int current; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 营救结果
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsRescueErrorCode
     */
    ERROR_CODE((short)1, "errorCode"),
    /**
     * 当前进度
     */
    CURRENT((short)2, "current");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ERROR_CODE
          return ERROR_CODE;
        case 2: // CURRENT
          return CURRENT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __CURRENT_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.CURRENT};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ERROR_CODE, new org.apache.thrift.meta_data.FieldMetaData("errorCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsRescueErrorCode.class)));
    tmpMap.put(_Fields.CURRENT, new org.apache.thrift.meta_data.FieldMetaData("current", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgRescueResult.class, metaDataMap);
  }

  public CgRescueResult() {
  }

  public CgRescueResult(
    com.lc.billion.icefire.protocol.constant.PsRescueErrorCode errorCode)
  {
    this();
    this.errorCode = errorCode;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgRescueResult(CgRescueResult other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetErrorCode()) {
      this.errorCode = other.errorCode;
    }
    this.current = other.current;
  }

  public CgRescueResult deepCopy() {
    return new CgRescueResult(this);
  }

  @Override
  public void clear() {
    this.errorCode = null;
    setCurrentIsSet(false);
    this.current = 0;
  }

  /**
   * 营救结果
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsRescueErrorCode
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsRescueErrorCode getErrorCode() {
    return this.errorCode;
  }

  /**
   * 营救结果
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsRescueErrorCode
   */
  public CgRescueResult setErrorCode(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsRescueErrorCode errorCode) {
    this.errorCode = errorCode;
    return this;
  }

  public void unsetErrorCode() {
    this.errorCode = null;
  }

  /** Returns true if field errorCode is set (has been assigned a value) and false otherwise */
  public boolean isSetErrorCode() {
    return this.errorCode != null;
  }

  public void setErrorCodeIsSet(boolean value) {
    if (!value) {
      this.errorCode = null;
    }
  }

  /**
   * 当前进度
   */
  public int getCurrent() {
    return this.current;
  }

  /**
   * 当前进度
   */
  public CgRescueResult setCurrent(int current) {
    this.current = current;
    setCurrentIsSet(true);
    return this;
  }

  public void unsetCurrent() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CURRENT_ISSET_ID);
  }

  /** Returns true if field current is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrent() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CURRENT_ISSET_ID);
  }

  public void setCurrentIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CURRENT_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ERROR_CODE:
      if (value == null) {
        unsetErrorCode();
      } else {
        setErrorCode((com.lc.billion.icefire.protocol.constant.PsRescueErrorCode)value);
      }
      break;

    case CURRENT:
      if (value == null) {
        unsetCurrent();
      } else {
        setCurrent((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ERROR_CODE:
      return getErrorCode();

    case CURRENT:
      return getCurrent();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ERROR_CODE:
      return isSetErrorCode();
    case CURRENT:
      return isSetCurrent();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgRescueResult)
      return this.equals((CgRescueResult)that);
    return false;
  }

  public boolean equals(CgRescueResult that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_errorCode = true && this.isSetErrorCode();
    boolean that_present_errorCode = true && that.isSetErrorCode();
    if (this_present_errorCode || that_present_errorCode) {
      if (!(this_present_errorCode && that_present_errorCode))
        return false;
      if (!this.errorCode.equals(that.errorCode))
        return false;
    }

    boolean this_present_current = true && this.isSetCurrent();
    boolean that_present_current = true && that.isSetCurrent();
    if (this_present_current || that_present_current) {
      if (!(this_present_current && that_present_current))
        return false;
      if (this.current != that.current)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetErrorCode()) ? 131071 : 524287);
    if (isSetErrorCode())
      hashCode = hashCode * 8191 + errorCode.getValue();

    hashCode = hashCode * 8191 + ((isSetCurrent()) ? 131071 : 524287);
    if (isSetCurrent())
      hashCode = hashCode * 8191 + current;

    return hashCode;
  }

  @Override
  public int compareTo(CgRescueResult other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetErrorCode(), other.isSetErrorCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetErrorCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.errorCode, other.errorCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCurrent(), other.isSetCurrent());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrent()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.current, other.current);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgRescueResult(");
    boolean first = true;

    sb.append("errorCode:");
    if (this.errorCode == null) {
      sb.append("null");
    } else {
      sb.append(this.errorCode);
    }
    first = false;
    if (isSetCurrent()) {
      if (!first) sb.append(", ");
      sb.append("current:");
      sb.append(this.current);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (errorCode == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'errorCode' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgRescueResultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgRescueResultStandardScheme getScheme() {
      return new CgRescueResultStandardScheme();
    }
  }

  private static class CgRescueResultStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgRescueResult> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgRescueResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ERROR_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.errorCode = com.lc.billion.icefire.protocol.constant.PsRescueErrorCode.findByValue(iprot.readI32());
              struct.setErrorCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // CURRENT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.current = iprot.readI32();
              struct.setCurrentIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgRescueResult struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.errorCode != null) {
        oprot.writeFieldBegin(ERROR_CODE_FIELD_DESC);
        oprot.writeI32(struct.errorCode.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.isSetCurrent()) {
        oprot.writeFieldBegin(CURRENT_FIELD_DESC);
        oprot.writeI32(struct.current);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgRescueResultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgRescueResultTupleScheme getScheme() {
      return new CgRescueResultTupleScheme();
    }
  }

  private static class CgRescueResultTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgRescueResult> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgRescueResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.errorCode.getValue());
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetCurrent()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetCurrent()) {
        oprot.writeI32(struct.current);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgRescueResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.errorCode = com.lc.billion.icefire.protocol.constant.PsRescueErrorCode.findByValue(iprot.readI32());
      struct.setErrorCodeIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.current = iprot.readI32();
        struct.setCurrentIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

