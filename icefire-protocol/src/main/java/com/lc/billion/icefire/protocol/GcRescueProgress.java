/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 返回营救进度
 * @Message(7852)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcRescueProgress implements org.apache.thrift.TBase<GcRescueProgress, GcRescueProgress._Fields>, java.io.Serializable, Cloneable, Comparable<GcRescueProgress> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcRescueProgress");

  private static final org.apache.thrift.protocol.TField CURRENT_FIELD_DESC = new org.apache.thrift.protocol.TField("current", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField TOTAL_FIELD_DESC = new org.apache.thrift.protocol.TField("total", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField REWARDS_FIELD_DESC = new org.apache.thrift.protocol.TField("rewards", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField IS_REWARD_FIELD_DESC = new org.apache.thrift.protocol.TField("isReward", org.apache.thrift.protocol.TType.BOOL, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcRescueProgressStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcRescueProgressTupleSchemeFactory();

  /**
   * 当前进度
   */
  public int current; // required
  /**
   * 总进度
   */
  public int total; // required
  /**
   * 奖励列表
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards; // required
  /**
   * 是否已领取奖励
   */
  public boolean isReward; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 当前进度
     */
    CURRENT((short)1, "current"),
    /**
     * 总进度
     */
    TOTAL((short)2, "total"),
    /**
     * 奖励列表
     */
    REWARDS((short)3, "rewards"),
    /**
     * 是否已领取奖励
     */
    IS_REWARD((short)4, "isReward");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CURRENT
          return CURRENT;
        case 2: // TOTAL
          return TOTAL;
        case 3: // REWARDS
          return REWARDS;
        case 4: // IS_REWARD
          return IS_REWARD;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __CURRENT_ISSET_ID = 0;
  private static final int __TOTAL_ISSET_ID = 1;
  private static final int __ISREWARD_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CURRENT, new org.apache.thrift.meta_data.FieldMetaData("current", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TOTAL, new org.apache.thrift.meta_data.FieldMetaData("total", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.REWARDS, new org.apache.thrift.meta_data.FieldMetaData("rewards", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSimpleItem.class))));
    tmpMap.put(_Fields.IS_REWARD, new org.apache.thrift.meta_data.FieldMetaData("isReward", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcRescueProgress.class, metaDataMap);
  }

  public GcRescueProgress() {
  }

  public GcRescueProgress(
    int current,
    int total,
    java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards,
    boolean isReward)
  {
    this();
    this.current = current;
    setCurrentIsSet(true);
    this.total = total;
    setTotalIsSet(true);
    this.rewards = rewards;
    this.isReward = isReward;
    setIsRewardIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcRescueProgress(GcRescueProgress other) {
    __isset_bitfield = other.__isset_bitfield;
    this.current = other.current;
    this.total = other.total;
    if (other.isSetRewards()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> __this__rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(other.rewards.size());
      for (com.lc.billion.icefire.protocol.structure.PsSimpleItem other_element : other.rewards) {
        __this__rewards.add(new com.lc.billion.icefire.protocol.structure.PsSimpleItem(other_element));
      }
      this.rewards = __this__rewards;
    }
    this.isReward = other.isReward;
  }

  public GcRescueProgress deepCopy() {
    return new GcRescueProgress(this);
  }

  @Override
  public void clear() {
    setCurrentIsSet(false);
    this.current = 0;
    setTotalIsSet(false);
    this.total = 0;
    this.rewards = null;
    setIsRewardIsSet(false);
    this.isReward = false;
  }

  /**
   * 当前进度
   */
  public int getCurrent() {
    return this.current;
  }

  /**
   * 当前进度
   */
  public GcRescueProgress setCurrent(int current) {
    this.current = current;
    setCurrentIsSet(true);
    return this;
  }

  public void unsetCurrent() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CURRENT_ISSET_ID);
  }

  /** Returns true if field current is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrent() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CURRENT_ISSET_ID);
  }

  public void setCurrentIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CURRENT_ISSET_ID, value);
  }

  /**
   * 总进度
   */
  public int getTotal() {
    return this.total;
  }

  /**
   * 总进度
   */
  public GcRescueProgress setTotal(int total) {
    this.total = total;
    setTotalIsSet(true);
    return this;
  }

  public void unsetTotal() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TOTAL_ISSET_ID);
  }

  /** Returns true if field total is set (has been assigned a value) and false otherwise */
  public boolean isSetTotal() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TOTAL_ISSET_ID);
  }

  public void setTotalIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TOTAL_ISSET_ID, value);
  }

  public int getRewardsSize() {
    return (this.rewards == null) ? 0 : this.rewards.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewardsIterator() {
    return (this.rewards == null) ? null : this.rewards.iterator();
  }

  public void addToRewards(com.lc.billion.icefire.protocol.structure.PsSimpleItem elem) {
    if (this.rewards == null) {
      this.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>();
    }
    this.rewards.add(elem);
  }

  /**
   * 奖励列表
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewards() {
    return this.rewards;
  }

  /**
   * 奖励列表
   */
  public GcRescueProgress setRewards(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards) {
    this.rewards = rewards;
    return this;
  }

  public void unsetRewards() {
    this.rewards = null;
  }

  /** Returns true if field rewards is set (has been assigned a value) and false otherwise */
  public boolean isSetRewards() {
    return this.rewards != null;
  }

  public void setRewardsIsSet(boolean value) {
    if (!value) {
      this.rewards = null;
    }
  }

  /**
   * 是否已领取奖励
   */
  public boolean isIsReward() {
    return this.isReward;
  }

  /**
   * 是否已领取奖励
   */
  public GcRescueProgress setIsReward(boolean isReward) {
    this.isReward = isReward;
    setIsRewardIsSet(true);
    return this;
  }

  public void unsetIsReward() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ISREWARD_ISSET_ID);
  }

  /** Returns true if field isReward is set (has been assigned a value) and false otherwise */
  public boolean isSetIsReward() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ISREWARD_ISSET_ID);
  }

  public void setIsRewardIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ISREWARD_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case CURRENT:
      if (value == null) {
        unsetCurrent();
      } else {
        setCurrent((java.lang.Integer)value);
      }
      break;

    case TOTAL:
      if (value == null) {
        unsetTotal();
      } else {
        setTotal((java.lang.Integer)value);
      }
      break;

    case REWARDS:
      if (value == null) {
        unsetRewards();
      } else {
        setRewards((java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>)value);
      }
      break;

    case IS_REWARD:
      if (value == null) {
        unsetIsReward();
      } else {
        setIsReward((java.lang.Boolean)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case CURRENT:
      return getCurrent();

    case TOTAL:
      return getTotal();

    case REWARDS:
      return getRewards();

    case IS_REWARD:
      return isIsReward();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case CURRENT:
      return isSetCurrent();
    case TOTAL:
      return isSetTotal();
    case REWARDS:
      return isSetRewards();
    case IS_REWARD:
      return isSetIsReward();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcRescueProgress)
      return this.equals((GcRescueProgress)that);
    return false;
  }

  public boolean equals(GcRescueProgress that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_current = true;
    boolean that_present_current = true;
    if (this_present_current || that_present_current) {
      if (!(this_present_current && that_present_current))
        return false;
      if (this.current != that.current)
        return false;
    }

    boolean this_present_total = true;
    boolean that_present_total = true;
    if (this_present_total || that_present_total) {
      if (!(this_present_total && that_present_total))
        return false;
      if (this.total != that.total)
        return false;
    }

    boolean this_present_rewards = true && this.isSetRewards();
    boolean that_present_rewards = true && that.isSetRewards();
    if (this_present_rewards || that_present_rewards) {
      if (!(this_present_rewards && that_present_rewards))
        return false;
      if (!this.rewards.equals(that.rewards))
        return false;
    }

    boolean this_present_isReward = true;
    boolean that_present_isReward = true;
    if (this_present_isReward || that_present_isReward) {
      if (!(this_present_isReward && that_present_isReward))
        return false;
      if (this.isReward != that.isReward)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + current;

    hashCode = hashCode * 8191 + total;

    hashCode = hashCode * 8191 + ((isSetRewards()) ? 131071 : 524287);
    if (isSetRewards())
      hashCode = hashCode * 8191 + rewards.hashCode();

    hashCode = hashCode * 8191 + ((isReward) ? 131071 : 524287);

    return hashCode;
  }

  @Override
  public int compareTo(GcRescueProgress other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetCurrent(), other.isSetCurrent());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrent()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.current, other.current);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTotal(), other.isSetTotal());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTotal()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.total, other.total);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRewards(), other.isSetRewards());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRewards()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rewards, other.rewards);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetIsReward(), other.isSetIsReward());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsReward()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isReward, other.isReward);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcRescueProgress(");
    boolean first = true;

    sb.append("current:");
    sb.append(this.current);
    first = false;
    if (!first) sb.append(", ");
    sb.append("total:");
    sb.append(this.total);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rewards:");
    if (this.rewards == null) {
      sb.append("null");
    } else {
      sb.append(this.rewards);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("isReward:");
    sb.append(this.isReward);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'current' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'total' because it's a primitive and you chose the non-beans generator.
    if (rewards == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'rewards' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'isReward' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcRescueProgressStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRescueProgressStandardScheme getScheme() {
      return new GcRescueProgressStandardScheme();
    }
  }

  private static class GcRescueProgressStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcRescueProgress> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcRescueProgress struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CURRENT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.current = iprot.readI32();
              struct.setCurrentIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // TOTAL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.total = iprot.readI32();
              struct.setTotalIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REWARDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                  _elem1.read(iprot);
                  struct.rewards.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setRewardsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // IS_REWARD
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isReward = iprot.readBool();
              struct.setIsRewardIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetCurrent()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'current' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTotal()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'total' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetIsReward()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'isReward' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcRescueProgress struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(CURRENT_FIELD_DESC);
      oprot.writeI32(struct.current);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TOTAL_FIELD_DESC);
      oprot.writeI32(struct.total);
      oprot.writeFieldEnd();
      if (struct.rewards != null) {
        oprot.writeFieldBegin(REWARDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.rewards.size()));
          for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter3 : struct.rewards)
          {
            _iter3.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(IS_REWARD_FIELD_DESC);
      oprot.writeBool(struct.isReward);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcRescueProgressTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRescueProgressTupleScheme getScheme() {
      return new GcRescueProgressTupleScheme();
    }
  }

  private static class GcRescueProgressTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcRescueProgress> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcRescueProgress struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.current);
      oprot.writeI32(struct.total);
      {
        oprot.writeI32(struct.rewards.size());
        for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter4 : struct.rewards)
        {
          _iter4.write(oprot);
        }
      }
      oprot.writeBool(struct.isReward);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcRescueProgress struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.current = iprot.readI32();
      struct.setCurrentIsSet(true);
      struct.total = iprot.readI32();
      struct.setTotalIsSet(true);
      {
        org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list5.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem6;
        for (int _i7 = 0; _i7 < _list5.size; ++_i7)
        {
          _elem6 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
          _elem6.read(iprot);
          struct.rewards.add(_elem6);
        }
      }
      struct.setRewardsIsSet(true);
      struct.isReward = iprot.readBool();
      struct.setIsRewardIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

