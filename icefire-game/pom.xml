<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.longtech.icefire</groupId>
		<artifactId>icefire-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../icefire-parent</relativePath>
	</parent>

	<artifactId>icefire-game</artifactId>

	<name>icefire Game Server</name>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>libraries-bom</artifactId>
				<version>26.19.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-storage</artifactId>
		</dependency>
		<dependency>
			<groupId>io.github.schneiderlin</groupId>
			<artifactId>nrepl-starter</artifactId>
			<version>1.0.8</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.simfun.sgf</groupId>
			<artifactId>sgf-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.firebase</groupId>
			<artifactId>firebase-admin</artifactId>
			<version>7.0.1</version>
			<exclusions>
				<exclusion>
					<groupId>io.netty</groupId>
					<artifactId>netty-codec-http</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.netty</groupId>
					<artifactId>netty-handler</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.netty</groupId>
					<artifactId>netty-transport</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.simfun.sgf</groupId>
			<artifactId>sgf-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.simfun.sgf</groupId>
			<artifactId>sgf-net</artifactId>
		</dependency>
		<dependency>
			<groupId>com.longtech.icefire</groupId>
			<artifactId>icefire-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.longtech.icefire</groupId>
			<artifactId>icefire-protocol</artifactId>
		</dependency>
		<dependency>
			<groupId>com.simfun.sgf</groupId>
			<artifactId>sgf-monitoring</artifactId>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jongo</groupId>
			<artifactId>jongo</artifactId>
			<version>1.5.0</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.thrift</groupId>
			<artifactId>libthrift</artifactId>
		</dependency>
		<dependency>
			<groupId>org.simpleframework</groupId>
			<artifactId>simple-xml</artifactId>
		</dependency>
		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-dbcp2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.perf4j</groupId>
			<artifactId>perf4j</artifactId>
		</dependency>

		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-sns</artifactId>
		</dependency>
		<!-- <dependency> <groupId>net.jpountz.lz4</groupId> <artifactId>lz4</artifactId>
			<scope>compile</scope> <optional>true</optional> </dependency> -->
		<dependency>
			<groupId>org.lz4</groupId>
			<artifactId>lz4-pure-java</artifactId>
		</dependency>
		<!-- <dependency> <groupId>hot_load_agent</groupId> <artifactId>hot_load_agent</artifactId> 
			<version>0.0.1-SNAPSHOT</version> </dependency> -->
		<dependency>
			<groupId>com.googlecode.concurrentlinkedhashmap</groupId>
			<artifactId>concurrentlinkedhashmap-lru</artifactId>
		</dependency>
		<!-- <dependency> <groupId>com.ecwid.consul</groupId> <artifactId>consul-api</artifactId>
			</dependency> -->	
			<!-- https://mvnrepository.com/artifact/com.jcraft/jsch -->
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>

		<!-- <dependency> <groupId>com.simfun.sgf</groupId> <artifactId>sgf-descriptors</artifactId> 
			<version>${sgf.version}</version> </dependency> -->
		<!-- test -->
		<!-- <dependency> <groupId>com.orbitz.consul</groupId> <artifactId>consul-client</artifactId> 
			<version>0.16.3</version> </dependency> <dependency> <groupId>junit</groupId> 
			<artifactId>junit</artifactId> <scope>test</scope> </dependency> <dependency> 
			<groupId>org.easymock</groupId> <artifactId>easymock</artifactId> <scope>test</scope> 
			</dependency> -->

		<!-- https://mvnrepository.com/artifact/org.flywaydb/flyway-core <dependency> 
			<groupId>org.flywaydb</groupId> <artifactId>flyway-core</artifactId> <version>5.2.4</version> 
			</dependency> -->

		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.4</version>
		</dependency>
		<dependency>
			<groupId>com.longtech.icefire</groupId>
			<artifactId>ls-flyway-mongo</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.esotericsoftware</groupId>
			<artifactId>kryo</artifactId>
			<version>5.6.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.10.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/io.prometheus.jmx/jmx_prometheus_javaagent -->
		<dependency>
			<groupId>io.prometheus.jmx</groupId>
			<artifactId>jmx_prometheus_javaagent</artifactId>
			<version>0.18.0</version>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.xml.ws</groupId>
			<artifactId>jakarta.xml.ws-api</artifactId>
			<version>4.0.1</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/jakarta.xml.soap/jakarta.xml.soap-api -->
		<dependency>
			<groupId>jakarta.xml.soap</groupId>
			<artifactId>jakarta.xml.soap-api</artifactId>
			<version>3.0.2</version>
		</dependency>
		<dependency>
			<groupId>com.happysky.module.mail</groupId>
			<artifactId>mail-connector</artifactId>
			<version>1.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-client-java</artifactId>
			<version>5.0.4</version>
		</dependency>
		<dependency>
			<groupId>org.locationtech.jts</groupId>
			<artifactId>jts-core</artifactId>
			<version>1.19.0</version>
		</dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>5.2.4</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>5.2.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.2.2</version> <!-- 请确保版本是最新的 -->
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.11.0</version>
		</dependency>
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>4.0.21</version>
            <scope>compile</scope>
        </dependency>

		<!-- JMH 基准测试框架 -->
		<dependency>
			<groupId>org.openjdk.jmh</groupId>
			<artifactId>jmh-core</artifactId>
			<version>1.37</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.openjdk.jmh</groupId>
			<artifactId>jmh-generator-annprocess</artifactId>
			<version>1.37</version>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.xml</include>
				</includes>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.xml</include>
				</includes>
				<filtering>false</filtering>
			</resource>
		</resources>
		<!-- <plugins> <plugin> <groupId>org.apache.maven.plugins</groupId> <artifactId>maven-assembly-plugin</artifactId> 
			<configuration> <descriptorId>lib-dist</descriptorId> <finalName>${project.artifactId}</finalName> 
			</configuration> <dependencies> <dependency> <groupId>com.simfun.sgf</groupId> 
			<artifactId>sgf-descriptors</artifactId> <version>${sgf.version}</version> 
			</dependency> </dependencies> </plugin> </plugins> -->
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix>
							<mainClass>com.lc.billion.icefire.game.GameMain</mainClass>
							<useUniqueVersions>false</useUniqueVersions>
						</manifest>
						<manifestEntries>
							<Class-Path>./conf/</Class-Path>
							<Add-Opens>java.base/jdk.internal.misc</Add-Opens>
						</manifestEntries>
					</archive>
					<excludes>
						<exclude>spring-conf.properties</exclude>
						<exclude>logback.xml</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>3.3.0</version>

				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/lib</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>1.6.0</version>
				<executions>
					<execution>
						<phase>test</phase>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>
								<argument>-classpath</argument>
								<classpath />
								<argument>com.lc.billion.icefire.game.tool.config.CompileCheck</argument> <!-- 程序入口 -->
								<argument>${basedir}/../meta</argument>
								<argument>${basedir}/../data/serverInfo.csv</argument>
								<argument>${basedir}/map</argument>
							</arguments>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<repositories>
		<repository>
			<id>happysky-snap</id>
			<name>happysky</name>
			<url>https://tynexus.tuyoo.com/repository/happysky-snap</url>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>
		<repository>
			<id>happysky-release</id>
			<name>happysky</name>
			<url>https://tynexus.tuyoo.com/repository/happysky-release</url>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>
	</repositories>
</project>