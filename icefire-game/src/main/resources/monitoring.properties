# SGF监控模块配置
# 应用名称
monitoring.application.name=icefire-game
# JVM监控
monitoring.jvm.enabled=true
# WebSocket监控
monitoring.websocket.enabled=true
# 业务方法监控
monitoring.method.enabled=true
# Prometheus导出器配置
monitoring.prometheus.enabled=true
monitoring.prometheus.port=9090
monitoring.prometheus.path=/metrics
# 性能优化配置
monitoring.async.enabled=true
monitoring.async.queue.size=10000
monitoring.async.thread.pool.size=2
# 采样配置
monitoring.sampling.enabled=true
monitoring.sampling.strategy=adaptive
# 固定比例采样配置（当strategy=fixed时使用）
monitoring.sampling.fixed.rate=0.1
# 自适应采样配置（当strategy=adaptive时使用）
monitoring.sampling.adaptive.base.rate=0.1
monitoring.sampling.adaptive.window.size.ms=60000
monitoring.sampling.adaptive.high.frequency.threshold=100
monitoring.sampling.adaptive.min.rate=0.01
monitoring.sampling.adaptive.max.rate=0.5
# 日志配置
monitoring.logging.level=INFO
monitoring.logging.enabled=true