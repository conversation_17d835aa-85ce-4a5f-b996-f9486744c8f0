package com.lc.billion.icefire.game.msg.handler.impl.rescue;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.rescue.RescueService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgRescueProgress;
import com.lc.billion.icefire.protocol.GcRescueProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class CgRescueProgressHandler extends CgAbstractMessageHandler<CgRescueProgress> {

    @Autowired
    private RescueService rescueService;

    @Override
    protected void handle(Role role, CgRescueProgress message) {
        GcRescueProgress response = rescueService.getRescueProgress(role);
        role.send(response);
    }
}
