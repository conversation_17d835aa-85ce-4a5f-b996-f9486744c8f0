package com.lc.billion.icefire.game.support;

import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;
import org.springframework.util.Assert;

import java.lang.annotation.*;

/**
 * 日志系统的日志原因定义
 */
public interface LogReasons {

    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.FIELD, ElementType.TYPE})
    public @interface ReasonDesc {
        /**
         * 原因的文字描述
         *
         * @return
         */
        String value();
    }

    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.FIELD, ElementType.TYPE})
    public @interface LogDesc {
        /**
         * 日志描述
         *
         * @return
         */
        String desc();
    }

    /**
     * LogReason的通用接口
     */
    public static interface ILogReason {
        /**
         * 取得原因的序号
         *
         * @return
         */
        public int getReason();

        /**
         * 获取原因的文本
         *
         * @return
         */
        public String getReasonText(Object... params);

        public static class ReasonTextBuilder {

            /**
             * 构建原因文本的工具
             *
             * @param keys
             * @param args
             * @return
             */
            public static String builtStr(ParamKey[] keys, Object... args) {
                // 无参 返回空即可,忽略argus
                if (keys == null || keys.length == 0) {
                    return "";
                }

                Assert.isTrue(keys.length == args.length, "参数数量不匹配,检查argus数量")//
                ;

                // 处理成对参数
                StringBuffer result = new StringBuffer("")//
                        ;
                int len = keys.length;
                for (int i = 0; i < len; i++) {
                    String key = keys[i].getType();
                    String val = args[i].toString();
                    if (i == 0) {
                        result = result.append(key + "=" + val);
                    } else {
                        result = result.append(":" + key + "=" + val);
                    }
                }

                return result.toString();

            }

        }

    }

    public enum ParamKey {

        USER_ID("user_id"),

        ROLE_ID("role_id"),

        LAST_LOGIN_IP("last_login_ip"),

        TOTAL_MINUTE("total_minute"),

        RARITY("rarity"),

        STAR("star"),

        EXP("exp"),

        LEVEL("level"),

        ESSENCE("essence"),

        ERRNO("errno"),

        ERRMSG("errmsg"),

        BEFORE("before"),

        AFTER("after"),

        CHARGE_SN("charge_sn"),

        FIRST_PAY_AMOUNT("first_pay_amount"),

        ORDER_ID("order_id"),

        PRODUCT_ID("product_id"),

        PRODUCT_COUNT("product_count"),

        GIFT_SN("gift_sn"),

        CHARGE_AMOUNT("charge_amount"),

        CHARGE_FAIL_MSG("charge_fail_msg"),

        BAG_ID("bag_id"),

        BAD_INDEX("bag_idx"),

        BUILDING_ID("building_id"),

        BUILDING_NAME("building_name"),

        BUILDING_LEVEL("building_level"),

        BUFF_ID("buff_id"),

        TECH_ID("tech_id"),

        TECH_LEVEL("tech_level"),

        CMD_CONTENT("cmd_content"),

        ITEM_INDEX("item_index"),

        ITEM_META_ID("item_meta_id"),

        ITEM_UUID("item_uuid"),

        ITEM_DELTA("item_delta"),

        ITEM_RESULT_COUNT("item_result_c"),

        ITEM_GENID("item_genid"),

        ITEM_NAME("item_name"),

        ITEM_COUNT("item_count"),

        ITEM_BIND("item_bind"),

        ITEM_DEADLINE("item_deadline"),

        ITEM_PROP("item_prop"),

        ITEM_OVERLAP("item_overlap"),

        MONEY_TYPE("money_type"),

        CURRENCY_ID("currency_id"),

        META_ID("meta_id"),
        ;

        /**
         * 参数类型字符串
         */
        public final String type;

        private ParamKey(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }

    }

    @LogDesc(desc = "充值日志")
    public enum ChargeLogReason implements ILogReason {
        @ReasonDesc("充值钻石成功") CHARGE_DIAMOND_SUCCESS(1),
        @ReasonDesc("充值失败") CHARGE_FAILURE(2, ParamKey.CHARGE_FAIL_MSG),
        @ReasonDesc("充值月卡") CHARGE_MONTH_CARD(3),
        @ReasonDesc("充值礼包") CHARGE_PAYMENT_GIFT(5),
        @ReasonDesc("充值补单") CHARGE_PAYMENT_FIX(6),
        @ReasonDesc("零元充值") CHARGE_PAYMENT_ZERO(7),
        ;

        /**
         * 原因序号
         */
        public final int reason;
        /**
         * 原因参数
         */
        public final ParamKey[] keys;

        private ChargeLogReason(int reason, ParamKey... paramKeys) {
            this.reason = reason;
            this.keys = paramKeys;
        }

        @Override
        public int getReason() {
            return this.reason;
        }

        @Override
        public String getReasonText(Object... args) {
            return ReasonTextBuilder.builtStr(keys, args);
        }

    }

    @LogDesc(desc = "金钱改变日志") //
    public enum MoneyLogReason implements ILogReason, IntEnum {
        //
        @ReasonDesc("GM命令给钱") GM_CMD_GIVE_MONEY(1), //
        @ReasonDesc("GM命令扣钱") GM_CMD_COST_MONEY(2), //
        @ReasonDesc("城市产出") CITY_OUTPUT(3), //
        @ReasonDesc("出征") ARMY_SET_OUT(4), //
        @ReasonDesc("邮件奖励") EMAIL_REWARD(8), //
        @ReasonDesc("物品使用") USE_ITEM(9), //
        @ReasonDesc("侦查消耗") RECON(10), //
        @ReasonDesc("GM虚拟充值") GM_RECHARGE(11),
        @ReasonDesc("购买并使用物品") BUY_USE_ITEM(12, ParamKey.ITEM_META_ID, ParamKey.ITEM_COUNT), //
        @ReasonDesc("联盟商店购买") ALLIANCE_BUY(13), //
        @ReasonDesc("募集军队") ENLIST(14), //
        @ReasonDesc("建造建筑") BUILD_BUILDING(15), //
        @ReasonDesc("立即建造建筑") BUILD_BUILDING_IMMEDIATE(16), //
        @ReasonDesc("升级建筑") UPGRADE_BUILDING(17), //
        @ReasonDesc("立即升级建筑") UPGRADE_BUILDING_IMMEDIATE(18),
        @ReasonDesc("盟主取代") ALLIANCE_REPLACE(22), //
        @ReasonDesc("联盟科技捐献") ALLIANCE_TECH_DONATE(23), //
        @ReasonDesc("士兵训练") SOLDIER_TRAIN(24), //
        @ReasonDesc("立即士兵训练") SOLDIER_TRAIN_IMMEDIATE(25), //
        @ReasonDesc("工作队列加速完成") WORK_FAST_FINISH(26), //
        @ReasonDesc("联盟招募") ALLIANCE_RECRUIT(27), //
        @ReasonDesc("迁城") CITY_MOVE(28), //
        @ReasonDesc("玩家改名") CHANGE_NAME(31), //
        @ReasonDesc("商店购买") SHOP_BUY(33, ParamKey.ITEM_META_ID, ParamKey.ITEM_COUNT), //
        @ReasonDesc("行军加速") ARMY_MOVE_SPEED_UP(34), //
        @ReasonDesc("工作队列加速") WORK_SPEED_UP(35), //
        @ReasonDesc("充值") RECHARGE(36),
        @ReasonDesc("取消队列") WORK_CANCEL(37),
        @ReasonDesc("道具给钱") SIMPLE_ITEM(38),
        @ReasonDesc("活动兑换") EXCHANGE_ACTIVITY(39),
        @ReasonDesc("士兵治疗") SOLDIER_CURE(40), //
        @ReasonDesc("联盟改名") ALLIANCE_CHANGE_NAME(46), //
        @ReasonDesc("联盟改简称") ALLIANCE_CHANGE_SHORT_NAME(47), //
        @ReasonDesc("联盟改徽章") ALLIANCE_CHANGE_BADGE(48), //
        @ReasonDesc("联盟创建") ALLIANCE_CREATE(49), //
        @ReasonDesc("军队召回") RECALL_ARMY(50), //
        @ReasonDesc("开采") EXPLOIT(74),
        @ReasonDesc("修改联盟旗帜") ALLIANCE_CHANGE_FLAG(76),
        @ReasonDesc("占领奖励") GET_STATE_REWARD(82),
        @ReasonDesc("英雄装备升级") HERO_EQUIP_LEVEL_UP(92), //
        @ReasonDesc("英雄升级") HERO_LEVEL_UP(93), //
        @ReasonDesc("灭火") FIRE_FIGHTING(94), //
        @ReasonDesc("装备分解获得") EQUIP_BREAK_DOWN_ADD(95),
        @ReasonDesc("购买治疗队列") BUY_CURE_QUEUE(96),
        @ReasonDesc("刷新每日任务") REFRESH_DAILY_MISSION(97),
        @ReasonDesc("士兵升级") SOLDIER_UPDATE(98), //
        @ReasonDesc("国王公告") KINGDOM_AFFICHE(100),
        @ReasonDesc("联盟捐献") ALLIANCE_DONATE(101),
        @ReasonDesc("联盟工资获得") ALLIANCE_SALARY_GET(102),
        @ReasonDesc("联盟迁城") ALLIANCE_MOVE_CITY(103),
        @ReasonDesc("解锁区域") UNLOCK_LAND(104),
        @ReasonDesc("清理障碍") CLEAR_COVER(105),
        @ReasonDesc("建造酒馆获得") BUILD_TAVERN(106),
        @ReasonDesc("装饰类建筑拆除") DISMANTLE_DECORATE_BUILD(107),
        @ReasonDesc("建造木材仓库获得") BUILD_TIMBER_WAREHOUSE(108),
        @ReasonDesc("新手建造完成道路获得") BUILD_ROAD(109),
        @ReasonDesc("在线奖励") DAILY_ONLINE_REWARD_ADD(110),

        @ReasonDesc("地图中断事件交换") MAP_SPECIAL_EVENT_EXCHANGE(603),

        @ReasonDesc("掠夺") ROB_BATTLE(604),
        @ReasonDesc("快速完成日常任务消耗") DAILY_MISSION_SKIP(605),
        @ReasonDesc("地图中断集结事件交换") MAP_SPECIAL_EVENT_GATHER_EXCHANGE(607),

        @ReasonDesc("士兵训练取消") SOLDIER_TRAIN_STOP(608), //
        @ReasonDesc("驿站购买") SHOP_STATION_BUY(609), //
        @ReasonDesc("天赋重置") HERO_TALENT_RESET(610),
        @ReasonDesc("抽奖") LOTTERY_USE(611),
        @ReasonDesc("驿站刷新") REFRESH_STATION(612),
        @ReasonDesc("升级科技") UPGRADE_SIENCE(613), //
        @ReasonDesc("立即升级科技") UPGRADE_SIENCE_IMMEDIATE(614),
        @ReasonDesc("购买第二队列") BUY_SECOND_QUEUE(615),
        @ReasonDesc("购买第二科研队列") BUY_SECOND_SCIENCE_QUEUE(616),
        @ReasonDesc("黑市刷新消耗") BSHOP_BUY(618),
        @ReasonDesc("联盟邀请") ALLIANCE_INVITE(621),
        @ReasonDesc("资源产出") RESOURCEOUTPUT(622, false),
        @ReasonDesc("技能激活") HERO_SKILL_ACTIVITY(623),
        @ReasonDesc("英雄手动分解获得") HERO_DECOMPOSE_MANUAL(669),
        @ReasonDesc("运输资源到生产建筑") TRANSPORT_RESOURCE(700),
        @ReasonDesc("购买城防耐久度") ROLECITY_DURABLE(701),
        @ReasonDesc("英雄技能：随机获得资源") HERO_RANDOM_GAIN_RESOURCE(702),
        @ReasonDesc("钻石秒资源") IMMEDIATE_RESOURCE(703),
        @ReasonDesc("建筑修复") BUILDING_REPAIR(704),
        @ReasonDesc("钻石购买礼包") BUY_LIBAO(705),
        @ReasonDesc("联盟帮助") ALLIANCE_HELP(706),
        @ReasonDesc("购买BattlePass等级") BUY_BATTLE_PASS(707),
        @ReasonDesc("联盟上货") ALLIANCE_STORE_ADD_ITEM_STOCK(714),
        @ReasonDesc("购买英雄") CITY_BUY_HERO(715),
        @ReasonDesc("持续采集因服务器而补偿") REAP_ON_SERVER_START(716),
        @ReasonDesc("英雄购买钻石秒时间") CITY_HERO_BUY_IMMEDIATE(718),
        @ReasonDesc("持续采集") REAP_RES(719, false),
        @ReasonDesc("英雄升级通用经验") HERO_LEVEL_UP_EXP(721),
        @ReasonDesc("出售道具") SALE_ITEM(722),
        @ReasonDesc("英雄驻扎或者战斗获得经验") HERO_STATION_ADD_EXP(723),
        @ReasonDesc("攻占资源自动生产") NEW_RES_NODE_AUTO_OUTPUT(724),
        @ReasonDesc("英雄碎片兑换") SURVIVOR_TOKEN_EXCHANGE(725),
        @ReasonDesc("幸存者商店购买") SURVIVOR_SHOP_BUY(726),
        @ReasonDesc("装备锻造") EQUIP_COMPOSE(728),
        @ReasonDesc("账号创建初始化") ROLE_CREATE(729),
        @ReasonDesc("材料分解") MATERIAL_DECOMPOSE(731),
        @ReasonDesc("盟友资源援助") RES_HELP(733),
        @ReasonDesc("GVG战场购买体力") GVG_BUY_STAMINA(738),
        @ReasonDesc("GVG战场购买迁城次数") GVG_BUY_MOVE_CITY(739),
        @ReasonDesc("内城救英雄") INNER_CITY_SAVE_HERO(740),

        @ReasonDesc("低语音活动非首次召唤怪物") WHISPERERS_CALL_NPC(747),
        @ReasonDesc("钻石刷新射击训练") SHOOTING_TRAINING(752),
        @ReasonDesc("钻石零元购") FREE_BUY(753),
        @ReasonDesc("宠物商店购买") PET_SHOP_BUY(755),
        @ReasonDesc("宠物洗炼") PET_SWAP(758),
        @ReasonDesc("低语者攻打boss") WHISPERER_ATTACK_BOSS(763),
        @ReasonDesc("修改军团基础信息") LEGION_CHANGE_CONSUME(764),
        @ReasonDesc("创建军团") LEGION_CREATE(765),
        @ReasonDesc("体力恢复") STAMINA_RECOVER(767),
        @ReasonDesc("低语者攻打boss退还") WHISPERER_ATTACK_BOSS_RETURN(771),
        @ReasonDesc("家具升级") UPGRADE_FURNITURE(773), //
        @ReasonDesc("家具升级失败返还") UPGRADE_FURNITURE_FAIL(774), //
        // 775-776 竞技场在用
        @ReasonDesc("竞技场刷新挑战列表消耗") ARENA_REFRESH_CHALLENGER_LIST(775),
        @ReasonDesc("竞技场购买挑战次数") ARENA_BUY_TICKET(776),
        @ReasonDesc("探险关卡挂机奖励") PVE_EXPEDITION_HANGUP_REWARD(777),
        @ReasonDesc("集結取消") ARMY_RALLY_CANCEL(778),
        @ReasonDesc("烽火台攻击NPC失败") EXPLORE_EVENT_ATTACK_NPC_FAILED(779),
        @ReasonDesc("烽火台救援失败") EXPLORE_EVENT_RESCUE_FAILED(780),
        @ReasonDesc("完成剧情增加民心") POPULAR_WILL_STORY_FINISHED(781),
        @ReasonDesc("特殊剧情增加民心") POPULAR_WILL_SPECIAL_STORY_FINISHED(782),
        @ReasonDesc("章节完成增加民心") POPULAR_WILL_CHAPTER_FINISHED(783),
        @ReasonDesc("建筑升级完成增加民心") POPULAR_WILL_BUILDING_UPGRADE_FINISHED(784),
        @ReasonDesc("人数变化增加民心") POPULAR_WILL_PEOPLE_CHANGE(785),
        @ReasonDesc("npc事件完成增加民心") POPULAR_WILL_NPC_EVENT_FINISHED(786),
        @ReasonDesc("时间变化增加民心") POPULAR_WILL_CHANGE_BY_TIME(787),
        @ReasonDesc("代币购买礼包") TOKEN_BUY_LIBAO(788),
        @ReasonDesc("首充双倍") FIRST_CHARGE_DOUBLE(789),

        @ReasonDesc("坐骑升级消耗") HORSE_LEVEL_UP(790),

        @ReasonDesc("采集产生军饷") WAR_CHEST_GATHER(800),
        @ReasonDesc("烽火台产生军饷") WAR_CHEST_WORLD_EXPLORE(801),

        @ReasonDesc("商队品质刷新") CARAVAN_TRADE_REFRESH(910),
        @ReasonDesc("商队抢夺刷新") CARAVAN_PLUNDER_REFRESH(911),
        @ReasonDesc("商队抢夺获得") CARAVAN_TRADE_PLUNDER(912),
        @ReasonDesc("商队完成获得") CARAVAN_TRADE_REWARD(913),
        @ReasonDesc("三英战吕布建造捐献") ALLIANCE_BOSS_DONATE_BUILD(914),
        @ReasonDesc("三英战吕布buff捐献") ALLIANCE_BOSS_DONATE_BUFF(915),

        @ReasonDesc("修改邀请码") MODIFY_INVITE_CODE(920),

        @ReasonDesc("转盘活动抽奖") ICE_WHEEL_PULL_COST(930), //

        @ReasonDesc("民心消耗") POPULAR_WILL_SKILL_COST(931),
        @ReasonDesc("民心效果") POPULAR_WILL_SKILL_EFFECT(932),
        @ReasonDesc("八方来援-损失兵力资源补偿") BATTLE_LOSE_RESOURCE_COMPENSATION(933),
        @ReasonDesc("贸易站兑换活动") TRADE_POST_ACTIVITY(934),
        @ReasonDesc("KVK迁服") KVK_MIGRATE(935),
        @ReasonDesc("花费元宝设置同盟集结地") ALLIANCE_GATHERING_PLACE(936),
        @ReasonDesc("同盟迁城消耗") ALLIANCE_GATHERING_PLACE_MOVE(937),
        @ReasonDesc("同盟建筑") ALLIANCE_BUILDING(938),
        @ReasonDesc("坐骑改名") HORSE_RENAME(939),
        ;

        /**
         * 原因序号
         */
        public final int reason;
        /**
         * 原因参数
         */
        public final ParamKey[] keys;

        /**
         * 是否立即记入BI，true为立即，false 为记入缓存，定时处理
         * 默认为true
         */
        public final boolean immediatelyBI;

        private static final MoneyLogReason[] INDEXES = EnumUtils.toArray(MoneyLogReason.values());

        public static MoneyLogReason findById(int id) {
            if (id < 0 || id >= INDEXES.length) {
                return null;
            }
            return INDEXES[id];
        }

        private MoneyLogReason(int reason, boolean immediatelyBI, ParamKey... paramKeys) {
            this.reason = reason;
            this.immediatelyBI = immediatelyBI;
            this.keys = paramKeys;
        }

        private MoneyLogReason(int reason, ParamKey... paramKeys) {
            this.reason = reason;
            this.immediatelyBI = true;
            this.keys = paramKeys;
        }

        @Override
        public int getReason() {
            return reason;
        }

        @Override
        public String getReasonText(Object... args) {
            return ReasonTextBuilder.builtStr(keys, args);
        }

        public static String simpleJoin(Object o1, Object o2) {
            return o1 + ":" + o2;
        }

        @Override
        public int getId() {
            return reason;
        }
    }

    @LogDesc(desc = "物品更新日志") //
    public enum ItemLogReason implements ILogReason, IntEnum {

        @ReasonDesc("debug命令全部清空物品") DEBUG_REMOVE_ALL_ITEM(501),
        @ReasonDesc("debug测试添加道具") DEBUG_ADD_ITEM(502),
        @ReasonDesc("使用道具") USE_ITEM(503, ParamKey.ITEM_META_ID, ParamKey.ITEM_COUNT), //

        @ReasonDesc("邮件奖励") EMAIL_REWARD(506),
        @ReasonDesc("月卡奖励") MONTHCARD_REWARD(507),
        @ReasonDesc("初始新角色") INIT_NEW_ROLE(510),
        @ReasonDesc("礼包购买") LIBAO_BUY(512),
        @ReasonDesc("任务奖励") MISSION_REWARD(513),
        @ReasonDesc("商店购买") SHOP_BUY(515),
        @ReasonDesc("玩家改名") CHANGE_NAME(518),
        @ReasonDesc("七日签到奖励") LOGIN_REWARD(519),
        @ReasonDesc("城市迁移") MOVE_CITY(522),
        @ReasonDesc("行军加速") ARMY_MOVE_SPEED_UP(523),
        @ReasonDesc("工作队列加速") WORK_SPEED_UP(524),
        @ReasonDesc("工作队列加速返还") WORK_SPEED_UP_RETURN(525),
        @ReasonDesc("宝箱") BOX_OPEN(527),
        @ReasonDesc("召回军队") RECALL_ARMY(534),
        @ReasonDesc("物品超时失效") ITEM_EXPIRE(566),
        @ReasonDesc("使用打折卷") USE_DISCOUNT_ITEM(568),
        @ReasonDesc("连续充值奖励") CONTINUITY_RECHARGE_REWARD(569),
        @ReasonDesc("GM发送") GM_EMAIL_GET(573),
        @ReasonDesc("GM充值奖励") GM_CMD_GIVE_ITEM(574),
        @ReasonDesc("GM发放道具") GM_CMD_COST_ITEM(575),

        @ReasonDesc("档位首充奖励") PRICE_FIRST_PAY_REWARD(597),
        @ReasonDesc("GVG战场建筑NPC掉落") STRONG_HOLD_NPC_DROP(590),
        @ReasonDesc("GVG战场NPC掉落") GVG_NPC_DROP(591),
        @ReasonDesc("GVG战场结算") GVG_END(593),
        @ReasonDesc("地图新资源点PVE掉落") MAP_NEW_RES_PVE_DROP(599),
        @ReasonDesc("地图事件掉落") MAP_EVENT_DROP(600),
        @ReasonDesc("地图NPC掉落") MAP_NPC_DROP(601),
        @ReasonDesc("地图中断事件掉落") MAP_SPECIAL_EVENT_DROP(602),
        @ReasonDesc("地图中断事件交换") MAP_SPECIAL_EVENT_EXCHANGE(603),
        @ReasonDesc("英雄脱下装备") HERO_PUT_OFF(610),
        @ReasonDesc("死亡士兵增加英雄经验道具") HERO_EXP_SOLDIER_DEAD(611),
        @ReasonDesc("购买并使用") BUY_AND_USE(612),
        @ReasonDesc("充值礼包邮件") RECHARGE_GIFT_EMAIL_GET(613),
        @ReasonDesc("灭火") FIRE_FIGHTING(614),
        @ReasonDesc("装备升星消耗") EQUIP_LEVEL_STAR(615),
        @ReasonDesc("道具合成消耗") ITEM_SYNTHESIS_COST(616),
        @ReasonDesc("道具合成获得") ITEM_SYNTHESIS_ADD(617),
        @ReasonDesc("装备分解获得") EQUIP_BREAK_DOWN_ADD(618),
        @ReasonDesc("装备锻造获得") EQUIP_FORGING_ADD(619),
        @ReasonDesc("每日任务奖励") DAILY_MISSION_ADD(620),
        @ReasonDesc("月石商城") MOON_STONE_SHOP(621),
        @ReasonDesc("在线奖励") DAILY_ONLINE_REWARD_ADD(622),
        @ReasonDesc("道具购买英雄") HERO_BUY_BY_ITEM(623),
        @ReasonDesc("新手7天任务全完成奖励") NEW_PLAYER_MISSION_END_ALL(624),
        @ReasonDesc("碎片分解") ITEM_BREAK_DOWN(626),
        @ReasonDesc("碎片分解获得") ITEM_BREAK_DOWN_ADD(627),
        @ReasonDesc("英雄兑换扣除道具") HERO_EXCHANGE(628),
        @ReasonDesc("累计登陆奖励") LOGIN_DAY_REWARD(630),
        @ReasonDesc("英雄派遣奖励") HERO_TRAIN_REWARD(631),
        @ReasonDesc("礼包购买完美装备品质") LIBAO_BUY_PERFECT(632),
        @ReasonDesc("世界boss攻击奖励") WORLD_BOSS_ATTACK_GET(634),
        @ReasonDesc("集结世界boss获得") MAP_SPECIAL_EVENT_GATHER_EXCHANGE(635),
        @ReasonDesc("通过物品合成英雄") HERO_GET_FROM_ITEM(636),
        @ReasonDesc("英雄升星") HERO_STAR_UP(637),
        @ReasonDesc("驿站购买") SHOP_STATION_BUY(638),
        @ReasonDesc("英雄碎片兑换") SIMPLE_HERO_SPLIT_CHANGE(639),
        @ReasonDesc("英雄天赋重置") HERO_TALENT_RESET(640),
        @ReasonDesc("抽奖") LOTTERY_USE_ITEM(641),
        @ReasonDesc("清理障碍") CLEAR_COVER(642),
        @ReasonDesc("远征掉落") BATTLE_FIELD_DROP(643),
        @ReasonDesc("首充奖励") FIRST_RECHARGE(646),
        @ReasonDesc("vip等级奖励") VIP_LEVEL(647),
        @ReasonDesc("每日特惠礼包奖励") DAILY_OFFER(648),
        @ReasonDesc("兑换活动兑换") EXCHANGE_ACTIVITY(650),
        @ReasonDesc("升级建筑") BUILDING_LEVELUP(651),
        @ReasonDesc("建筑升级奖励") BUILDING_LEVEL_UP(653),
        @ReasonDesc("首次加入联盟奖励") FIRST_JOIN_ALLIANCE(654),
        @ReasonDesc("付费月签") RECHARGE_SIGN(655),
        @ReasonDesc("黑市购买") BSHOP_BUY(658),
        @ReasonDesc("联盟商店购买") ALLIANCE_STORE_BUY(659),
        @ReasonDesc("新连续充值奖励") CONTINUOUS_RECHARGE_REWARD_NEW(660),
        @ReasonDesc("最强领主") STRONGEST_LORDS_ACTIVITY(661),
        @ReasonDesc("观看视频广告奖励") VIDEO_AD_WATCHING(662),
        @ReasonDesc("联盟礼物") ALLIANCE_GIFT_REWARD(663),
        @ReasonDesc("cp抽取英雄") LOTTERY_CP(664),
        @ReasonDesc("英雄自动分解获得") HERO_DECOMPOSE_AUTO(666),
        @ReasonDesc("技能激活") HERO_SKILL_ACTIVITY(667),
        @ReasonDesc("技能升级") HERO_SKILL_LEVEL_UP(668),
        @ReasonDesc("英雄手动分解获得") HERO_DECOMPOSE_MANUAL(669),
        @ReasonDesc("科技升级使用") TECH_UPGRADE_USE(670),
        @ReasonDesc("新手战损保护") NEW_PLAYER_PROTECT(671),
        @ReasonDesc("领取battlePass奖励") REWARD_BATTLE_PASS(677),
        @ReasonDesc("首冲送英雄奖励") FIRST_RECHARGE_HERO(679),
        @ReasonDesc("累计充值奖励") CONTINUOUS_RECHARGE(683),
        @ReasonDesc("首充送建筑队列") FIRST_RECHARGE_BUILDING_QUEUE(687),
        @ReasonDesc("新手七日累充") SEVEN_DAY_RECHARGE(688),
        @ReasonDesc("英雄升级") HERO_LEVEL_UP(689),
        @ReasonDesc("幸存者货币兑换消耗") SURVIVOR_TOKEN_EXCHANGE_COST(690),
        @ReasonDesc("幸存者货币兑换获得") SURVIVOR_TOKEN_EXCHANGE_ADD(691),
        @ReasonDesc("里程碑奖励") MILESTONE_REWARD(692),
        @ReasonDesc("永久迁服") MIGRATE(697),
        @ReasonDesc("新手7天任务宝箱奖励") NEW_PLAYER_MISSION_BOX(699),
        @ReasonDesc("装备锻造") EQUIP_COMPOSE(700),
        @ReasonDesc("材料合成") MATERIAL_COMPOSE(701),
        @ReasonDesc("材料分解") MATERIAL_DECOMPOSE(702),
        @ReasonDesc("连续充值最终奖励") DAILY_RECHARGE_MAX_REWARD(706),
        @ReasonDesc("内城搜刮") PLUNDER_COVER(707),

        @ReasonDesc("装备分解") EQUIP_DECOMPOSE(709),
        @ReasonDesc("CDKey兑换") EXCHANGE_CDKEY_REWARDS(710),
        @ReasonDesc("DEBUG") DEBUG_ADD(711),
        @ReasonDesc("内城事件完成") CITY_EVENT_FINISH(712),
        @ReasonDesc("pve战斗邮件领取") PVE_BATTLE_MAIL_REWARD(714),
        @ReasonDesc("解锁内城地块战斗") UNLOCK_INNER_LAND_FIGHT(715),
        @ReasonDesc("VIP每日登陆经验点数") VIP_LOGIN_EXP_ITEM(717),
        @ReasonDesc("自选宝箱") OPTIONAL_BOX(722),
        @ReasonDesc("章节任务奖励") CHAPTER_REWARD(723),
        @ReasonDesc("pve关卡首次通关奖励") PVE_SCENE_FIRST_REWARD(724),
        @ReasonDesc("pve关卡每日奖励") PVE_SCENE_DAY_REWARD(725),
        @ReasonDesc("WebCDKey兑换") EXCHANGE_WEB_CDKEY_REWARDS(727),

        @ReasonDesc("英雄重置物品归还") HERO_RESET(734),
        @ReasonDesc("英雄通用碎片兑换") EXCHANGE_HERO_FRAGMENT(735),
        @ReasonDesc("KVK迁服") MIGRATE_KVK(736),
        @ReasonDesc("新手必买免费奖励") ROOKIE_DAILY_FREE_REWARD(737),

        @ReasonDesc("装备重构新旧兑换") EQUIP_RECONSTRUCT(740),
        @ReasonDesc("跨服抢城迁服扣道具") CAS_MIGRATE_SUB_ITEM(749),

        @ReasonDesc("完成探索实践任务，领取奖励") EXPLORE_EVENT_COMPLETE_REWARD(750),
        @ReasonDesc("事件是攻击npc，胜利获得奖励") EXPLORE_EVENT_ATTACK_NPC_REWARD(751),
        @ReasonDesc("探索事件，奖励等级提升") EXPLORE_EVENT_REWARD_LEVEL_UP(752),
        @ReasonDesc("探索事件，领取物质") EXPLORE_EVENT_CENTER_REWARD(753),

        @ReasonDesc("跨服抢城任务奖励") CSA_MISSION_REWARD(754),
        @ReasonDesc("联盟福利事件分配") ALLIANCE_BENEFITS_ALLOCATE(755),
        @ReasonDesc("瑞克的射击训练") SHOOTING_TRAINING(761),
        @ReasonDesc("零元购") FREE_BUY_REWARD(763),
        @ReasonDesc("KVK坐标迁服") KVK_MIGRATE_JUMP(767),
        @ReasonDesc("团购礼包阶段奖励") GROUP_STAGE_REWARD(768),
        @ReasonDesc("宠物商店购买") PET_SHOP_BUY(769),
        @ReasonDesc("KVK赛季任务领奖") SEASON_TASK_REWARD(770),
        @ReasonDesc("KVK赛季周功勋领奖") SEASON_WEEK_TASK_REWARD(771),
        @ReasonDesc("KVK赛季切换清理") KVK_SEASON_SWITCH(775),
        @ReasonDesc("跨服夺城联赛胜场奖励") CSA_LEAGUE_WIN_PLAY_REWARD(776),
        @ReasonDesc("皮肤商店购买") SKIN_SHOP_BUY(777),
        @ReasonDesc("出售道具") SALE_ITEM(778),
        @ReasonDesc("赛季荣誉道具转换") SEASON_CONVERT(782),
        @ReasonDesc("gvg观赛") GVG_OBSERVE(785),
        @ReasonDesc("通过道具获得改装车") GET_TUNEDCAR_DESC(787),
        @ReasonDesc("首次创建联盟奖励") FIRST_CREATE_ALLIANCE(796),
        @ReasonDesc("跨服抢城个人排行奖励") CSA_ROLE_RANK_REWARD(797),
        @ReasonDesc("集成弹板H5页面领奖") SPRING_BOARD_REWARD(800),
        @ReasonDesc("新手7天礼包免费领取的奖励") NEW_PLAYER_PACKAGE_GIFT_FREE(804),
        @ReasonDesc("低语者boss击杀奖励") WHISPERER_BOSS_KILL(805),
        @ReasonDesc("低语者boss宝箱奖励") WHISPERER_BOSS_BOX(806),
        @ReasonDesc("赛季荣誉值军团") SEASON_HONOR_LEGION(807),
        @ReasonDesc("新手七日签到补发邮件") SEVEN_SIGN_REISSUE(808),
        @ReasonDesc("新手七日签到,付费补发邮件") SEVEN_SIGN_PAY_REISSUE(810),
        @ReasonDesc("赛季军团势力值排行榜结算") KVK_SETTLEMENT_LEGION_PROSPERITY(812),
        @ReasonDesc("军团福利事件分配") LEGION_BENEFITS_ALLOCATE(813),
        @ReasonDesc("参加bingo活动使用") BINGO_ITEM_JOIN_COST(814),
        @ReasonDesc("刷新bingo格子使用") BINGO_ITEM_REFRESH_COST(815),
        @ReasonDesc("bingo道具奖励") BINGO_ITEM_REWARD(816),
        @ReasonDesc("bingo每日宝箱奖励") BINGO_DAILY_BOX_REWARD(818),
        @ReasonDesc("bingo瓜分奖励") BINGO_SUPER_REWARD(819),
        @ReasonDesc("bingo幸运奖励") BINGO_LUCKY_REWARD(820),
        @ReasonDesc("跨服夺城联赛补偿奖励") CSA_SERVER_COMPENSATE(822),
        @ReasonDesc("跨服夺城友谊赛胜利奖励") CSA_NORMAL_WIN_REWARD(823),
        @ReasonDesc("TVT宝箱奖励") TVT_DAY_BOX(825),
        @ReasonDesc("TVT赛季排名奖励") TVT_SEASON_RANK_REWARD(826),
        @ReasonDesc("TVT赛季积分区间奖励") TVT_SEASON_POWER_REWARD(827),
        @ReasonDesc("钻石累计消耗补发奖励") DIAMOND_COUNT_CONSUMER_REWARD(832),

        @ReasonDesc("成就，任务领奖") ACHIEVEMENT_TASK_REWARD(836),
        @ReasonDesc("成就，点数领奖") ACHIEVEMENT_POINT_REWARD(837),
        @ReasonDesc("市政厅里程碑领奖") CITY_HALL_MILESTONE(839),
        @ReasonDesc("VIP4解锁建筑队列后，队列道具转换为建筑加速") CITY_BUILD_QUEUE_EXCHANGE(840),

        @ReasonDesc("pve战斗通关奖励") PVE_BATTER_WIN_REWARD(841),
        @ReasonDesc("pve探险挂机奖励") PVE_EXPEDITION_HANGUP_REWARD(842),
        @ReasonDesc("暴风雪奖励") BLIZZARD_REWARD(843),
        @ReasonDesc("暴风雪奖励邮件") BLIZZARD_MAIL_REWARD(844),
        @ReasonDesc("竞技场赛季奖励") ARENA_RANK_SEASON_REWARD(845),
        @ReasonDesc("竞技场每日奖励") ARENA_RANK_DAILY_REWARD(846),

        @ReasonDesc("竞技场挑战奖励") ARENA_CHALLENGE_REWARD(847),
        @ReasonDesc("装备升级") EQUIPMENT_UPGRADE(848),
        @ReasonDesc("装备升级返还") EQUIPMENT_UPGRADE_RETURN(849),
        @ReasonDesc("专属装备强化消耗") EXCLUSIVE_STRENGTHEN_COST(850),
        @ReasonDesc("山贼来袭奖励") ROBBER_REWARD(851),
        @ReasonDesc("山贼来袭奖励邮件") ROBBER_MAIL_REWARD(852),
        @ReasonDesc("pve探险挂机章节奖励") PVE_EXPEDITION_GROUP_HANGUP_REWARD(853),
        @ReasonDesc("离开联盟时自动补发所有辉煌宝箱") LEAVE_ALLIANCE_GIVE_ALL_GIFT(854),

        @ReasonDesc("傲视群雄新英雄战力提审奖励") NEW_HERO_POWER_REVIEW_REWARD(855),

        @ReasonDesc("玩家活动任务奖励") ACTIVITY_MISSION_REWARD(856),
        @ReasonDesc("保护罩过期") PROTECTION_EXPIRED(857),
        @ReasonDesc("决战王城功勋奖励") ROYAL_HONOR_REWARD(867),
        @ReasonDesc("州府战TIPS奖励") REGION_CAPITAL_TIPS_REWARD(868),
        @ReasonDesc("州府战PVE奖励") REGION_CAPITAL_PVE_REWARD(870),
        @ReasonDesc("州府战控制奖励") REGION_CAPITAL_OCCUPY_REWARD(872),
        @ReasonDesc("州府战分配奖励") REGION_CAPITAL_ALLOCATION_REWARD(873),
        @ReasonDesc("购买道具") BUY_ITEM(874),
        @ReasonDesc("新手七日任务") NEW_PLAYER_MISSION_REWARD(875),
        @ReasonDesc("新手七日宝箱任务") NEW_PLAYER_MISSION_BOX_REWARD(876),
        @ReasonDesc("新最强领主积分奖励") NEW_STRONGEST_LORDS_SCORE_REWARD(877),

        @ReasonDesc("第二建筑队列试用") BUILD_QUEUE_TRAIL(878),
        @ReasonDesc("剧情人物事件奖励") STORY_CHARACTER_EVENT_REWARD(879),
        @ReasonDesc("联盟排行奖励") ALLIANCE_RANK_REWARD(880),
        @ReasonDesc("评价奖励") EVALUATION_REWARD(881),
        @ReasonDesc("打折购买月卡") MONTHCARD_DISCOUNT_BUY(882),
        @ReasonDesc("英雄合成") HERO_COMPOUND(883),
        @ReasonDesc("切换阵营") SWITCH_CAMP(885),
        @ReasonDesc("KVK赛季个人功勋奖励") SEASON_PERSONAL_REWARD(886),
        @ReasonDesc("赛季结算联盟分配奖励") SEASON_ALLIANCE_ALLOC_REWARD(887),
        @ReasonDesc("赛季结算补发个人荣誉奖励") SEASON_PERSONAL_HONOR_REWARD(888),
        @ReasonDesc("免费宝箱") FREE_TREASURE_BOX(889),
        @ReasonDesc("民心系统任务完成奖励") POPULAR_WILL_MISSION_REWARD(890),
        @ReasonDesc("赛季服务排名奖励") SEASON_SERVER_REWARD(891),
        @ReasonDesc("赛季补发个人周功勋奖励") SEASON_WEEK_HONOR_REWARD(892),

        @ReasonDesc("商队刷新") CARAVAN_TRADE_REFRESH(910),
        @ReasonDesc("商队抢夺获得") CARAVAN_TRADE_PLUNDER(912),
        @ReasonDesc("商队完成获得") CARAVAN_TRADE_REWARD(913),
        @ReasonDesc("为国王点赞获得") LIKE_KING_REWARD(920),
        @ReasonDesc("调查问卷获得") SURVEY_REWARD(921),

        @ReasonDesc("添加小游戏获得") WECHAT_COLLECTION(925),
        @ReasonDesc("三英战吕布捐献消耗") ALLIANCE_BOSS_DONATE_CONSUME(926),
        @ReasonDesc("七擒孟获道具兑换") SEVEN_CAPTURE_EXCHANGE(927),
        @ReasonDesc("七擒孟获道具召唤") SEVEN_CAPTURE_SUMMON(928),
        @ReasonDesc("七擒孟获结算补发") SEVEN_CAPTURE_SETTLE(929),
        @ReasonDesc("私域拉新奖励1") SI_YU_LINK_NEW_USER_TYPE_1(930),
        @ReasonDesc("私域拉新奖励2") SI_YU_LINK_NEW_USER_TYPE_2(931),
        @ReasonDesc("华为小游戏") HUAWEI_COLLECTION(932),


        @ReasonDesc("金兰结契") BROTHER_HOOD_TASK_REWARD(950),
        @ReasonDesc("金兰结契邮件") BROTHER_HOOD_TASK_MAIL_REWARD(951),


        @ReasonDesc("转盘活动抽奖消耗") ICE_WHEEL_PULL_COST(961),
        @ReasonDesc("转盘活动抽奖获得单次") ICE_WHEEL_PULL_REWARD_ONCE(962),
        @ReasonDesc("转盘活动抽奖获得十连") ICE_WHEEL_PULL_REWARD_TEN(963),
        @ReasonDesc("转盘活动领取宝箱奖励") ICE_WHEEL_BOX(964),
        @ReasonDesc("转盘活动领取免费礼包") ICE_WHEEL_FREE_GIFT(965),
        @ReasonDesc("转盘活动补发邮件奖励") ICE_WHEEL_BOX_MAIL(966),

        @ReasonDesc("转盘活动购买代币") ICE_WHEEL_BUY_TOKEN(967),

        @ReasonDesc("bp结束奖励补发") BATTLE_PASS_REISSUE(970),
        @ReasonDesc("民心技能使用消耗") POPULAR_WILL_SKILL_COST(971),
        @ReasonDesc("民心技能使用效果") POPULAR_WILL_SKILL_EFFECT(972),

        @ReasonDesc("自选礼包购买") SELECTION_PACK(973),

        @ReasonDesc("群雄讨董挑战奖励") ACTIVITY_DONGZHUO_CHALLENGE_REWARD(974),
        @ReasonDesc("群雄讨董排行奖励") ACTIVITY_DONGZHUO_RANK_REWARD(975),

        @ReasonDesc("同盟角逐积分奖励") ALLIANCE_BATTLE_SCORE_REWARD(976),
        @ReasonDesc("同盟角逐胜利联盟奖励") ALLIANCE_BATTLE_WIN_REWARD(977),
        @ReasonDesc("同盟角逐失败联盟奖励") ALLIANCE_BATTLE_LOSE_REWARD(978),
        @ReasonDesc("同盟角逐每日参与奖励") ALLIANCE_BATTLE_DAILY_JOIN_REWARD(979),
        @ReasonDesc("微信首次订阅奖励") WECHAT_FIRST_SUB_REWARD(980),


        @ReasonDesc("活动任务补发邮件奖励") EVENT_MISSION_MAIL(981),

        @ReasonDesc("通用活动-阶段任务奖励") ACTIVITY_COMMON_STAGE_MISSION_REWARD(982),
        @ReasonDesc("通用活动-阶段排行奖励") ACTIVITY_COMMON_STAGE_RANK_REWARD(983),
        @ReasonDesc("通用活动-总排行奖励") ACTIVITY_COMMON_TOTAL_RANK_REWARD(984),
        @ReasonDesc("通用活动-总任务奖励") ACTIVITY_COMMON_TOTAL_MISSION_REWARD(985),
        @ReasonDesc("民心升阶奖励领取") POPULAR_WILL_STAGE_REWARD(986),
        @ReasonDesc("订阅奖励活动") WECHAT_SUBSCRIBE_REWARD(987),

        @ReasonDesc("任务boss击杀奖励") MISSION_BOSS_KILL_REWARD(988),
        @ReasonDesc("主公藏品升级") LORD_TREASURE_FORGE(989),
        @ReasonDesc("煮酒论英雄攻击BOSS") WHISPERER_BOSS_ATTACK(990),
        @ReasonDesc("煮酒论英雄攻击小怪") WHISPERER_NPC_WIN(991),
        @ReasonDesc("贸易站兑换活动") TRADE_POST_ACTIVITY(992),

        @ReasonDesc("通用活动-联盟排行奖励") ACTIVITY_COMMON_ALLIANCE_RANK_REWARD(993),

        @ReasonDesc("八方来援-损失兵力资源补偿") BATTLE_LOSE_RESOURCE_COMPENSATION(994),
        @ReasonDesc("主公藏品解锁") LORD_TREASURE_UNLOCK(995),

        @ReasonDesc("坐骑兑换") HORSE_EXCHANGE(996),
        @ReasonDesc("坐骑升级") HORSE_LEVEL_UP(997),
        @ReasonDesc("坐骑升星") HORSE_STAR_UP(998),
        @ReasonDesc("坐骑装备升级") HORSE_EQUIP_LEVEL_UP(999),

        @ReasonDesc("GVG邮件结算联盟胜利方") GVG_MAIL_REWARD_MEMBER_WIN(1100),
        @ReasonDesc("GVG邮件结算联盟失败方") GVG_MAIL_REWARD_MEMBER_LOSE(1101),
        @ReasonDesc("GVG邮件结算积分胜利方") GVG_MAIL_REWARD_POINT_WIN(1102),
        @ReasonDesc("GVG邮件结算积分失败方") GVG_MAIL_REWARD_POINT_LOSE(1103),
        @ReasonDesc("GVG邮件结算联盟轮空方") GVG_MAIL_REWARD_MEMBER_BYE(11004),

        @ReasonDesc("失去榜一") WIN_RANK_CHAMPION(1000),
        @ReasonDesc("天子皇权") OFFICIAL_AWARD(1001),
        @ReasonDesc("盟主任务") ALLIANCE_LEADER_MISSION_ADD(1002),
        @ReasonDesc("领取朋友小人系统好感度奖励") PEOPLE_FRIEND_POINT_REWARD(1003),
        @ReasonDesc("董卓秘藏-发起者领奖") DONGZHUOBOX_PUBLISHER_REWARD(1004),
        @ReasonDesc("董卓秘藏-参与者领奖") DONGZHUOBOX_PARTICIPANT_REWARD(1005),

        @ReasonDesc("pve多队讨伐通关奖励") EXPEDITION_MUL_BATTER_WIN_REWARD(1006),
        @ReasonDesc("pve多队讨伐章节奖励") EXPEDITION_MUL_GROUP_REWARD(1007),
        @ReasonDesc("同盟任务") ALLIANCE_MISSION_ADD(1008),


        @ReasonDesc("坐骑交配奖励") HORSE_MATE_REWARD(1009),

        @ReasonDesc("分享任务") SHARE_MISSION_REWARD(1010),
        @ReasonDesc("小人词条收集组领奖") GROUP_PROPERTY_REWARD(1015),
        @ReasonDesc("赛季预热积分奖励") SEASON_WARM_UP_SCORE_REWARD(1016),
        @ReasonDesc("赛季预热排名奖励") SEASON_WARM_UP_RANK_REWARD(1017),
        @ReasonDesc("代币活动") TOKEN_EVENT_REWARD(1018),


        @ReasonDesc("同盟角逐同盟积分奖励") ALLIANCE_BATTLE_ALLIANCE_SCORE_REWARD(1020),

        @ReasonDesc("通用活动-宝箱奖励") ACTIVITY_COMMON_BOX_REWARD(1021),
        @ReasonDesc("名驹养成道具消耗") HORSE_FAVOR_ITEM_COST(1022),
        @ReasonDesc("名驹养成活动奖励") HORSE_FAVOR_REWARD(1023),
        @ReasonDesc("道具换坐骑") ITEM_TO_HORSE(1024),
        @ReasonDesc("冰雪节道具消耗") SNOW_FESTIVAL_DONATE(1025),
        @ReasonDesc("冰雪节宝箱奖励") SNOW_EVENT_BOX_REWARD(1026),
        @ReasonDesc("通用活动道具回收") ACTIVITY_COMMON_ITEM_CONVERT(1027),
        @ReasonDesc("通用活动宝箱补发") ACTIVITY_COMMON_BOX_REISSUE(1028),
        @ReasonDesc("签到活动奖励") SIGN_ACTIVITY_REWARD(1029),

        @ReasonDesc("绑定邀请码奖励") BIND_INVITE_CODE(1030),
        @ReasonDesc("翻牌子奖励") LUCKY_BAG_REWARD(1031),
        @ReasonDesc("开始翻牌子") LUCKY_BAG_START(1032),
        @ReasonDesc("红包奖励") RED_PACK_REWARD(1033),
        @ReasonDesc("红包结束转换") RED_PACK_FINISH_RECYCLE(1034),

        @ReasonDesc("联盟宴席拜访奖励") ALLIANCE_FEAST_ATTEND(1035),

        @ReasonDesc("春节分享活动") SPRING_FESTIVAL_SHARE(1036),
        @ReasonDesc("裂变活动-分享得收集道具") FISSION_SHARE_WORD_ITEM(1037),
        @ReasonDesc("裂变活动-最终大奖") FISSION_SHARE_FINAL_REWARD(1038),
        @ReasonDesc("裂变活动-兑换道具") FISSION_SHARE_EXCHANGE(1039),
        @ReasonDesc("裂变活动-兑换集齐奖励") FISSION_SHARE_COLLECT_REWARD(1040),
        @ReasonDesc("裂变活动-购买通用道具") FISSION_SHARE_BUY_ITEM(1041),
        @ReasonDesc("裂变活动-回收道具") FISSION_SHARE_RECYLE_ITEM(1042),

        @ReasonDesc("建筑内探索奖励道具") BUILD_SEARCH_REWARD(1050),
        @ReasonDesc("营救道具") RESCUE_CONSUME(1060),
        @ReasonDesc("营救奖励") RESCUE_REWARD(1061),
        ;

        /**
         * 原因序号
         */
        public final int reason;
        /**
         * 原因参数
         */
        public final ParamKey[] keys;

        private static final ItemLogReason[] INDEXES = EnumUtils.toArray(values());

        private ItemLogReason(int reason, ParamKey... paramKeys) {
            this.reason = reason;
            this.keys = paramKeys;
        }

        @Override
        public int getReason() {
            return reason;
        }

        @Override
        public int getId() {
            return reason;
        }

        @Override
        public String getReasonText(Object... args) {
            return ReasonTextBuilder.builtStr(keys, args);
        }

        public static ItemLogReason findById(int value) {
            if (value < 0 || value >= INDEXES.length) {
                return null;
            }
            return INDEXES[value];
        }
    }
}
