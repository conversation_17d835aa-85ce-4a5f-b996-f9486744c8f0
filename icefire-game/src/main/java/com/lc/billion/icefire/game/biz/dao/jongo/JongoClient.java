package com.lc.billion.icefire.game.biz.dao.jongo;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.lc.billion.icefire.core.support.StringInternDeserializer;
import com.lc.billion.icefire.core.support.StringInternKeyDeserializer;
import com.lc.billion.icefire.game.MongoConfig;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.mongodb.*;
import com.mongodb.event.CommandFailedEvent;
import com.mongodb.event.CommandListener;
import com.mongodb.event.CommandStartedEvent;
import com.mongodb.event.CommandSucceededEvent;
import org.jongo.Jongo;
import org.jongo.Mapper;
import org.jongo.marshall.jackson.JacksonMapper;
import org.jongo.marshall.jackson.JacksonMapper.Builder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 我们在服务器启动的时候会建立向所有服的mongo链接。一个mongo集群中可能有多个服的库。
 *         这样如果每个jongoclient都要建立对mongo集群的链接，可能会导致游戏服针对一个mongo集群建立了很多个链接池。
 *         为合理利用mongoclient减少游戏服的线程数和缓存区，我们打算复用mongoclient。我们构造一个静态的map，key是url，value是mongoclient
 *         构造新的jongoclient时，优先从缓存里根据url查找mongoclient对象。如果这个jongoclient并非本服，而且缓存里有mongoclient对象
 *         就复用原来的mongoclient。因为出现了复用mongoclient的情况,所以在关闭mongoclient的时候要特别注意了，停服的时候可以关闭mongoclient，
 *         其他的时候关闭mongoclient可能导致别的服读库写库失败。
 */
public class JongoClient {

	private static final Logger log = LoggerFactory.getLogger(JongoClient.class);

	private Jongo jongo; // Jongo接口
//	private MongoClient client; // 用于关闭链接
	private LazyMongoClient client; // 用于关闭链接

	private String dbUrl;
	private String dbName;

//	private static Map<String, MongoClient> clientCache = new HashMap<>();
	private static Map<String, LazyMongoClient> clientCache = new HashMap<>();

	protected JongoClient() {
		// CheckConfig使用，其他勿用
	}

	/**
	 * 本服起服创建连接自己服务器db的连接
	 * 
	 * @param dbUrl
	 * @param dbName
	 */
	public JongoClient(String dbUrl, String dbName) {
		this(dbUrl, dbName, true, "-1");
	}

	/**
	 * 创建连接mongo库的连接~初始化jongoClient对象
	 * 
	 * @param dbUrl
	 * @param dbName
	 * @param isSelfServer
	 *            是否是连接本服对应的数据库
	 * @param serverId
	 *            如果连接本服~serverId=-1~否则就是去连接的dbId
	 */
	public JongoClient(String dbUrl, String dbName, boolean isSelfServer, String serverId) {
		this.dbUrl = dbUrl;
		this.dbName = dbName;
		boolean CACHE_MONGO_CLIENT = ServerConfigManager.getInstance().getGameConfig().isCacheMongoClient();
		MongoConfig mongoConfig = ServerConfigManager.getInstance().getMongoConfig();

		log.info("服务器id= {} JongoClient {} {} 创建了~ selfJongo = {}, mongoConfig is : {} ", serverId, dbUrl, dbName, isSelfServer, mongoConfig);
		MongoClientURI url = new MongoClientURI(dbUrl);
		List<String> hosts = url.getHosts();
		if (hosts == null || hosts.size() <= 0) {
			throw new AlertException("mongodb数据库连接配置的连接地址有问题");
		}
		List<ServerAddress> seeds = hosts.stream().map(host -> new ServerAddress(host)).collect(Collectors.toList());

		MongoCredential credential = null;
		if (url.getUsername() != null) {
			credential = MongoCredential.createCredential(url.getUsername(), url.getDatabase(), url.getPassword());
		}
		MongoClientOptions.Builder builder = new MongoClientOptions.Builder(url.getOptions());
		if (isSelfServer) {
			builder.minConnectionsPerHost(mongoConfig.getSelfMinConnectionsPerHost());
			builder.connectionsPerHost(mongoConfig.getSelfConnectionsPerHost());
			builder.maxConnectionIdleTime(mongoConfig.getSelfMaxConnectionIdleTime());
			builder.maxConnectionLifeTime(mongoConfig.getSelfMaxConnectionLifeTime());
			builder.maxWaitTime(mongoConfig.getSelfMaxWaitTime());
			builder.connectTimeout(mongoConfig.getSelfConnectTimeout());
			// builder.threadsAllowedToBlockForConnectionMultiplier(mongoConfig.getSelfThreadsAllowedToBlockForConnectionMultiplier());
		} else {
			builder.minConnectionsPerHost(mongoConfig.getOtherMinConnectionsPerHost());
			builder.connectionsPerHost(mongoConfig.getOtherConnectionsPerHost());
			builder.maxConnectionIdleTime(mongoConfig.getOtherMaxConnectionIdleTime());
			builder.maxConnectionLifeTime(mongoConfig.getOtherMaxConnectionLifeTime());
			builder.maxWaitTime(mongoConfig.getOtherMaxWaitTime());
			builder.connectTimeout(mongoConfig.getOtherConnectTimeout());
			// builder.threadsAllowedToBlockForConnectionMultiplier(mongoConfig.getOtherThreadsAllowedToBlockForConnectionMultiplier());
		}
		builder.addCommandListener(new MyCommandListener());

		MongoClientOptions options = builder.build();
//		MongoClient cache = null;
		LazyMongoClient cache = null;
		if (CACHE_MONGO_CLIENT && !isSelfServer) {
			cache = clientCache.get(dbUrl);
		}
		if (cache != null) {
			this.client = cache;
		} else {
//			this.client = new MongoClient(seeds, credential, options);
			this.client = new LazyMongoClient(seeds, credential, options);
			if (CACHE_MONGO_CLIENT) {
				clientCache.putIfAbsent(dbUrl, this.client);
			}
		}

		DB database = this.client.getDB(dbName);
		Builder mapperBuilder = new JacksonMapper.Builder().enable(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY).disable(MapperFeature.AUTO_DETECT_SETTERS)
				.disable(MapperFeature.AUTO_DETECT_GETTERS).disable(MapperFeature.AUTO_DETECT_IS_GETTERS);
		// if
		// (ServerConfigManager.getInstance().getGameConfig().isJongoMapperFailOnUnknownPrpperties())
		// {
		mapperBuilder.enable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		var simpleModule = new SimpleModule("string-intern");
		simpleModule.addDeserializer(String.class, new StringInternDeserializer());
		simpleModule.addKeyDeserializer(String.class, new StringInternKeyDeserializer());
		mapperBuilder.registerModule(simpleModule);
		// }
		// 自定义ObjectIdUpdater，提升性能
		// 先在DEV跑一段时间，没问题再上PROD
		Mapper jm;
//		GameConfig gameConfig = ServerConfigManager.getInstance().getGameConfig();
//		if (gameConfig.getEnv() == Env.DEV) {
			jm = mapperBuilder.withObjectIdUpdater(new CustomObjectIdUpdater()).build();
//		} else {
//			jm = mapperBuilder.build();
//		}
		this.jongo = new Jongo(database, jm);
	}

	public Jongo get() {
		if (this.jongo == null) {
			throw new AlertException("jongo未初始化,是否使用了错误的构造函数JongoClient()");
		}
		return jongo;
	}

	public void close() {
		this.close(true);
	}

	/**
	 * 请看Jongo类的注释
	 * 
	 * @param realClose
	 */
	public void close(boolean realClose) {
		log.info("关闭MongoClient：{},{} 是否真的关闭?{}", dbUrl, dbName, realClose);
		if (realClose) {
			this.client.close();
		}
	}

	class MyCommandListener implements CommandListener {

		@Override
		public void commandStarted(CommandStartedEvent event) {
			// TODO Auto-generated method stub

		}

		@Override
		public void commandSucceeded(CommandSucceededEvent event) {
			// TODO Auto-generated method stub

		}

		@Override
		public void commandFailed(CommandFailedEvent event) {
			// TODO Auto-generated method stub
			ErrorLogUtil.errorLog("commandFailed","发送内容",event.getCommandName(),"接收方",event.getConnectionDescription(),"失败耗时",event.getElapsedTime(TimeUnit.MILLISECONDS),"失败原因",event.getThrowable().toString());
		}

	}

}
