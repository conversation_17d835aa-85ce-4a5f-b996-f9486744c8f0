package com.lc.billion.icefire.game.biz.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.happysky.misc.Time;
import com.lc.billion.icefire.core.application.ServerType;
import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.core.config.model.IntKeyIntValue;
import com.lc.billion.icefire.core.config.model.IntKeyStringValue;
import com.lc.billion.icefire.core.config.model.StringKeyIntValue;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.BizException;
import com.lc.billion.icefire.game.biz.config.setting.DefusalRewardConfig;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.currency.CurrencyValue;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.prop.AttributeValue;
import com.simfun.sgf.utils.JavaUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.lc.billion.icefire.core.config.model.AbstractMeta.*;

/**
 * <AUTHOR>
 */
@Config(name = "Setting")
@Getter
@Setter
public class SettingConfig {

    /**
     * 英雄重置功能相关配置。格式：重置次数|开始时间|结束时间
     */
    private String heroRestProperty;

    /**
     * 英雄通用碎片配置。格式：英雄品质,通用碎片ID;英雄品质,通用碎片ID
     */
    private String fragmentExchanged;

    /**
     * 内政英雄重置道具，如果有值，则重置橙色内政英雄的时候将其勋章转换为该值，否则原封不动退回对应勋章
     */
    private String developmentResetItem;

    /**
     * 玩家初始行动力上限
     */
    private int initialEnergy;

    /**
     * 每多少秒回复一点体力
     */
    private int eneryRecoverSpeed;
    /**
     * 体力补充时间
     */
    private int[] energyRefreshArr;
    /**
     * 每次补给多少体力
     */
    private int energyRecovery;
    /**
     * 时间基数，毫秒
     */
    private long timeBase;


    /**
     * 掠夺消耗行动力点数
     */
    private int robEnergyCost;

    /**
     * 迁城花费钻石
     */
    private int seniorMoveCityCost;

    /**
     * 玩家改名钻石价格
     */
    private int playerRenamePrice;
    /**
     * 改名冷却时间
     */
    private int changeNameCoolDown;

    /**
     * 新手保护罩时间
     */
    private int newRoleProtectionTime;
    /**
     * 新手保护解除城堡等级上限
     */
    private int protectLevelLimit;
    /**
     * 新手出征加速有效时间  ：秒
     */
    
    private int newRoleSpeedTime;
    /**
     * 新手出征加速解除的主城等级
     */
    
    private int speedLevelLimit;
    /**
     * 初始资源
     */
    private CurrencyValue[] initResources;

    /**
     * 国王公告冷却时间
     */
    private int kingAnnouncementCD;


    /**
     * 初始物品
     */
    private List<SimpleItem> initItems;

    /**
     * 主城升级奖励
     */
    private List<IntKeyStringValue> commanderCenterUpgradeMails;

    /**
     * 初始化士兵
     */
    private List<StringKeyIntValue> initSoldiers;
    /**
     * 初始化伤兵
     */
    private List<StringKeyIntValue> initWoundedSoldiers;

    /**
     * 玩家初始化头像
     */
    private String[] playerHeads;

    /**
     * 头像上传成功后再次上传时间
     */
    private long cdTime;

    /**
     * 头像被举报生效后再次上传时间
     */
    private long cdTimeRe;

    /**
     * 头像被举报次数阀值
     */
    private int reportHeadNum;

    /**
     * 联盟自动rank，rank2所需贡献
     */
    private int rank2num;
    /**
     * 联盟自动rank，rank3所需贡献
     */
    private int rank3num;
    /**
     * 联盟自动rank，rank4所需贡献
     */
    private int rank4num;

    /**
     * 建筑免费加速时间，单位秒
     */
    private int buildingFreeTime;

    /**
     * 城堡初始等级
     */
    private IntKeyIntValue[] initCastle;
    /**
     * 玩家进入游戏后拥有初始属性
     */
    @JsonIgnore
    private AttributeValue[] initialPropertys;
    // 产生英雄共鸣数量
    private int heroResonanceLimit;
    /**
     * 治疗队列最小时长
     */
    private long healTimeLimit;

    private int activityKingdomWarLevel;

    /**
     * 黑骑士守城伤兵复活加成
     */
    private float activityBlackKnightReviveRate;
    /**
     * 黑骑士守城战力排名前的玩家
     */
    private float activityBlackKnightTarget;
    /**
     * 黑骑士参与等级
     */
    private int activityBlackKnightLevel;

    /**
     * 资源点刷新周期（毫秒）
     */
    private long resFreshTime;
    /**
     * 抽到特殊英雄，用于发通知
     */
    private int[] heroBroadcast;
    // 燃烧状态每秒掉多少城防值
    private int wallFireDamage;
    // 修理城墙每次增加值|修理的冷却时间（分）
    private int[] wallRepairIntervalNums;
    // 首充奖励
    private Map<String, String> firstRechargeLibaoId;
    private Map<String, List<SimpleItem>> firstRechargeRewards;
    private Map<String, List<SimpleItem>> firstRechargeRewardsSecondDay;
    private Map<String, String> firstRechargePlans;
    private Map<String, long[]> firstRechargeABTestTime;
    private Map<String, Set<Integer>> firstRechargeABTestServer;
    private String directMoveCityItem;
    private String randomMoveCityItem;
    private String allianceMoveCityItem;
    private long npcLiveTime;
    private String[] initHeros;
    /**
     * vip每日登录奖励
     */
    private int[] vipLoginExps;

    private long scoutresource;

    private Map<Currency, float[]> cureResourceCostCPParam;// 用钻石秒资源公式

    private long allianceMemberOffline;

    private String defaultfashion;

    private int fashionSwitch;

    private double soldierPowerParameter;

    /**
     * 视频广告次数限制
     */
    private int videoAdLimit;

    private int[] brushplayers;

    private boolean openCodUidExport;

    /**
     * 随机事件等级总量
     */
    private Map<Integer, Integer> numberOfEvents;
    /**
     * 随机事件刷新间隔
     */
    private int[] refreshInterval;
    /**
     * 随机事件刷新比重区间
     */
    @JsonIgnore
    private Map<String, Double> eventProbability;

    private int[] eventProbWights;
    private String[] eventProbValues;
    private int[] eventProbPointer;

    /**
     * 切换职业消耗的道具ID
     */
    private int[] playerLevelPolicyChanges;

    private long activityZhuanPanTime;

    private int initAtkNpcLevel;

    private int foodInitialUpperLimit;
    private int waterInitialUpperLimit;
    private int woodInitialUpperLimit;
    private int ironInitialUpperLimit;

    private int stamineLimit;// 每个出征队列体力上限
    private int stamineRecovery;// 体力恢复速度stamineRecovery（单位s）/点体力

    private int baseTrainCount; // 单次训练时的初始默认值

    // 新手战斗损失保护冷却时间
    private int lossCompanstaeCd;

    // 系统迁城补偿邮件id
    private String mailNewMapLocation;

    // 首次绑定第三方账号奖励邮件Id
    private String firstBindAccountRewardMailID;

    // 新玩家第1次全图刷新每个100*100刷新数量
    private int worldPlayerCount1;
    // 新玩家第2次全图刷新每个100*100刷新数量
    private int worldPlayerCount2;

    private int mandatoryRecall;// 1:启动服务器强制召回所有行军 0：该功能关闭

    // ===============================================================

    // 侦查基础单位行军时间系数
    private double reconMoveSpeedBase;
    // 初始耐久值
    private int initialBaseDurability;
    // 初始耐久值上限
    private int initialBaseDurabilityLimit;
    // 每分钟耐久恢复值
    private int durabilityRecoverySpeed;
    // 燃烧持续时间，单位秒
    private int burnTime;
    // 冒烟持续时间，单位秒
    private int smokeTime;
    // 每日恢复耐久钻石梯度价格，超出配置次数后按最后一次计算
    
    private int wallFireFightingCost;
    // 花费钻石单次恢复耐久值
    private int diamondDurabilityRecoveryNum;
    // 打飞后重新生成基地时的耐久值
    private int rebuildInitialDurabilityNum;
    // 废墟存留时间，单位秒
    private int ruinTime;

    private long commonBossFreshTime;

    @JsonIgnore
    /** GOOGLE平台包名：整包版本号：热更包版本号|IOS平台包名：整包版本号：热更包版本号|其他平台包名：整包版本号：热更包版本号 */
    private Map<String, String[]> forceUpdateVersionMap = new HashMap<>();

    private String nameplateDefault;

    private String defaultChatBubble;
    private String defaultHeadPortraitFrame;

    private int allianceGiftPushLimit;
    /**
     * 单位s
     */
    private int allianceGiftPushTime;

    private int continuousRechargeNewDuration;

    // 资源持续时间
    
    private int resDuration;
    
    @JsonIgnore
    private int[] mapSearchRangeNpc;
    
    @JsonIgnore
    private int[] mapSearchRangeWorldBoss;
    
    @JsonIgnore
    private int[] mapSearchRangeRes;

    private Map<String, String> heroRecruitmentConditionCounts;

    /**
     * 获得英雄提示公告所需品质
     */
    private int pumpingCardScrollMessage;

    /**
     * 系统内公告滚动速度
     */
    private int scrollingInformationSpeed;

    private int shieldedUsageInterval; // 保护罩使用间隔

    private int lampDisplayConditions;

    /**
     * 新手移民使用新手移民道具
     */
    private String newPlayerImmigrants;
    /**
     * 正常移民使用迁城道具
     */
    private String normalImmigration;

    /**
     * 保护罩记录存留时长
     */
    private long shieldLogSaveTime;

    /**
     * 冒烟战损比
     */
    private double smokeHurtPercent;

    /**
     * 贵重物品bilog
     */
    private List<Integer> preciousItemRecords;

    @JsonIgnore
    private Map<Integer, String> warFeverBuffLevelMap;

    private String customAvatarRejectMail;// 自定义头像被人工审核拒绝后，收到的邮件id by 韩喆

    // 每日最大可以运输出去的上限
    private long dailyMaximumAmountOfResourceAssistance;

    // 解锁金币采集 的科技ID
    private int unlockCoinGather;
    // 解锁铁采集 的科技ID
    private int unlockSteelGather;
    // 联盟入盟申请与邀请过期时间
    private int allianceAppDeleteTime;

    // 联盟推荐参数
    private int[] clanRecommendLevels;

    // 称号开启等级
    private int titleLevelLimit;

    // Setting 里面 communityGotoReward : 100|200,1002200|1002201
    // Discord钻石奖励数额|facebook钻石奖励数额,discord奖励邮件ID|facebook奖励邮件ID
    // communityGotoRewards 数组: discord奖励邮件ID, facebook奖励邮件ID
    private String[] communityGotoRewards;

    // 按钮钻石奖励增加等级开关
    private int communityGotoRewardLevel;

    // 战斗buff伤害效果 值域范围
    private double buffDamageMin;
    private double buffDamageMax;

    // 国王任命倒计时（秒）
    private int kingAppointmentTiming;

    private int gvgBuildingReinforcementMaxTeam;// GVG建筑援军最大队伍数
    private int gvgBuildingReinforcementBattleTeam;// GVG建筑援军每次出战最大队伍数

    // 国王转让邮件id
    private String kingTransferMail;
    // 国王未上线直接下野邮件id
    private String kingTransferNobodyResignMail;
    // 国王所在联盟解散邮件id
    private String kingDisbandClanResignMail;
    // 国王超时未上线转让，超时时间（秒）
    private int kingOvertimeTransfer;

    //官职申请列表上限
    
   
    private int officerPositionApplicationListLimit;
    //官职申请列表上限
    
   
    private int season2officerPositionApplicationListLimit;
    //玩家申请有效时间
    
   
    private int playerApplicationValidityPeriod;
    //玩家申请有效时间
    
   
    private int season2playerApplicationValidityPeriod;
    //玩家再申请官职CD
    
   
    private int playerReapplicationCooldown;
    //玩家再申请官职CD
    
   
    private int season2playerReapplicationCooldown;
    //官职预约列表数量上限
    
   
    private Map<String, Integer> officerPositionReservationListLimitMap;
    //天子使用邮件CD
    
   
    private int emperorEmailUsageCooldown;
    //天子使用邮件CD
    
   
    private int season2emperorEmailUsageCooldown;
    //旧服务器威望值
    
   
    private Map<Integer, Integer> oldServerPrestigeMap;
    //占领王城后可重新任命天子时长
    
   
    private int emperorReappointmentTimeLimit;
    //占领王城后可重新任命天子时长
    
   
    private int season2emperorReappointmentTimeLimit;
    //玩家每次开采农舍资源量获取威望值
    
   
    private int prestigePerLumberYardExtraction;
    //玩家每次开采伐木场资源量获取威望值
    
   
    private int prestigePerFarmhouseExtraction;
    //玩家每次开采煤矿资源量获取威望值
    
   
    private int prestigePerCoalMineExtraction;
    //玩家每次开采铁矿资源量获取威望值
    
   
    private int prestigePerIronMineExtraction;
    //申请官职的等级限制
    
   
    private int unlockRequirements;
    //申请官职的等级限制
    
   
    private int season2unlockRequirements;
    //官职预上任
    
   
    private int[] reservationTimeLimitArray;
    /**
     * 朋友小人死亡扣除好感度
     */
    
   
    private int peopleDieDecreasedFavorability;


    private int[] propertyParams40122;// 战斗技能洞察prop40122,对应的免疫buff id列表

    /**
     * 新手必买礼包持续时间（秒）
     */
    private int rookieGiftDurationTime;
    /**
     * 新手必买礼包每日免费奖励
     */
    private String rookieGiftDailyFreeReward;

    /**
     * 资源自动打点的发送最低级别限制
     */
    private int rssBiSendLevel;
    /**
     * 资源自动打点的发送间隔时间~单位分钟
     */
    private int rssBiSendDurationMinutes;
    /**
     * 资源自动打点开放的服务器
     */
    private Set<Integer> rssBiOpenServers = new HashSet<>();
    /**
     * 探索事件：每天UTC时间0点、12点时间在玩家主城周围刷新普通事件
     */
    private int[] exploreEventCommonResetTimeArr;
    /**
     * 探索事件：迷雾探索事件数量同时最多只显示8个
     */
    private int exploreEventFogShowCountMax;
    /**
     * 探索事件：迷雾探索多余的事件保存在探索事件队列， 7. 事件队列中的事件倒计时仍然在缩短，8.
     * 探索事件队列的上限为20个（可配置），达到队列上限后不再触发探索事件
     */
    private int exploreEventFogPoolCountMax;
    /**
     * 特殊事件现实数量限制
     */
    private int exploreEventSpecialShowCountMax;
    /**
     * 特殊事件队列池数量限制
     */
    private int exploreEventSpecialPoolCountMax;

    /**
     * 装备分配时每个玩家最多可以获得多少件装备
     */
    private int itemAllocationNum;

    /**
     * 英雄重置道具信息
     */
    private StringKeyIntValue heroResetItemInfo;

    /**
     * Castle,SurvivorCamp使用新版本怪数据的最低编年史版本
     */
    private int survivorCampUseNewVersionMilestoneMinVersion;
    /**
     * 零元购返时间：天
     */
    private int freeBuyTime;
    /**
     * 零元购活动持续时间: 秒
     */
    private int freeBuyLastTime;
    /**
     * 0元购持续时间,配置方式：档位|持续时间，档位|持续时间,档位|持续时间
     */
    private Map<Integer, Integer> freeBuyLastTimesMap;
    /**
     * 0元购玩家可参加的注册时间差:秒
     */
    private int freeBuyNewPlayerTime;
    /**
     * 玩家迁服时，只能迁徙到自己所在服务器附近的服务器，两个服务器之间的ID差距，无法大于上面配置的这个整数值
     */
    private int migrationAdjacent;
    /**
     * 退服限制等级
     */
    private int exitClanLimitLevel;
    /**
     * 退服限制冷却时长
     */
    private long exitClanCD;

    /**
     * 赛季迁服退服限制等级
     */
    private int exitClanLimitLevelOfMigration;
    /**
     * 赛季迁服退服限制冷却时长
     */
    private long exitClanCDOfMigration;


    /**
     * 皮肤代币物品id
     *
     * @param json
     */
    private String sellTokenItem;

    /**
     * 单个服务器最大角色数量:3
     */
    private int serverTransferRoleMaxNum;

    /* 新手必买免费假礼包掉落组（填Dropgroup表id） */
    private String newPlayerMustBuyFreeLiBao;

    // 普通卡池第一次抽卡黑盒 （填Dropgroup表id）
    private String firstCommonRecruitBlackBox;

    /**
     * 调用中台接口，加个开关控制是调用正式接口还是测试接口 这个是后加的，目前 退出聊天室，销毁聊天室 这个2个接口用到啦 open 代表 调用测试接口
     * null或其他值 默认代表 调用正式接口
     */
    private String chatSdkRequestTestSwitch;

    /**
     * 成就系统，新增任务没有领奖需要下发的邮件id
     */
    private String achievementNoReceiveDropId;

    private int initialTroopCapacity;
    private int baseMarchTime;

    
    private int expeditionHangupMaxSeconds;

    /**
     * 暴风雪配置
     */
    
    private int warningTimeBlizzard;
    
    private int unlockNormalBlizzardCd;
    
    private String rewardBlizzard;
    private String mailBlizzard;
    
    private List<int[]> blizzardTriggerCondition;
    
    private List<int[]> blizzardResistTriggerCondition;
    /**
     * 山贼来袭配置
     */
    
    private int warningTimeRobber;
    
    private String rewardRobber;
    private String mailRobber;
    
    private List<int[]> robberTriggerCondition;
    /**
     * 侦查等级差
     */
    
    private int scoutLevelLimitation;

    /**
     * 用户离线多长时间之后开始计算离线收益。单位：秒
     */
    
    private int offlineDuration;
    /**
     * 离线收益一共多长时间。单位：小时
     */
    
    private int outputCap;

    /**
     * 装备锻造最低品质
     */
    
    private int equipForgeQuality;
    
    private int equipForgeLevel;
    /**
     * 参与集结世界boss可以获奖的次数 每日重置
     */
    
    @JsonProperty("RallyRewardLimitation1")
    private int rallyRewardLimitation1;

    /**
     * 七日签到 奖励内容
     */
    
    private List<String> loginRewardMetaIdArr;

    
    @JsonIgnore
    private double[] fightSoldierDamageRatio;
    
    @JsonIgnore
    private double[] fightAttackSoldierExponentRatio;
    
    @JsonIgnore
    private double[] fightDefenderSoldierExponentRatio;
    @JsonIgnore
    
    private double[] slgDeadConvertRatio;

    // 低于等于这个等级的玩家首次加入一个联盟需要自动迁城到盟主附近
    
    private int automatedCityMove;

    
    private int npcEventCd;
    
    private int npcEventRemain;
    
    private String discountMonthcardItem;
    
    private int exploreEventUnlockBoss;
    
    private int exploreEventLimitBoss;

    
    private int rallyRewardLimitation2;

    
    private int menghuoLimit;

    
    @JsonIgnore
    private String[] autoHuntingMonthCardType = new String[0];
    
    @JsonIgnore
    private Map<String, String> siyulinkNewUserRewardMap;

    private int joinPveRallyTimeLimit;

    
    private String battlePassReissueMailId;
    /**
     * 被攻陷损失的城防值
     */
    
    private int attackCityDamage;

    
    private int peopleCountLimitMax;

    /**
     * 战损资源补偿
     */
    
    @JsonProperty("bafangresourcecompensation")
    private double baFangResourceCompensation;

    /**
     * 战损资源补偿的条件
     */
    
    @JsonProperty("bafangcondition")
    private int baFangCondition;


    // 后备营
    
    @JsonIgnore
    private double[] enlistCapacityRatio;
    
    private double reservecampfreenum;
    
    private double reservecampdeadnum;
    
    @JsonIgnore
    private List<int[]> warChestRecover;
    
    @JsonIgnore
    private List<Float> militaryHospitalRemindMail;
    
    private int playerRecallPushTime;
    
    private int peopleShareBornMaxCount;

    // 订阅有礼活动奖励
    
    private String weChatSubscribeReward;
    // 榜一称号,k 是榜类型， v是对应的称号道具ID
    private Map<Integer, String> rankFashion;
    // 称号发放周期，单位分钟
    private int personalRankingSettlementTime;
    /**
     * 默认解锁的最大区域
     */
    
    private int unlockRegion;
    
    private double mailPowerGap;
    
    private String lordTreasureUnlockReward;
    
    private String lordTreasureJadeUnlockReward;

    @JsonIgnore
    
    private Map<Integer, Integer> heroBondSLGCampRestraintMap;
    
    private String heroBondSLGCampRestraint;
    
    private double heroBondSLGDamageUp;
    
    private double heroBondSLGRestrained;

    
    private int joinPeopleTimeLimit;

    
    private float autoJoinRallyLimitation;

//    
//    private TreeMap<Double, Double> plunderLimit = new TreeMap<>();

    
    @JsonProperty("duplicateNamePlayer")
    private String duplicateNamePlayerMailId;

    /**
     * 朋友小人相关  start
     */
    
    private int peopleLimit;
    
    private double peopleEfficiency;
    
    private int peopleNumMax;
    
    private Map<Integer, Integer> peopleOldUserFrdPoint;
    /**
     * key 权重
     * value 友情点
     */
    
    private Map<Integer, Integer> peopleNewUserFrdPoint;
    
    private int peopleFavorabilityLimit;
    
    private int npcFavorabilityLimit;
    /**
     * 被分享上限
     */
    
    private int numPeopleShared;
    /**
     * 朋友小人死亡回复时间 单位秒
     */
    
    private long timeRecoveryFriend;

    
    private long timeOptionA;
    
    private long timeOptionB1;
    
    private long timeOptionB2;

    /**
     * 朋友小人相关  end
     */

    
    private long shareActivityTime;

    // 董卓秘藏每日参与次数
    
    private int participantNum;

    
    private int horseMatingCount;

    
    private int horseMatingRuntime;

    private String horseMatingBigWin;
    
    private int[] horseMatingBigWinInts;

    
   
    private int peopleInteractionLimit;
    
   
    private int peopleChatLimit;

    
   
    private int serverGroup;
    
   
    private int nearServerRankOpen;


    @JsonIgnore
    private List<String> warDayActivityIds = new ArrayList<>();

    
   
    private int[][] invitationCodeReward;
    
   
    private int[] invitationCodeModifyCost;
    
   
    private int invitationCodeModifyCD;
    
   
    private int invitationCodeLvLimit;
    
   
    private int invitationCodeExpireDay;
    
   
    private int horseNameCost;
    
   
    private int[] horseChangeNameRule;
    
   
    private long horseNameCoolDown;
    
   
    private int horseNameStarLimit;
    private int receiveRedPocketTime;


    // 付费刷新烽火台事件的个数
    
    @JsonProperty("payEventFreshCount")
    private int payEventRefreshCount;
    /**
     * 主播活动赛季结算特殊奖励
     */
    @Getter
    private long specialSeasonRewardStart;
    @Getter
    private long specialSeasonRewardEnd;
    @Getter
    //    主播活动赛季结算特殊奖励 运行的平台
    private ServerType specialSeasonRewardServerType;

    /**
     * 拆弹奖励配置解析结果，用于缓存解析后的数据结构
     */
    @JsonIgnore
    @Getter
    private DefusalRewardConfig defusalRewardConfig;

    public void init(JsonNode json) {
        survivorCampUseNewVersionMilestoneMinVersion = MetaUtils.tryParseInt(json.path("SurvivorCampUseNewVersionMilestoneMinVersion").asText());

        clanRecommendLevels = MetaUtils.parseInts(json.path("clanRecommendLevel").asText(), META_SEPARATOR_2);

        this.allianceMemberOffline = allianceMemberOffline * TimeUtil.DAY_MILLIS;
        this.timeBase = timeBase * TimeUtil.SECONDS_MILLIS;
        this.cdTime = TimeUtil.MINUTE_MILLIS * json.path("cdTime").asInt();
        this.cdTimeRe = TimeUtil.MINUTE_MILLIS * json.path("cdTimeRe").asInt();
        this.healTimeLimit = this.healTimeLimit * TimeUtil.SECONDS_MILLIS;

        this.heroBroadcast = MetaUtils.parseInts(json.path("herobroadcast").asText(), META_SEPARATOR_2);

        this.playerHeads = MetaUtils.parse(json.get("playerHeadInitial").asText());

        this.initResources = MetaUtils.parseObjects(json.path("initResource").asText(), CurrencyValue.class);

        this.initItems = MetaUtils.parseObjectList(json.path("initItem").asText(), SimpleItem.class);

        this.initSoldiers = MetaUtils.parseObjectList(json.path("initSoldier").asText(), StringKeyIntValue.class);
        this.initWoundedSoldiers = MetaUtils.parseObjectList(json.path("initWounded").asText(), StringKeyIntValue.class);

        this.commanderCenterUpgradeMails = MetaUtils.parseObjectList(json.path("commanderCenterUpgradeMail").asText(), IntKeyStringValue.class);

        this.initCastle = MetaUtils.parseObjects(json.path("initialCastleLv").asText(), IntKeyIntValue.class);
        this.initialPropertys = MetaUtils.parseObjects(json.path("initialProperty").asText(), AttributeValue.class);

        this.resFreshTime = this.resFreshTime * TimeUtil.MINUTE_MILLIS;
        this.commonBossFreshTime = this.commonBossFreshTime * TimeUtil.SECONDS_MILLIS;

        wallRepairIntervalNums = MetaUtils.parseInts(json.path("wallRepairIntervalNum").asText());
        npcLiveTime = npcLiveTime * TimeUtil.HOUR_MILLIS;

        // 首冲配置
        this.firstRechargeLibaoId = new HashMap<>();
        this.firstRechargeRewards = new HashMap<>();
        this.firstRechargeRewardsSecondDay = new HashMap<>();
        firstRechargePlans = MetaUtils.parseStringMap(json.path("firstRechargeAB").asText(), AbstractMeta.META_SEPARATOR_1, META_SEPARATOR_2);
        firstRechargePlans.forEach((var key, var value) -> {
            var firstRechargeInfo = MetaUtils.parseStringList(json.path(value).asText(), AbstractMeta.META_SEPARATOR_3);
            var libaoId = firstRechargeInfo.get(0);
            var rewards = MetaUtils.parseObjectList(firstRechargeInfo.get(1), SimpleItem.class);
            var rewardsSecondDay = MetaUtils.parseObjectList(firstRechargeInfo.get(2), SimpleItem.class);
            this.firstRechargeLibaoId.put(value, libaoId);
            this.firstRechargeRewards.put(value, rewards);
            this.firstRechargeRewardsSecondDay.put(value, rewardsSecondDay);
        });
        var firstRechargeABConfig = MetaUtils.parseStringList(json.path("firstRechargeABConfig").asText(), AbstractMeta.META_SEPARATOR_3);
        firstRechargeABTestTime = new HashMap<>();
        firstRechargeABTestServer = new HashMap<>();
        firstRechargeABConfig.forEach(str -> {
            var times = MetaUtils.parseStringList(str, AbstractMeta.META_SEPARATOR_1);
            var type = times.get(0);
            var start = TimeUtil.parseStr2MillTime(times.get(1), "yyyy-MM-dd HH:mm:ss");
            var end = TimeUtil.parseStr2MillTime(times.get(2), "yyyy-MM-dd HH:mm:ss");
            var serverIds = MetaUtils.parseInts(times.get(3), AbstractMeta.META_SEPARATOR_2);
            var ids = new HashSet<Integer>();
            Arrays.stream(serverIds).forEach(ids::add);
            firstRechargeABTestTime.put(type, new long[]{start, end});
            firstRechargeABTestServer.put(type, ids);
        });
        this.vipLoginExps = MetaUtils.parseInts(json.path("vipLoginExp").asText(), '|');

        initHeros = MetaUtils.parse(json.path("initHero").asText());

        // 钻石秒资源公式
        cureResourceCostCPParam = new HashMap<>();
        float[] foodParam = MetaUtils.parseFloats(json.get("cureShiWuPsd").asText(), ',');
        float[] woodParam = MetaUtils.parseFloats(json.get("cureGangPsd").asText(), ',');
        float[] waterParam = MetaUtils.parseFloats(json.get("cureShiYouPsd").asText(), ',');
        float[] goldParam = MetaUtils.parseFloats(json.get("cureXituPsd").asText(), ',');
        float[] IRONParam = MetaUtils.parseFloats(json.get("cureTieKuangPsd").asText(), ',');
        float[] heroExpParam = MetaUtils.parseFloats(json.get("cureCombatExpPsd").asText(), ',');
        float[] heroDevelopExpParam = MetaUtils.parseFloats(json.get("cureDevelopmentExpPsd").asText(), ',');

        cureResourceCostCPParam.put(Currency.FOOD, foodParam);
        cureResourceCostCPParam.put(Currency.WOOD, woodParam);
        cureResourceCostCPParam.put(Currency.WATER, waterParam);
        cureResourceCostCPParam.put(Currency.GOLD, goldParam);
        cureResourceCostCPParam.put(Currency.IRON, IRONParam);
        cureResourceCostCPParam.put(Currency.HERO_EXP, heroExpParam);
        cureResourceCostCPParam.put(Currency.HERO_DEVELOP_EXP, heroDevelopExpParam);

        this.soldierPowerParameter = json.get("soldierPowerParameter").asDouble();

        this.videoAdLimit = json.get("videoAdLimit").asInt();

        brushplayers = MetaUtils.parseInts(json.get("brushplayer").asText(), META_SEPARATOR_2);

        this.numberOfEvents = MetaUtils.parseIntMap(json.path("numberofevents").asText(), AbstractMeta.META_SEPARATOR_1, META_SEPARATOR_2);

        this.refreshInterval = MetaUtils.parseInts(json.path("refreshinterval").asText(), META_SEPARATOR_2);

        StringKeyIntValue[] eventObjs = MetaUtils.parseObjects(json.path("eventProbability").asText(), StringKeyIntValue.class);
        eventProbWights = new int[eventObjs.length];
        eventProbValues = new String[eventObjs.length];
        eventProbPointer = new int[eventObjs.length];
        for (int i = 0; i < eventObjs.length; i++) {
            StringKeyIntValue event = eventObjs[i];
            eventProbWights[i] = event.getCount();
            eventProbValues[i] = event.getId();
            eventProbPointer[i] = i;
        }

        this.eventProbability = new LinkedHashMap<>();
        int totalWeight = 0;
        Map<String, Integer> eventPeriods = new LinkedHashMap<>();
        for (StringKeyIntValue eventObj : eventObjs) {
            eventPeriods.put(eventObj.getId(), totalWeight);
            totalWeight += eventObj.getCount();
        }
        for (Map.Entry<String, Integer> eventPeriod : eventPeriods.entrySet()) {
            this.eventProbability.put(eventPeriod.getKey(), (double) eventPeriod.getValue() / totalWeight);
        }

        playerLevelPolicyChanges = MetaUtils.parseInts(json.get("playerLevelPolicyChange").asText(), META_SEPARATOR_2);

        mapSearchRangeNpc = MetaUtils.parseInts(json.path("mapSearchRangeNpc").asText(), META_SEPARATOR_2);
        mapSearchRangeWorldBoss = MetaUtils.parseInts(json.path("mapSearchRangeWorldBoss").asText(), META_SEPARATOR_2);
        mapSearchRangeRes = MetaUtils.parseInts(json.path("mapSearchRangeRes").asText(), META_SEPARATOR_2);
        heroRecruitmentConditionCounts = MetaUtils.parseStringMap(json.get("heroRecruitmentConditionCount").asText(), META_SEPARATOR_2, AbstractMeta.META_SEPARATOR_4);

        preciousItemRecords = MetaUtils.parseIntegerList(json.get("preciousItemRecord").asText(), META_SEPARATOR_2);

        warFeverBuffLevelMap = new HashMap<>();
        IntKeyStringValue[] warFeverArr = MetaUtils.parseObjects(json.path("warFeverBuffLevel").asText(), IntKeyStringValue.class);
        for (IntKeyStringValue warFever : warFeverArr) {
            warFeverBuffLevelMap.put(warFever.getId(), warFever.getCount());
        }

        String[] forceVersionArray = MetaUtils.parse(json.get("forceUpdateVersion").asText(), META_SEPARATOR_2);
        if (forceVersionArray != null) {
            forceUpdateVersionMap.clear();
            for (String versionValue : forceVersionArray) {
                String[] versionInfos = MetaUtils.parse(versionValue, AbstractMeta.META_SEPARATOR_4);
                if (versionInfos != null && versionInfos.length == 3) {
                    forceUpdateVersionMap.put(versionInfos[0], new String[]{versionInfos[1], versionInfos[2]});
                }
            }
        }

        // communityGotoReward : 100|200,1002200|1002201
        // Discord钻石奖励数额|facebook钻石奖励数额,discord奖励邮件ID|facebook奖励
        String[] communityGotoReward = StringUtils.split(json.path("communityGotoReward").asText(), AbstractMeta.META_SEPARATOR_1);
        this.communityGotoRewards = MetaUtils.parse(communityGotoReward[1], META_SEPARATOR_2);

        propertyParams40122 = MetaUtils.parseInts(json.path("propertyParam40122").asText(), META_SEPARATOR_2);

        this.rookieGiftDurationTime = json.path("newPlayerMustBuyTime").asInt();
        this.rookieGiftDailyFreeReward = json.path("newPlayerMustBuyFreeSupply").asText();

        this.rssBiOpenServers = new HashSet<>();
        JsonNode rssBiOpenServer = json.get("rssBiOpenServer");
        if (rssBiOpenServer != null) {
            String[] strings = MetaUtils.parse(rssBiOpenServer.asText(), META_SEPARATOR_2);
            if (JavaUtils.bool(strings)) {
                for (String str : strings) {
                    this.rssBiOpenServers.add(Integer.parseInt(str));
                }
            }
        }

        exploreEventCommonResetTimeArr = MetaUtils.parseInts(json.path("exploreEventCommonResetTime").asText(), META_SEPARATOR_2);
        loginRewardMetaIdArr = MetaUtils.parseStringList(json.path("SevenDaysReward").asText(), META_SEPARATOR_2);

        heroResetItemInfo = MetaUtils.parseObjectNew(json.path("resetHeroItem").asText(), StringKeyIntValue.class);

        freeBuyLastTimesMap = MetaUtils.parseIntMap(json.path("freeBuyLastTimes").asText(), AbstractMeta.META_SEPARATOR_1, META_SEPARATOR_2);

        // 退盟限制配置
        String[] exitClanConfig = MetaUtils.parse(json.path("ExitClanCD").asText(), META_SEPARATOR_2);
        if (exitClanConfig != null && exitClanConfig.length == 2) {
            this.exitClanLimitLevel = Integer.parseInt(exitClanConfig[0]);
            this.exitClanCD = Long.parseLong(exitClanConfig[1]);
        }
        // 赛季迁服退盟限制配置
        String[] exitClanMigrationConfig = MetaUtils.parse(json.path("ExitClanCDDuringMigration").asText(), META_SEPARATOR_2);
        if (exitClanMigrationConfig != null && exitClanMigrationConfig.length == 2) {
            this.exitClanLimitLevelOfMigration = Integer.parseInt(exitClanMigrationConfig[0]);
            this.exitClanCDOfMigration = Long.parseLong(exitClanMigrationConfig[1]);
        }

        this.energyRefreshArr = MetaUtils.parseInts(json.path("energyRefresh").asText(), AbstractMeta.META_SEPARATOR_2);
        if (this.energyRefreshArr != null && this.energyRefreshArr.length < 1) {
            throw new BizException("energyRefresh length not eq 1","meta",SettingConfig.class.getName());
        }

        if (json.has("afkrewardmax")) {
            expeditionHangupMaxSeconds = MetaUtils.tryParseInt(json.path("afkrewardmax").asText());
        }

        // 暴风雪配置
        {
            blizzardTriggerCondition = new ArrayList<>();
            String[] conditions = MetaUtils.parse(json.path("unlockBlizzard").asText(), META_SEPARATOR_1);
            if (conditions != null) {
                for (String cond : conditions) {
                    blizzardTriggerCondition.add(MetaUtils.parseInts(cond, META_SEPARATOR_2));
                }
            }
        }
        // 暴风抵御雪配置
        {
            blizzardResistTriggerCondition = new ArrayList<>();
            String[] conditions = MetaUtils.parse(json.path("unlockBlizzarding").asText(), META_SEPARATOR_1);
            if (conditions != null) {
                for (String cond : conditions) {
                    blizzardResistTriggerCondition.add(MetaUtils.parseInts(cond, META_SEPARATOR_2));
                }
            }
        }
        // 山贼来袭配置
        {
            robberTriggerCondition = new ArrayList<>();
            String[] robberConditions = MetaUtils.parse(json.path("unlockRobber").asText(), META_SEPARATOR_1);
            if (robberConditions != null) {
                for (String cond : robberConditions) {
                    robberTriggerCondition.add(MetaUtils.parseInts(cond, META_SEPARATOR_2));
                }
            }
        }

        fightSoldierDamageRatio = MetaUtils.parseDoubles(json.path("fightSoldierDamageRatio").asText(), META_SEPARATOR_2);
        fightAttackSoldierExponentRatio = MetaUtils.parseDoubles(json.path("fightAttackSoldierExponentRatio").asText(), META_SEPARATOR_2);
        fightDefenderSoldierExponentRatio = MetaUtils.parseDoubles(json.path("fightDefenderSoldierExponentRatio").asText(), META_SEPARATOR_2);
        slgDeadConvertRatio = MetaUtils.parseDoubles(json.path("slgDeadConvert").asText(), META_SEPARATOR_2);

        this.autoHuntingMonthCardType = MetaUtils.parse(json.path("autoHuntingMonthCardType").asText(), META_SEPARATOR_2);

        // 私域拉新配置
        {
            siyulinkNewUserRewardMap = new HashMap<>();
            MetaUtils.parseStringList(json.path("siyulink").asText(), META_SEPARATOR_1).stream().forEach(each -> {
                List<String> split = MetaUtils.parseStringList(each, META_SEPARATOR_2);
                if (split.size() == 2) {
                    siyulinkNewUserRewardMap.put(split.get(0), split.get(1));
                }
            });
        }

        enlistCapacityRatio = MetaUtils.parseDoubles(json.path("reservecampnum").asText(), META_SEPARATOR_2);
        warChestRecover = new ArrayList<>();
        var warChestRecoverArr = MetaUtils.parseStringList(json.path("reservecampresourcenum").asText(), META_SEPARATOR_1);
        for (var str : warChestRecoverArr) {
            var warChestRecoverCurrency = MetaUtils.parseInts(str, META_SEPARATOR_2);
            warChestRecover.add(warChestRecoverCurrency);
        }

        {
            militaryHospitalRemindMail = new ArrayList<>();
            var militaryHospitalRemindMailStr = json.path("militaryHospitalRemindMail").asText();
            if (!StringUtils.isBlank(militaryHospitalRemindMailStr)) {
                var militaryHospitalRemindMailAttr = MetaUtils.parseFloats(militaryHospitalRemindMailStr,
                        META_SEPARATOR_2);
                if (militaryHospitalRemindMailAttr != null) {
                    for (var percent : militaryHospitalRemindMailAttr) {
                        militaryHospitalRemindMail.add(percent);
                    }
                }
                militaryHospitalRemindMail.sort(Comparator.reverseOrder());
            }
        }
        //天子皇权
        {
            officerPositionReservationListLimitMap = MetaUtils.parseStringIntegerMap(json.path("officerPositionReservationListLimit").asText(), AbstractMeta.META_SEPARATOR_1, META_SEPARATOR_2);
            oldServerPrestigeMap = MetaUtils.parseIntMap(json.path("oldServerPrestige").asText(), AbstractMeta.META_SEPARATOR_1, META_SEPARATOR_2);
            reservationTimeLimitArray = MetaUtils.parseInts(json.path("reservationTimeLimit").asText(), META_SEPARATOR_3);
        }
        // 排行榜称号配置解析
        rankFashion = new HashMap<Integer, String>();
        var rankTitleStringList = MetaUtils.parseStringList(json.path("personalRankingFashion").asText(), META_SEPARATOR_1);
        for (var rankTitleString : rankTitleStringList) {
            List<Integer> paire = MetaUtils.parseIntegerList(rankTitleString, META_SEPARATOR_2);
            if (paire.size() == 2) {
                rankFashion.put(paire.get(0), Integer.toString(paire.get(1))); // 道具id 是字符串类型
            }
        }
        // 排行榜称号发放周期解析
        personalRankingSettlementTime = MetaUtils.tryParseInt(json.path("personalRankingSettlementTime").asText());

        heroBondSLGCampRestraintMap = MetaUtils.parseIntMap(heroBondSLGCampRestraint, META_SEPARATOR_1, META_SEPARATOR_2);

        peopleNewUserFrdPoint = new HashMap<>();
        String peopleNewUserStr = json.path("peopleNewUser").asText();
        if (!StringUtils.isEmpty(peopleNewUserStr)) {
            List<String> peopleNewUserList = MetaUtils.parseStringList(peopleNewUserStr, META_SEPARATOR_3);
            for (String peopleNewUser : peopleNewUserList) {
                List<Integer> peopleNewUserAttr = MetaUtils.parseIntegerList(peopleNewUser, META_SEPARATOR_2);
                if (peopleNewUserAttr != null && peopleNewUserAttr.size() == 2) {
                    peopleNewUserFrdPoint.put(peopleNewUserAttr.get(0), peopleNewUserAttr.get(1));
                }
            }
        }
        peopleOldUserFrdPoint = new HashMap<>();
        String peopleOldUserStr = json.path("peopleOldUser").asText();
        if (!StringUtils.isEmpty(peopleOldUserStr)) {
            List<String> peopleOldUserList = MetaUtils.parseStringList(peopleOldUserStr, META_SEPARATOR_3);
            for (String peopleOldUser : peopleOldUserList) {
                List<Integer> peopleOldUserAttr = MetaUtils.parseIntegerList(peopleOldUser, META_SEPARATOR_2);
                if (peopleOldUserAttr != null && peopleOldUserAttr.size() == 2) {
                    peopleOldUserFrdPoint.put(peopleOldUserAttr.get(0), peopleOldUserAttr.get(1));
                }
            }
        }

        this.warDayActivityIds = MetaUtils.parseStringList(json.path("warDayActivityIds").asText(), META_SEPARATOR_1);
        this.horseMatingBigWinInts = MetaUtils.parseInts(json.path("horseMatingBigWin").asText(), META_SEPARATOR_2);
//        plunderLimit.put(0.0, 1.0);
//        String plunderLimitMid = json.path("plunderLimitMid").asText();
//        if (JavaUtils.bool(plunderLimitMid)) {
//            var limit = MetaUtils.parseDoubles(plunderLimitMid, META_SEPARATOR_2);
//            plunderLimit.put(limit[0] + 1.0f, limit[1]);
//        }
//        String plunderLimitLow = json.path("plunderLimitLow").asText();
//        if (JavaUtils.bool(plunderLimitLow)) {
//            var limit = MetaUtils.parseDoubles(plunderLimitLow, META_SEPARATOR_2);
//            plunderLimit.put(limit[0] + 1.0f, limit[1]);
//        }

        // 小人互动
        this.peopleInteractionLimit = MetaUtils.tryParseInt(json.path("peopleInteractionLimit").asText());
        this.peopleChatLimit = MetaUtils.tryParseInt(json.path("peopleChatLimit").asText());


        invitationCodeReward = MetaUtils.parseInt2d(json.path("Invitation001").asText(), META_SEPARATOR_1, META_SEPARATOR_2);
        invitationCodeModifyCost = MetaUtils.parseInts(json.path("Invitation002").asText(), META_SEPARATOR_2);
        invitationCodeModifyCD = json.path("Invitation003").asInt();
        invitationCodeLvLimit = json.path("Invitation004").asInt();
        invitationCodeExpireDay = json.path("Invitation005").asInt();

        serverGroup = json.path("serverGroup").asInt();
        nearServerRankOpen = json.path("nearserverRankOpen").asInt();

        horseChangeNameRule = MetaUtils.parseInts(json.path("horseNameLength").asText(), META_SEPARATOR_2);
        String anchorRewardsLimitedTime = json.path("anchorRewardsLimitedTime").asText();
        if (JavaUtils.bool(anchorRewardsLimitedTime)) {
            var data = MetaUtils.parse(anchorRewardsLimitedTime, META_SEPARATOR_1);
            specialSeasonRewardServerType = ServerType.valueOf(data[0]);
            specialSeasonRewardStart = TimeUtil.parseStr2MillTime(data[1], "yyyy-MM-dd HH:mm:ss");
            specialSeasonRewardEnd = TimeUtil.parseStr2MillTime(data[2], "yyyy-MM-dd HH:mm:ss");
        }

        // 拆弹奖励参数配置
        this.defusalRewardConfig = DefusalRewardConfig.parse(json.path("defusalRewardParams").asText());
    }

    public String getRankFashionId(int rankTitle) {
        return rankFashion.getOrDefault(rankTitle, "");
    }

    public boolean isRankFashionId(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return false;
        }
        for (Map.Entry<Integer, String> entry : rankFashion.entrySet()) {
            if (itemId.equals(entry.getValue())) {
                return true;
            }
        }
        return false;
    }

    public int getRankFashionPeriod() {
        return personalRankingSettlementTime;
    }

    public List<Long> getEnergyRefreshTime() {
        long[] result = {0, 0};
        long time = Time.now();
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(time);
        List<Long> refreshTimes = new ArrayList<>();

        // 添加前一天的刷新节点
        cal.add(Calendar.DAY_OF_YEAR, -1);
        for (var hour : this.energyRefreshArr) {
            cal.set(Calendar.HOUR_OF_DAY, hour);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            refreshTimes.add(cal.getTimeInMillis());
        }

        // 添加当天的刷新节点
        cal.add(Calendar.DAY_OF_YEAR, 1);
        for (var hour : this.energyRefreshArr) {
            cal.set(Calendar.HOUR_OF_DAY, hour);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            refreshTimes.add(cal.getTimeInMillis());
        }

        // 添加下一天的刷新节点
        cal.add(Calendar.DAY_OF_YEAR, 1);
        for (var hour : this.energyRefreshArr) {
            cal.set(Calendar.HOUR_OF_DAY, hour);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            refreshTimes.add(cal.getTimeInMillis());
        }

        return refreshTimes;
    }

    public List<Long> getExploreEventRefreshTimes() {
        long time = Time.now();
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(time);
        List<Long> refreshTimes = new ArrayList<>();

        // 添加前一天的刷新节点
        cal.add(Calendar.DAY_OF_YEAR, -1);
        for (var hour : this.exploreEventCommonResetTimeArr) {
            cal.set(Calendar.HOUR_OF_DAY, hour);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            refreshTimes.add(cal.getTimeInMillis());
        }

        // 添加当天的刷新节点
        cal.add(Calendar.DAY_OF_YEAR, 1);
        for (var hour : this.exploreEventCommonResetTimeArr) {
            cal.set(Calendar.HOUR_OF_DAY, hour);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            refreshTimes.add(cal.getTimeInMillis());
        }

        // 添加下一天的刷新节点
        cal.add(Calendar.DAY_OF_YEAR, 1);
        for (var hour : this.exploreEventCommonResetTimeArr) {
            cal.set(Calendar.HOUR_OF_DAY, hour);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            refreshTimes.add(cal.getTimeInMillis());
        }

        return refreshTimes;
    }

    public String getFirstRechargePlan(long userId, int serverId, long createTime, ServerType serverType) {
        var times = firstRechargeABTestTime.get(serverType.name());
        if (times == null || createTime < times[0] || createTime > times[1]) {
            return "Z";
        }
        var serverIds = firstRechargeABTestServer.get(serverType.name());
        if (serverIds == null || !serverIds.contains(serverId)) {
            return "Z";
        }

        if (userId % 2 != 0) {
            return "A";
        } else {
            return "B";
        }
    }

    public String getFirstRechargeConfig(long userId, int serverId, long createTime, ServerType serverType) {
        var plan = getFirstRechargePlan(userId, serverId, createTime, serverType);
        return firstRechargePlans.get(plan);
    }

    public String getFirstRechargeLibaoId(String plan) {
        return firstRechargeLibaoId.get(plan);
    }

    public List<SimpleItem> getFirstRechargeRewards(String plan) {
        return firstRechargeRewards.get(plan);
    }

    public List<SimpleItem> getFirstRechargeRewardsSecondDay(String plan) {
        return firstRechargeRewardsSecondDay.get(plan);
    }

    public String getOneEventProbabilityMetaIdByRandom() {
        int pointer = RandomUtils.randWeight(eventProbWights, eventProbPointer);
        String metaId = eventProbValues[pointer];
        return metaId;
    }

    public int getInitialUpperLimit(Currency currency) {
        switch (currency) {
            case FOOD:
                return foodInitialUpperLimit;
            case IRON:
                return ironInitialUpperLimit;
            case WATER:
                return waterInitialUpperLimit;
            case WOOD:
                return woodInitialUpperLimit;
            default:
                return 0;
        }
    }

    public String[] getForceUpdateVersion(String appKey) {
        return forceUpdateVersionMap.get(appKey);
    }

    public boolean getTickCalcOpen() {
        return ServerConfigManager.getInstance().getGameConfig().isTickCalcOpen();
    }

    public boolean rssBiOpen(int serverId) {
        if (rssBiOpenServers.contains(-1)) {
            return true;
        }
        return rssBiOpenServers.contains(serverId);
    }

    public static SettingConfig getSetting() {
        return Application.getBean(ConfigServiceImpl.class).getConfig(SettingConfig.class);
    }


    public int getJoinPveRallyTimeLimit() {
        return joinPveRallyTimeLimit * 1000;
    }
}
