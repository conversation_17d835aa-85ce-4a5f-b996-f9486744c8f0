package com.lc.billion.icefire.game.biz.config.setting;

import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import org.apache.commons.lang3.StringUtils;

import static com.lc.billion.icefire.core.config.model.AbstractMeta.META_SEPARATOR_2;
import static com.lc.billion.icefire.core.config.model.AbstractMeta.META_SEPARATOR_3;

/**
 * 拆弹奖励配置结构体
 * 配置格式：消耗物品ID|数量;总需求次数;奖励物品ID|数量
 * 示例：10460001|1;5;2201|1
 *
 * @param consumeItem            消耗物品
 * @param totalSubmissionsRequired 总需求次数
 * @param rewardItem            奖励物品
 */
public record DefusalRewardConfig(
        SimpleItem consumeItem,
        int totalSubmissionsRequired,
        SimpleItem rewardItem
) {
    /**
     * 解析配置字符串创建DefusalRewardConfig实例
     *
     * @param configString 配置字符串，格式：消耗物品ID|数量;总需求次数;奖励物品ID|数量
     * @return DefusalRewardConfig实例，解析失败返回null
     */
    public static DefusalRewardConfig parse(String configString) {
        if (StringUtils.isBlank(configString)) {
            return null;
        }
        
        try {
            var parts = MetaUtils.parseStringList(configString, META_SEPARATOR_3); // 使用 ; 分割
            if (parts.size() != 3) {
                return null;
            }
            
            // 解析消耗物品
            var consumeParts = MetaUtils.parseStringList(parts.getFirst(), META_SEPARATOR_2); // 使用 | 分割
            if (consumeParts.size() != 2) {
                return null;
            }
            var consumeItemId = consumeParts.getFirst();
            var consumeItemQuantity = Integer.parseInt(consumeParts.getLast());
            
            // 解析总需求次数
            var totalSubmissionsRequired = Integer.parseInt(parts.get(1));
            
            // 解析奖励物品
            var rewardParts = MetaUtils.parseStringList(parts.getLast(),META_SEPARATOR_2); // 使用 | 分割
            if (rewardParts.size() != 2) {
                return null;
            }
            var rewardItemId = rewardParts.getFirst();
            int rewardItemQuantity = Integer.parseInt(rewardParts.getLast());
            
            return new DefusalRewardConfig(new SimpleItem(consumeItemId, consumeItemQuantity),
                                         totalSubmissionsRequired, new SimpleItem(rewardItemId, rewardItemQuantity));
        } catch (Exception e) {
            return null;
        }
    }
} 