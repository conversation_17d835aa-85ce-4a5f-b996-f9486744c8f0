package com.lc.billion.icefire.game.biz.config.kvk;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaList;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.Application;
import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.game.biz.model.scene.IRoundRefreshMeta;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Config(name = "KvkNpcFresh", metaClass = KvkNpcFreshConfig.KvkNpcFreshMeta.class)
public class KvkNpcFreshConfig {
	public static Logger logger = LoggerFactory.getLogger(KvkNpcFreshConfig.class);

	@MetaMap
	private final Map<String, KvkNpcFreshMeta> metaMap = new HashMap<>();

	@MetaList
	@Getter
	private List<KvkNpcFreshMeta> metaList;

	public void init(List<KvkNpcFreshMeta> list) {
		if (Application.getServerType() != ServerType.KVK_SEASON) {
			logger.debug("非kvk赛季服启动，服务器id{}, 无需加载{}", Application.getServerId(), getClass().getSimpleName());
			return;
		}
		KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = Application.getConfigCenter().getCurrentKvkSeasonServerGroupConfig();
		int season = kvkSeasonServerGroupConfig.getSeason();// 获取赛季信息 读取对应配置
		for (KvkNpcFreshMeta metaData : list) {
			if (!metaData.getSeason().contains(season)) {
				metaMap.remove(metaData.getId());
				continue;
			}

			Map<Integer, Integer> roundMap = new HashMap<>();
			roundMap.put(1, metaData.getNum1());
			roundMap.put(2, metaData.getNum2());
			roundMap.put(3, metaData.getNum3());
			roundMap.put(4, metaData.getNum4());
			roundMap.put(5, metaData.getNum5());
			roundMap.put(6, metaData.getNum6());
			roundMap.put(7, metaData.getNum7());
			metaData.setRoundNumMap(roundMap);
		}
	}

	public Map<String, KvkNpcFreshMeta> getMetaMap() {
		return metaMap;
	}

	public KvkNpcFreshMeta get(String id) {
		return metaMap.get(id);
	}

	public static class KvkNpcFreshMeta extends AbstractMeta implements IRoundRefreshMeta {
		/*
		 * 赛季标记
		 */
		@JsonIgnore
		private List<Integer> season;

		/*
		 * 刷新模式
		 */
		private int refreshType;

		/*
		 * 刷新类型
		 */
		private int type;

		/*
		 * 基础刷新等级
		 */
		private float level;

		/*
		 * 成长系数
		 */
		private float grow;

		/*
		 * 刷新数量6
		 */
		private int num6;
		private int num5;
		private int num4;
		private int num3;
		private int num2;
		private int num1;
		@Getter
		private int num7;
		@JsonIgnore
		private Map<Integer, Integer> roundNumMap;

		public void init(JsonNode json) {
			season = MetaUtils.parseIntegerList(json.path("season").asText(), AbstractMeta.META_SEPARATOR_2);
		}

		public List<Integer> getSeason() {
			return season;
		}

		public int getRefreshType() {
			return refreshType;
		}

		public int getType() {
			return type;
		}

		public float getLevel() {
			return level;
		}

		public float getGrow() {
			return grow;
		}

		public int getNum6() {
			return num6;
		}

		public int getNum5() {
			return num5;
		}

		public int getNum4() {
			return num4;
		}

		public int getNum3() {
			return num3;
		}

		public int getNum2() {
			return num2;
		}

		public int getNum1() {
			return num1;
		}

		public Map<Integer, Integer> getRoundNumMap() {
			return roundNumMap;
		}

		public void setRoundNumMap(Map<Integer, Integer> roundNumMap) {
			this.roundNumMap = roundNumMap;
		}
	}
}
