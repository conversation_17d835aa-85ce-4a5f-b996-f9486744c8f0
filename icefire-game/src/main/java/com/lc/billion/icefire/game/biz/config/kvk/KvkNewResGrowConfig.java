package com.lc.billion.icefire.game.biz.config.kvk;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Config(name = "KvkNewResGrow", metaClass = KvkNewResGrowConfig.KvkNewResGrowMeta.class)
public class KvkNewResGrowConfig {
    private final Logger logger = LoggerFactory.getLogger(KvkNewResGrowConfig.class);

    @MetaMap
    private final Map<String, KvkNewResGrowMeta> metaMap = new HashMap<>();

    private final Map<Integer, KvkNewResGrowMeta> circleTypeLevelMap = new HashMap<>();

    // key : level
    private final Map<Integer, List<Integer>> refreshTimeMap = new HashMap<>();

    private final Map<Integer, Integer> usedCountMap = new HashMap<>();

    public static final int MAX_GROWTH = 100;

    public static final int MAX_LEVEL = 1000;

    public static final int MAX_CIRCLE = 100;

    public static final int ZONE_GRID_MAX = 1500;

    public void init(List<KvkNewResGrowMeta> list) {
        if (Application.getServerType() != ServerType.KVK_SEASON) {
            logger.debug("非kvk赛季服启动，服务器id{}, 无需加载{}", Application.getServerId(), getClass().getSimpleName());
            return;
        }
        for (KvkNewResGrowMeta metaData : list) {
            circleTypeLevelMap.put(metaData.getLevel() * MAX_LEVEL + metaData.getCircle() * MAX_CIRCLE + metaData.getType(), metaData);
            List<Integer> levelList = refreshTimeMap.get(metaData.getLevel());
            if (levelList == null) {
                List<Integer> refreshTime = metaData.getRefreshTime();
                levelList = new ArrayList<>(refreshTime);
                refreshTimeMap.put(metaData.getLevel(), levelList);
            }

        }
    }

    public Map<String, KvkNewResGrowMeta> getMetaMap() {
        return metaMap;
    }

    public KvkNewResGrowMeta get(String id) {
        return metaMap.get(id);
    }

    public KvkNewResGrowConfig.KvkNewResGrowMeta getByTypeAndLevelAndCircle(int circle, Currency type, Integer level) {
        int key = level * MAX_LEVEL + circle * MAX_CIRCLE + type.getId();
        if (circleTypeLevelMap.containsKey(key)) {
            return circleTypeLevelMap.get(key);
        }

        return null;
    }

    public int getNewResRefreshTime(int level) {
        int count = 0;
        count = usedCountMap.getOrDefault(level, 0);
        if (count >= refreshTimeMap.get(level).size()) {
            count = 0;
        }

        usedCountMap.put(level, count + 1);
        return refreshTimeMap.get(level).get(count);
    }

    public static class KvkNewResGrowMeta extends AbstractMeta {
        /*
         * 赛季标记
         */
        @JsonIgnore
        private List<Integer> season;

        /*
         * 基础等级
         */
        private int level;

        /*
         * 地块等级
         */
        private int circle;

        /*
         * 资源类型
         */
        private int type;

        /*
         * 刷新时间
         */
        @JsonIgnore
        private List<Integer> refreshTime;

        /*
         * 开服天数1
         */
        private final List<Double> growthList = new ArrayList<>();

        public void init(JsonNode json) {
            season = MetaUtils.parseIntegerList(json.path("season").asText(), AbstractMeta.META_SEPARATOR_2);
            refreshTime = MetaUtils.parseIntegerList(json.path("refreshTime").asText(), AbstractMeta.META_SEPARATOR_2);

            for (int i = 1; i < MAX_GROWTH; i++) {
                String weightRaw = json.path("growth" + i).asText();
                if (!StringUtils.isEmpty(weightRaw)) {
                    growthList.add(Double.parseDouble(weightRaw));
                }
            }
        }

        public List<Integer> getSeason() {
            return season;
        }

        public int getLevel() {
            return level;
        }

        public int getCircle() {
            return circle;
        }

        public int getType() {
            return type;
        }

        public List<Integer> getRefreshTime() {
            return refreshTime;
        }

        public List<Double> getGrowthList() {
            return growthList;
        }

        public double getGrowthRate(int days) {
            if (days <= growthList.size()) {
                return growthList.get(days - 1) * 1d / 10000;
            }

            return growthList.get(growthList.size() - 1) * 1d / 10000;
        }
    }
}
