package com.lc.billion.icefire.game.biz.config.kvk;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.Application;
import com.fasterxml.jackson.databind.JsonNode;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@Config(name = "KvkNpcLevel", metaClass = KvkNpcLevelConfig.KvkNpcLevelMeta.class)
public class KvkNpcLevelConfig {
	private static final Logger logger = LoggerFactory.getLogger(KvkNpcLevelConfig.class);

	@MetaMap
	private final Map<String, KvkNpcLevelMeta> metaMap = new HashMap<>();

	private final TreeMap<Integer, Integer> dayLevelMap = new TreeMap<>();

	public void init(List<KvkNpcLevelMeta> list) {
		if (Application.getServerType() != ServerType.KVK_SEASON) {
			logger.debug("非kvk赛季服启动，服务器id{}, 无需加载{}", Application.getServerId(), getClass().getSimpleName());
			return;
		}
		KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = Application.getConfigCenter().getCurrentKvkSeasonServerGroupConfig();
		int season = kvkSeasonServerGroupConfig.getSeason();// 获取赛季信息 读取对应配置
		KvkNpcLevelConfig.KvkNpcLevelMeta lastMeta = null;
		for (KvkNpcLevelMeta metaData : list) {
			if (!metaData.getSeason().contains(season)) {
				metaMap.remove(metaData.getId());
				continue;
			}

			if (lastMeta == null) {
				lastMeta = metaData;
			}
			for (int i = lastMeta.getDay() + 1; i < metaData.getDay(); i++) {
				dayLevelMap.put(i, lastMeta.getLevel());
			}
			dayLevelMap.put(metaData.getDay(), metaData.getLevel());
			lastMeta = metaData;
		}
	}

	public Map<String, KvkNpcLevelMeta> getMetaMap() {
		return metaMap;
	}

	public KvkNpcLevelMeta get(String id) {
		return metaMap.get(id);
	}

	public TreeMap<Integer, Integer> getDayLevelMap() {
		return dayLevelMap;
	}

	public int getMaxLevelByDay(int day) {
		Integer result = this.dayLevelMap.get(day);
		if (result == null) {
			return this.dayLevelMap.lastEntry().getValue();
		}
		return result;
	}

	public static class KvkNpcLevelMeta extends AbstractMeta {
		/*
		 * 赛季标记
		 */
		@JsonIgnore
		private List<Integer> season;

		/*
		 * 天数
		 */
		private int day;

		/*
		 * 等级上限
		 */
		private int level;

		public void init(JsonNode json) {
			season = MetaUtils.parseIntegerList(json.path("season").asText(), AbstractMeta.META_SEPARATOR_2);
		}

		public List<Integer> getSeason() {
			return season;
		}

		public int getDay() {
			return day;
		}

		public int getLevel() {
			return level;
		}
	}
}
