package com.lc.billion.icefire.game.biz.config.kvk;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.Application;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import lombok.Getter;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Config(name = "KvkParcellevel", metaClass = KvkParcellevelConfig.KvkParcellevelMeta.class)
public class KvkParcellevelConfig {
    private static final Logger logger = LoggerFactory.getLogger(KvkParcellevelConfig.class);

    @MetaMap
    private final Map<String, KvkParcellevelMeta> metaMap = new HashMap<>();
    /**
     * regionId to meta map
     */
    private final Map<Integer, KvkParcellevelMeta> regionIdMap = new HashMap<>();
    // 玩家出生点地块排序下标
    private List<Integer> sortedZoneGridIndexs;
    // 出生点地块<level,id>
    private Map<Integer, List<Integer>> map;

    public void init(List<KvkParcellevelMeta> list) {
        if (Application.getServerType() != ServerType.KVK_SEASON) {
            logger.debug("非kvk赛季服启动，服务器id{}, 无需加载{}", Application.getServerId(), getClass().getSimpleName());
            return;
        }
        KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = Application.getConfigCenter().getCurrentKvkSeasonServerGroupConfig();
        for (KvkParcellevelMeta metaData : list) {

            List<KvkParcellevelConfig.KvkParcellevelMeta> tempList = new ArrayList<>();
            map = new HashMap<>();
            if (metaData.getNewPlayerOrder() > 0) {
                // 与策划约定只判断大于0的排序
                tempList.add(metaData);
            }
            map.compute(metaData.getLevel(), (k, v) -> v == null ? new ArrayList<>() : v).add(NumberUtils.toInt(metaData.getId()));
            // 按newPlayerOrder升序
            tempList.sort((m1, m2) -> {
                return m1.getNewPlayerOrder() - m2.getNewPlayerOrder();
            });
            sortedZoneGridIndexs = new ArrayList<>();
            tempList.forEach(meta -> sortedZoneGridIndexs.add(NumberUtils.toInt(meta.getId())));
            regionIdMap.put(metaData.getRegionId(), metaData);
        }
    }

    public Map<String, KvkParcellevelMeta> getMetaMap() {
        return metaMap;
    }

    public KvkParcellevelMeta get(String id) {
        return metaMap.get(id);
    }
    public KvkParcellevelMeta get(int regionId) {
        return regionIdMap.get(regionId);
    }
    public static class KvkParcellevelMeta extends AbstractMeta {

        /*
         * 地块等级
         */
        private int level;

        @Getter
        private int resNum;
        @Getter
        private int npcNum;
        /*
         *
         */
        private int newPlayerOrder;

        @Getter
        private int rallyNpcNum;

        @Getter
        private int resLevel;

        @Getter
        private int regionId;

        public void init(JsonNode json) {
        }

        public int getLevel() {
            return level;
        }

        public int getNewPlayerOrder() {
            return newPlayerOrder;
        }
    }
}
