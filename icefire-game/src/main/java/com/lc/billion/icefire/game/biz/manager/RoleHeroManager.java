package com.lc.billion.icefire.game.biz.manager;

import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.*;
import com.lc.billion.icefire.game.biz.config.HerosConfig.HerosMeta;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.PlayerHeroDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleEquipDao;
import com.lc.billion.icefire.game.biz.manager.prop.HeroCalcPropManager;
import com.lc.billion.icefire.game.biz.model.equip.Equip;
import com.lc.billion.icefire.game.biz.model.equip.RoleEquip;
import com.lc.billion.icefire.game.biz.model.fightpower.FightPowerType;
import com.lc.billion.icefire.game.biz.model.hero.Hero;
import com.lc.billion.icefire.game.biz.model.hero.HeroSimpleInfo;
import com.lc.billion.icefire.game.biz.model.hero.RoleHeroPower;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroConstants;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Component
public class RoleHeroManager extends AbstractRoleManager {
	@Autowired
	private PlayerHeroDao heroDao;

	@Autowired
	protected ConfigServiceImpl configService;
	@Autowired
	private ServiceDependency srvDep;
	@Autowired
	private HeroServiceImpl heroService;

	@Autowired
	private HeroCalcPropManager heroCalcPropManager;
	@Autowired
	private RoleEquipDao equipDao;
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private RankServiceImpl rankService;

	private final Map<Long, HeroSimpleInfo> roleFirstHeroMap = new ConcurrentHashMap<>();

	private final Map<Long, RoleHeroPower> roleHeadHeroPower = new ConcurrentHashMap<>();

	@Override
	public void onCreateRole(Role role, Player player) {

	}

	@Override
	public void afterCreateRole(Role role, Player player) {
		SettingConfig settingConfig = configService.getConfig(SettingConfig.class);
		HerosConfig config = srvDep.getConfigService().getConfig(HerosConfig.class);
		String[] initHero = settingConfig.getInitHeros();

		if (null != initHero) {
			for (String heroId : initHero) {
				Hero hero = heroDao.getPlayerHero(role.getId(), heroId);
				if (null == hero) {
					HerosMeta meta = config.getById(heroId);
					if (meta == null) {
						continue;
					}
					hero = heroService.createHero(meta, role);
					// 刷新英雄属性
					heroCalcPropManager.updateProps(hero);
					// BILog
					srvDep.getBiLogUtil().heroAcquisition(role, hero.getPersistKey() + "", 1, heroId, "changeCamp");
				}
			}
		}
	}

	@Override
	protected void onCreateRoleFailed(Role role) {
		for (Hero hero : heroDao.getPlayerHeros(role.getId()).values()) {
			heroDao.delete(hero);
		}
	}

	@Override
	public void beforeLogin(Role role) {

	}

	@Override
	public void afterLogin(Role role) {
		// RoleRecord roleRecord = recordManager.getRoleRecord(role.getId());
		// if (roleRecord.getGiveHeroTime() == 0 || roleRecord.isShow()) {
		// return;
		// }
		// pushGiveHeroMessage(role, roleRecord.getHeroMetaId(), false);
		// if (roleRecord.getHeroMetaId() != null) {
		// return;
		// }
		// startGiveHeroTick(role, roleRecord.getGiveHeroTime());

	}

	public void save(Hero hero) {
		heroDao.save(hero);
	}

	public Hero findById(Long heroId) {
		return heroDao.findById(heroId);
	}

	public Collection<Hero> getHeros(Long roleId) {
		return heroDao.getPlayerHeros(roleId).values();
	}

	public Hero getHeroByHeroId(Long roleId, String heroId) {
		if (heroDao.getPlayerHeros(roleId) != null) {
			return heroDao.getPlayerHeros(roleId).get(heroId);
		}
		return null;
	}

	public ConfigServiceImpl getConfigService() {
		return configService;
	}

	public void setConfigService(ConfigServiceImpl configService) {
		this.configService = configService;
	}

	/**
	 * gm测试用
	 */
	public void delHero(Role role, String heroMetaId) {
		Hero hero = getHeroByHeroId(role.getId(), heroMetaId);
		if (hero == null) {
			return;
		}
		heroDao.delete(hero);
	}

	public void delAllHero(Role role) {
		Collection<Hero> heros = getHeros(role.getId());
		ArrayList<Hero> heroes = new ArrayList<>(heros);
		for (Hero hero : heroes) {
			heroDao.delete(hero);
		}
	}

	/**
	 * 更新英雄身上的属性 在初始化、影响属性变化之后调用~ 影响因素： 1.等级 2.星级 3.装备
	 *
	 * @param hero
	 */
	public void initHeroProperties(Hero hero) {
		HerosConfig config = srvDep.getConfigService().getConfig(HerosConfig.class);
		HerosMeta herosMeta = config.getById(hero.getMetaId());
		if (herosMeta == null) {
			ErrorLogUtil.errorLog("initHeroProperties error, heroes is null", "heroMetaId",hero.getMetaId());
			return;
		}

		int strength, will, patience, vegetables, fuel, wood, steel, gold, building, research, training, treatment, explore;
		int heroStar = hero.getStar();
		if (!config.heroAttributeCheck(herosMeta, heroStar)) {
			return;
		}
	}

	public double lordSkillAddition(Role role, Hero hero) {
		if (hero == null) {
			return 0;
		}
		var herosConf = srvDep.getConfigService().getConfig(HerosConfig.class);
		HerosMeta heroMeta = herosConf.getById(hero.getMetaId());
		return lordSkillAddition(role, heroMeta);
	}
	/**
	 * 获取主公技能属性加成
	 * 种族 1-魏 2-蜀 3-吴 4-群
	 * @param role
	 * @param herosMeta
	 * @return
	 */
	public double lordSkillAddition(Role role, HerosMeta herosMeta){
		double result = 0.0;
		if (role == null){
			return result;
		}
		// 判断是否存在可用的技能
		switch (herosMeta.getRace()) {
		case WEI:
			result += role.getNumberProps().getDouble(Prop.LEADER_CAOCAO_EXP_SKILL_ADD_RATIO);
			result += role.getNumberProps().getDouble(Prop.LEADER_WEI_KINGDOM_EXP_SKILL_ADD_RATIO);
			break;
		case SHU:
			result += role.getNumberProps().getDouble(Prop.LEADER_LIUBEI_EXP_SKILL_ADD_RATIO);
			result += role.getNumberProps().getDouble(Prop.LEADER_SHU_KINGDOM_EXP_SKILL_ADD_RATIO);
			break;
		case WU:
			result += role.getNumberProps().getDouble(Prop.LEADER_SUNQUAN_EXP_SKILL_ADD_RATIO);
			result += role.getNumberProps().getDouble(Prop.LEADER_WU_KINGDOM_EXP_SKILL_ADD_RATIO);
			break;
		}
		return result;
	}

	/**
	 * 获取主公技能属性加成
	 * 种族 1-魏 2-蜀 3-吴 4-群
	 * @param role
	 * @param herosMeta
	 * @return
	 */
	public double lordSkillSlgAddition(Role role, HerosMeta herosMeta){
		double result = 0.0;
		if (role == null){
			return result;
		}
		// 判断是否存在可用的技能
		switch (herosMeta.getRace()) {
			case WEI:
				result += role.getNumberProps().getDouble(Prop.LEADER_CAOCAO_SLG_SKILL_ADD_RATIO);
				result += role.getNumberProps().getDouble(Prop.LEADER_WEI_KINGDOM_SLG_SKILL_ADD_RATIO);
				break;
			case SHU:
				result += role.getNumberProps().getDouble(Prop.LEADER_LIUBEI_SLG_SKILL_ADD_RATIO);
				result += role.getNumberProps().getDouble(Prop.LEADER_SHU_KINGDOM_SLG_SKILL_ADD_RATIO);
				break;
			case WU:
				result += role.getNumberProps().getDouble(Prop.LEADER_SUNQUAN_SLG_SKILL_ADD_RATIO);
				result += role.getNumberProps().getDouble(Prop.LEADER_WU_KINGDOM_SLG_SKILL_ADD_RATIO);
				break;
		}
		return result;
	}

	/**
	 * 获取单个英雄的战力值
	 *
	 * @param hero
	 * @return
	 */
	public long getOneHeroPower(Role role, Hero hero, boolean includeEquip) {
		if (hero == null) {
			ErrorLogUtil.errorLog("[getOneHeroPower] get hero param is null");
			return 0;
		}
		var herosConf = srvDep.getConfigService().getConfig(HerosConfig.class);
		HerosMeta heroMeta = herosConf.getById(hero.getMetaId());
		if (heroMeta == null) {
			ErrorLogUtil.errorLog(" hero get HerosMeta is null ! please check meta", "roleId",hero.getRoleId(), "heroMetaId",hero.getMetaId());
			return 0;
		}

		double totalPower = 0;

		// 增加基础战斗力
		var heroProperties = this.srvDep.getConfigService().getConfig(HeroPropertiesConfig.class);
		var propertiesMeta = heroProperties.getHeroPropertiesMeta(heroMeta.getId());
		if (propertiesMeta != null) {
			// 查看是否有主公属性加成
			double lordSkillAddition = this.lordSkillAddition(role, heroMeta);
			double levePower = this.srvDep.getConfigService().getConfig(HeroUpgradeConfig.class).getPowerByLevel(hero.getLevel());
//			levePower *= 1 + lordSkillAddition;
			totalPower += propertiesMeta.getLevelPower() * levePower;

			double starPower = this.srvDep.getConfigService().getConfig(HeroStarLevelConfig.class).getPowerByQualityAndStar(heroMeta.getQuality(), hero.getStar());
//			starPower *= 1 + lordSkillAddition;
			totalPower += propertiesMeta.getStarPower() * starPower;
		}

		// 技能计算
		var btlSkills = herosConf.getBtlSkillsByLevel(hero.getMetaId(), hero.getLevel(), hero.getStar());
		var expeditionSkills = herosConf.getExpeditionSkillsByLevel(hero.getMetaId(), hero.getLevel(), hero.getStar());

		for (var skill : btlSkills) {
			totalPower += this.srvDep.getConfigService().getConfig(BtlSkillConfig.class).getMetaById(skill).getPower();
		}

		for (var expedition : expeditionSkills) {
			var cfg = this.srvDep.getConfigService().getConfig(BtlSLGSkillConfig.class).getMetaById(expedition);
			if (cfg != null) {
				totalPower += cfg.getPower();
			}
		}
		int innerSkill = herosConf.getInternalAffairsSkillByLevel(hero.getMetaId(), hero.getLevel(), hero.getStar());
		if (innerSkill > 0) {
			var cfg = this.srvDep.getConfigService().getConfig(BtlSLGSkillConfig.class).getMetaById(innerSkill);
			if (cfg != null) {
				totalPower += cfg.getPower();
			}
		}

		int lordSkill = herosConf.getLordSkillByLevel(hero.getMetaId(), hero.getLevel(), hero.getStar());
		if (lordSkill > 0) {
			var cfg = this.srvDep.getConfigService().getConfig(BtlSLGSkillConfig.class).getMetaById(lordSkill);
			if (cfg != null) {
				totalPower += cfg.getPower();
			}
		}

		if (includeEquip) {
			// 装备战力
			totalPower += getHeroEquipPower(hero);
		}

		// 专属装备战斗力
		if (hero.getExclusiveLevel() > 0) {
			var upgradeMeta = srvDep.getConfigService().getConfig(ExclusiveEquipUpgradeConfig.class).getMetaByLevel(hero.getExclusiveLevel());
			var meta = srvDep.getConfigService().getConfig(ExclusiveEquipConfig.class).getMeta(heroMeta.getExclusiveEquipId());
			totalPower += upgradeMeta.getPower() * meta.getPower();
		}

		return (long) Math.ceil(totalPower);
	}

	/**
	 *
	 * @param hero
	 * @return
	 */
	public long getHeroEquipPower(Hero hero) {
		// 装备战力
		double equipPower = 0;
		RoleEquip roleEquip = equipDao.findById(hero.getRoleId());
		if (roleEquip == null) {
			return 0L;
		}
		List<Long> equipIds = hero.getEquipIds();
		for (Long id : equipIds) {
			Equip equip = roleEquip.getEquipById(id);
			double ePower = 0;
			if (equip != null) {
				var meta = srvDep.getConfigService().getConfig(EquipmentConfig.class).getMetaById(equip.getMetaId());
				if (meta != null) {
					ePower += meta.getPower() * srvDep.getConfigService().getConfig(EquipmentUpgradeConfig.class).getMetaByQualityAndLevel(meta.getQuality(), equip.getLevel()).getPower();
				}
				if (equip.getForge() > 0) {
					var forgeMeta = srvDep.getConfigService().getConfig(EquipmentForgeConfig.class).getMetaByLevel(equip.getForge());
					ePower *= forgeMeta.getPower();
				}
				equipPower += Math.ceil(ePower);
			}
		}
		return (long) equipPower;
	}

	public Long getEquipPower(Equip equip) {
		double ePower = 0;
		if (equip != null) {
			var meta = srvDep.getConfigService().getConfig(EquipmentConfig.class).getMetaById(equip.getMetaId());
			if (meta != null) {
				ePower += meta.getPower() * srvDep.getConfigService().getConfig(EquipmentUpgradeConfig.class).getMetaByQualityAndLevel(meta.getQuality(), equip.getLevel()).getPower();
			}
			if (equip.getForge() > 0) {
				var forgeMeta = srvDep.getConfigService().getConfig(EquipmentForgeConfig.class).getMetaByLevel(equip.getForge());
				ePower *= forgeMeta.getPower();
			}
		}
		return (long) ePower;
	}

	public void clearRoleFirstHeroMap() {
		roleFirstHeroMap.clear();
	}

	public Map<Long, HeroSimpleInfo> getRoleFirstHeroMap() {
		return roleFirstHeroMap;
	}

	public List<String> getHeroMetaIds(int orangeCount, int purpleCount){
		List<String> resultList = new ArrayList<>();
		if(orangeCount > 0){
			List<String> orangeList = srvDep.getConfigService().getConfig(HerosConfig.class).getHeroMetaIdsByQuality(5);
			List<String> randomList = RandomUtils.randomItemList(orangeList, orangeCount);
			resultList.addAll(randomList);
		}

		if(purpleCount > 0){
			List<String> purpleList = srvDep.getConfigService().getConfig(HerosConfig.class).getHeroMetaIdsByQuality(4);
			List<String> randomList = RandomUtils.randomItemList(purpleList, purpleCount);
			resultList.addAll(randomList);
		}

		return resultList;
	}

	public Collection<String> getHeroPowerList(Role role, int count){
		List<String> heroMetaIds = new ArrayList<>();
		List<HeroSimpleInfo> heroPowers = new ArrayList<>();
		Collection<Hero> heroes = getHeros(role.getId());
		if(JavaUtils.bool(heroes)){
			for(Hero hero : heroes){
				long power = getOneHeroPower(role, hero,true);
				HeroSimpleInfo heroPower = new HeroSimpleInfo(role.getRoleId(), hero.getMetaId(), hero.getLevel(), hero.getStar(), power);
				heroPowers.add(heroPower);
			}
		}

		if(JavaUtils.bool(heroPowers)){
			heroPowers.sort(new HeroSimpleInfo.PowerComparator());
			for (HeroSimpleInfo heroPower : heroPowers) {
				heroMetaIds.add(heroPower.getMetaId());
				if(heroMetaIds.size() >= count){
					return heroMetaIds;
				}
			}
		}

		return heroMetaIds;
	}

	/**
	 *  刷新活跃玩家英雄数据排序
	 */
	public int refreshActiveRoleHeadHero(boolean updateRank){
		long startTime = System.currentTimeMillis();
		Collection<Role> roles = roleDao.findAll();
		int count = 0;
		for (Role role : roles) {
			Collection<Hero> roleHeroes = heroDao.findByRoleId(role.getRoleId());
			if (CollectionUtils.isEmpty(roleHeroes)){
				continue;
			}
			//刷新 玩家战斗 内政类型英雄前几名信息
			refrehHeadHeroPower(role.getRoleId() , roleHeroes);

			if(updateRank){
				//战力刷新
				long fightPower = role.getFightPowerManager().get(FightPowerType.HERO);
				if(fightPower > 0){
					rankService.updateRankScore(RankType.PERSONAL_FIGHT_HERO_POWER_RANK, fightPower, role);
				}
			}
			count++;
		}
		logger.info("玩家战斗(内政)英雄排序刷新, roleSize:{}, cost:{}ms", count, System.currentTimeMillis() - startTime);
		return count;
	}

	public void refrehHeadHeroPower(Long roleId , Collection<Hero> heroes){
		if(JavaUtils.bool(heroes)){
			RoleHeroPower tempPower = roleHeadHeroPower.compute(roleId, (k, v) -> v == null ? new RoleHeroPower(roleId) : v);
			tempPower.refreshHeadHearPower(heroes , HeroConstants.HEAD_HERO_SIZE);
		}
	}

	/**
	 *
	 * @param roleId
	 * @param heroType  英雄类型
	 * @return
	 */
	public Collection<HeroSimpleInfo> getHeadHeroPower(Long roleId , int heroType){
		RoleHeroPower roleHeroPower = roleHeadHeroPower.get(roleId);
		if (roleHeroPower != null) {
			return roleHeroPower.getHeroSimpleInfosByType(heroType);
		}

		return List.of();
	}
}
