package com.lc.billion.icefire.game.biz.config.kvk;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.api.client.util.Lists;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.scene.IRoundRefreshMeta;
import com.lc.billion.icefire.game.biz.service.impl.scene.enums.BossFreshType;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Config(name = "KvkBossFresh", metaClass = KvkBossFreshConfig.KvkBossFreshMeta.class)
public class KvkBossFreshConfig {
    private static final Logger logger = LoggerFactory.getLogger(KvkBossFreshConfig.class);

    @MetaMap
    private final Map<String, KvkBossFreshMeta> metaMap = new HashMap<>();
    private final Map<BossFreshType, List<KvkBossFreshMeta>> data = new HashMap<>();

    public void init(List<KvkBossFreshMeta> list) {
        if (Application.getServerType() != ServerType.KVK_SEASON) {
            metaMap.clear();
            logger.debug("非kvk赛季服启动，服务器id{}, 无需加载{}", Application.getServerId(), getClass().getSimpleName());
            return;
        }

        for (KvkBossFreshMeta metaData : list) {
            Map<Integer, Integer> roundMap = new HashMap<>();
            roundMap.put(1, metaData.getNum1());
            roundMap.put(2, metaData.getNum2());
            roundMap.put(3, metaData.getNum3());
            roundMap.put(4, metaData.getNum4());
            roundMap.put(5, metaData.getNum5());
            roundMap.put(6, metaData.getNum6());
            roundMap.put(7, metaData.getNum7());
            metaData.setRoundNumMap(roundMap);

            data.compute(BossFreshType.valueOf(metaData.getType()), (k, v) -> v == null ? new ArrayList<>() : v).add(metaData);
        }
    }

    public Map<String, KvkBossFreshMeta> getMetaMap() {
        return metaMap;
    }

    public KvkBossFreshMeta get(String id) {
        return metaMap.get(id);
    }

    public List<KvkBossFreshMeta> getMetaByType(BossFreshType type) {
        return data.get(type);
    }

    public List<KvkBossFreshMeta> getMetaByTypeAndFrenzy(BossFreshType type, int frenzyType) {
        if(getMetaByType(type) == null) {
            return Lists.newArrayList();
        }

        List<KvkBossFreshMeta> metas = Lists.newArrayList();
        for(KvkBossFreshMeta meta:getMetaByType(type)){
            if(meta.getFrenzyType() == frenzyType){
                metas.add(meta);
            }
        }

        return metas;
    }

    public static class KvkBossFreshMeta extends AbstractMeta implements IRoundRefreshMeta {
        /*
         * 赛季标记
         */
        @JsonIgnore
        private List<Integer> season;

        /*
         * 刷新类型
         */
        private int type;

        /*
         * bossid
         */
        private String bossid;

        /*
         * 是否开放
         */
        private int enableBoss;

        /*
         * 刷新数量6
         */
        private int num6;
        private int num5;
        private int num4;
        private int num3;
        private int num2;
        private int num1;
        @Getter
        private int num7;

        @JsonIgnore
        private Map<Integer, Integer> roundNumMap;

        private int frenzyType;

        public void init(JsonNode json) {
            season = MetaUtils.parseIntegerList(json.path("season").asText(), AbstractMeta.META_SEPARATOR_2);
        }

        public List<Integer> getSeason() {
            return season;
        }

        public int getType() {
            return type;
        }

        public String getBossid() {
            return bossid;
        }

        public int getEnableBoss() {
            return enableBoss;
        }

        public int getNum6() {
            return num6;
        }

        public int getNum5() {
            return num5;
        }

        public int getNum4() {
            return num4;
        }

        public int getNum3() {
            return num3;
        }

        public int getNum2() {
            return num2;
        }

        public int getNum1() {
            return num1;
        }

        public int getFrenzyType() {
            return frenzyType;
        }

        public void setFrenzyType(int frenzyType) {
            this.frenzyType = frenzyType;
        }

        public boolean isClose() {
            return enableBoss == 0;
        }

        public Map<Integer, Integer> getRoundNumMap() {
            return roundNumMap;
        }

        public void setRoundNumMap(Map<Integer, Integer> roundNumMap) {
            this.roundNumMap = roundNumMap;
        }
    }
}
