package com.lc.billion.icefire.game.biz.model.role;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IRolesEntity;
import com.lc.billion.icefire.game.biz.model.alipay.AlipayReportProgressInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.battleLose.AttackAutoProtect;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.favorites.RoleFavorite;
import com.lc.billion.icefire.game.biz.model.record.RolePlunderRecord;
import com.lc.billion.icefire.protocol.constant.PsRewardClaimStatus;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.jongo.marshall.jackson.oid.MongoId;

import java.util.*;

/**
 * <AUTHOR>
 */
@Data
public class RoleExtra extends AbstractEntity implements IRolesEntity {
    private static final long serialVersionUID = -6179602967047262717L;

    @MongoId
    private Long roleId;

    private boolean isGm;

    private long totalPay;

    // 单次最大额付费
    private double maxPay;

    private double totalDollar;

    private double totalRmb = 0;
    private double totalRmbOld = 0;
    private double dayRechargeRmb = 0;

    private long totalCost;

    private int timeSecOnline;

    private int timeSecOnlineToday;

    private boolean firstRecharge;

    private PsRewardClaimStatus firstRechargeClaimStatus = PsRewardClaimStatus.INVALID; // 首冲奖励领取状态

    private long firstRechargeTime; // 首充时间

    private PsRewardClaimStatus firstRechargeSecondDayClaimStatus = PsRewardClaimStatus.INVALID; // 首冲次日奖励领取状态

    private String firstRechargeConfig; // 首冲使用那个方案

    private Map<String, Boolean> firstRechargeDouble;// 是否满足首冲双倍 ，false不满足首冲双倍，不存在或true满足

    private long lastLibaoRewardTime; // 每日特惠-每日宝箱领取时间

    private int loginDay;

    protected long lastBaseOutputTime;

    /**
     * 最高能攻击的怪物等级
     */
    private int npcCanAttackLevel;
    /**
     * 连续登录天数
     */
    private int continuousLoginDays;
    /**
     * VIP连续登录天数~ 当玩家没有连续登录时，连续登录天数 = 当前连续登录天数-(当前日期-上次登录日期-2)”
     */
    private int vipContinuousLoginDays;

    private int fakeBeautyTimes;

    private String kingdomFirstOccupyRewards;

    /**
     * 最后一次零点调度时间
     */
    private long lastResetDayDataTime;

    // TWD军队损失低保规则
    private long lastProtectTime;// 上次保护时间
    private int protectCounter;// 保护次数

    /**
     * 购买礼包/vip升级等 获得的额外建筑数量
     */
    private Map<Integer, Integer> extraBuildingNums;

    /**
     * 是否领取了评价奖励
     **/
    private boolean isGetEvaluationReward;

    private long fightPower = 0;// 玩家战斗力

    /**
     * 是否迁服后首次登录
     */
    private boolean migrateFirstLogin;

    /**
     * 上次迁服时间
     */
    private long migrateLastTime;
    // 每日联盟援助上限
    private long resHelpAmountDayLimt;
    // 每日联盟援助接收量
    private long resHelpDailyReceivedAmount;
    // 联盟资源援助上次重置时间
    private long lastResetResHelpTime;
    private boolean migrateGVGFirstLogin;
    private boolean migrateGVGBackFirstLogin;
    private int modOrGmFlag; // 0 默认 1:MOD 2:GM

    // region allianceGift相关
    // 联盟礼物是否匿名
    private boolean allianceGiftAnonymity;
    /**
     * 联盟宝箱领取数量记录日期。每天限n个
     */
    private int allianceGiftReceiveDate;
    /**
     * 联盟宝箱领取数量记录数量。每天限n个
     */
    private int allianceGiftReceiveCount;
    /**
     * 最后一次推送联盟礼物失效时玩家的最后登录时间
     */
    @Deprecated
    private long lastPushTimeAllianceGiftExpire;
    // endregion allianceGift相关

    /**
     * KVK是否迁服后首次登录
     */
    private boolean migrateKVKFirstLogin;
    // KVK迁回首次登录
    private boolean migrateKVKBackFirstLogin;
    // 玩家KVK赛季，在第一次进入K服时置成K服赛季信息
    private int kvkSeason;

    private List<String> autoMailIdList = new ArrayList<>();

    // ========================CSA需求开始===============================

    /**
     * 跨城攻击后回来首次登录
     */
    private boolean migrateCSABackFirstLogin;

    /**
     * 跨服夺城场次奖励
     */
    private Map<Integer, Integer> csaPlayRewardStatus = new HashMap<>();

    /**
     * 跨服夺城得分
     */
    private Map<Long, Long> csaBattleScoreMap = new HashMap<>();

    // ========================CSA需求结束===============================

    // ----------【K服/原服坐标点点击跳转规则处理】需求变量开始 ---------- //
    /**
     * 目标点的X坐标
     */
    private int targetX;

    /**
     * 目标点的Y坐标
     */
    private int targetY;

    /**
     * 是否按照上面两个坐标进行跨服。true表示按照targetX，targetY坐标进行迁城；false表示随机迁城
     */
    private boolean isTargetedRelocate;

    /**
     * 玩家参加gvg杯赛时所在服务器id
     */
    private int gvgServerId;

    /**
     * 集成弹板功能，玩家已经领取过的奖励
     */
    private Map<String, Integer> springBoardReward;

    /**
     * bingo每日宝箱领取状态 key:dropId,val:领取时间戳
     */
    private Map<String, Long> bingoDailyBox;

    private long todayFirstLoginTime;

    /**
     * 进入tvt战场：1:红 2:蓝
     */
    private int tvtTeamId = 0;

    /**
     * 进入tvt战场：玩家隐藏积分
     */
    private int tvtHideScore = 0;

    /**
     * 进入tvt战场：玩家真实积分
     */
    private int tvtPowerScore = 0;

    /**
     * 玩家自动补兵的开关（弃用，通过城市buff判断玩家是否可以自动补兵）
     */
    private int autoInstantFormation;

    /**
     * 退出联盟的时间(毫秒级时间戳)，为了计算再次加入联盟的动态cd，这个属性只有满足一定的玩家等级才记录此值
     */
    private long exitAllianceTime;

    /**
     * 玩家领奖记录，主城里程碑等功能的唯一性奖励领取记录
     */
    private List<String> rewardRecord;

    private List<RoleFavorite> favoriteList = new ArrayList<>();

    /** 自动参加PVE集结是加入联盟后才有的功能 */
    /**
     * 自动参加PVE集结的结束时间
     */
    private long autoRallyPveEndTime;
    /**
     * 自动参加pve集结的士兵配置
     */
    private Map<String, Integer> autoRallyPveSoldier = null;
    /**
     * 自动参加pve集结的英雄配置
     */
    private List<String> autoRallyPveHeros = null;

    private List<ArmyType> autoRallyArmyType;
    /**
     * 集结是否使用自定义阵容
     */
    private boolean autoRallySelfDefine;
    /**
     * 是否禁用离线集自动集结
     */
    private boolean disableOfflineAutoRally;
    /**
     * 每日加入集结worldBoss的次数
     */
    private int dailyJoinRallyWorldBossTimes;
    /**
     * 每日挑战worldBoss的次数
     */
    @Deprecated
    private int dailyRewardJoinRallyWorldBossTimes;

    /**
     * 是否首次加入联盟
     */
    private boolean firstJoinAlliance = true;

    /**
     * 第二建筑队列试用次数
     */
    private int buildingQueueTrialTimes;
    /**
     * 付费次数
     */
    private int payTimes;

    /**
     * 每日特惠选择的英雄碎片
     */
    private String dailyGiftHeroFragmentId;

    /**
     * 第一次成功订阅时间
     */
    private long firstWechatSubTime;
    /**
     * 上次登录奖励召回推送时间
     */
    private long lastLoginRewardPushTime;
    /**
     * 上次离线召回推送时间
     */
    private long lastOfflineRecallPushTime;
    /**
     * 订阅有礼 key=activityMetaId value=领奖状态|活动首次开启的红点状态
     */
    private Map<String, Byte> wechatSubReward = new HashMap<>();

    /**
     * 是否打开盟主直聘被招募开关; 0:打开，-1：关闭
     */
    private int allianceRecruitSwitch;

    // 被联盟招募次数
    private int allianceRecruitCount;
    /**
     * 世界聊天次数
     */
    private int worldChatTimes;
    /**
     * 联盟聊天次数
     */
    private int allianceChatTimes;
    /**
     * 私人聊天次数
     */
    private int privateChatTimes;
    /**
     * 改头像次数
     */
    private int changeHeadTimes;
    /**
     * 改名字次数
     */
    private int changeNameTimes;

    private long lastOfflineHangupTime = 0;

    private boolean allianceKickedFlag = false;

    private long allianceCheckLastTime = 0L;

    // ----------被攻城保护功能 需求变量开始 ---------- //
    private AttackAutoProtect attackAutoProtect;

    private Map<Currency, Long> battleLoseResourceMap;

    //补偿治疗时间
    private int battleLoseHealMinutes;

    // ----------被攻城保护功能 需求变量结束 ---------- //

    //下次可申请官职的时间
    private long nextApplyOfficialTime;

    //下次可申请官职的时间
    private long nextApplyOfficialTimeKvK;

    //下次可免费盟迁的时间
    private long nextAllianceFreeMoveTime;

    /**
     * 领取的盟主任务w
     */
    private Set<String> allianceLeaderMissionRewards;

    /**
     * 每日伤兵损失的战力统计
     */
    private long woundedSoldierFP;

    /**
     * 是否发送过联盟反诈邮件
     */
    private boolean sendFraudPreventionMail;
    /**
     * 主公藏品解锁奖励时间
     */
    private long lordTreasureUnlockRewardTime;
    private long lordTreasureJadeUnlockRewardTime;
    private boolean isFirstShareLogin = false;
    private Long shareFromRoleId;

    private Map<Integer, Point> serverPosMap = new HashMap<>();

    private final List<RolePlunderRecord> plunderRecords = new ArrayList<>();
    /**
     * 今日已经消耗负载量
     */
    private long dailyUsedLoad;
    /**
     * 客户端参数
     */
    private String clientId = "";
    private int intClientId = 0;
    @JsonIgnore
    private boolean isRiskLimit = false;

    /**
     * 当前可参与宴会的联盟id
     */
    private long canJoinFeastAllianceId;
    /**
     * 个人宴会冷却时间
     */
    private long feastCoolEndTime;

    /**
     * 支付宝上报进度存档
     */
    private Map<String, AlipayReportProgressInfo> alipayReportProgressInfoMap;

    /**
     * 离线操作的每日重置
     */
    @Getter
    @Setter
    private long offlineOpResetTime;

    /** 每日的红包领取次数 */
    private int dailyRedPackTimes;

    /**
     * 营救进度
     */
    private int rescueProgress = 0;

    /**
     * 是否已领取营救奖励
     */
    private boolean rescueRewardReceived = false;

    @Override
    public void setPersistKey(Long id) {
        this.roleId = id;
    }

    @Override
    public Long getPersistKey() {
        return roleId;
    }

    @Override
    public Long getGroupingId() {
        return roleId;
    }

    @Override
    public Long getRoleId() {
        return getPersistKey();
    }

    @Override
    public void setRoleId(Long roleId) {
        setPersistKey(roleId);
    }

    public boolean isGm() {
        return isGm;
    }

    public void setGm(boolean isGm) {
        this.isGm = isGm;
    }

    public boolean isGetEvaluationReward() {
        return isGetEvaluationReward;
    }

    public void setGetEvaluationReward(boolean getEvaluationReward) {
        isGetEvaluationReward = getEvaluationReward;
    }

    @Override
    public int hashCodeImpl() {
        return hashCodeForPersistKey();
    }

    @Override
    public boolean equalsImpl(Object obj) {
        return equalsForPersistKey(obj);
    }

    public void addAutoMailId(String mailId) {
        if (!autoMailIdList.contains(mailId))
            autoMailIdList.add(mailId);
    }

    public boolean isSendAutoMail(String mailId) {
        return autoMailIdList.contains(mailId);
    }

    public boolean isTargetedRelocate() {
        return isTargetedRelocate;
    }

    public void setTargetedRelocate(boolean targetedRelocate) {
        isTargetedRelocate = targetedRelocate;
    }

    public List<RoleFavorite> getFavoriteList() {
        if (favoriteList == null) {
            favoriteList = new ArrayList<>();
        }
        return favoriteList;
    }

    public RoleFavorite getFavorite(int id) {
        if (favoriteList == null) {
            return null;
        }
        for (RoleFavorite r : favoriteList) {
            if (r.getId() == id) {
                return r;
            }
        }
        return null;
    }

    public boolean deleteFavorite(int id) {
        RoleFavorite roleFavorite = getFavorite(id);
        if (roleFavorite != null) {
            getFavoriteList().remove(roleFavorite);
            return true;
        }
        return false;
    }

    public void addFavorite(RoleFavorite roleFavorite) {
        int id = roleFavorite.getServerId() * 100000000 + roleFavorite.getX() * 10000 + roleFavorite.getY();
        RoleFavorite old = getFavorite(id);
        if (old != null) {
            getFavoriteList().remove(old);
        }
        roleFavorite.setId(id);
        getFavoriteList().add(roleFavorite);
    }

    public Map<String, Integer> getAutoRallyPveSoldier() {
        if (autoRallyPveSoldier == null) {
            autoRallyPveSoldier = new HashMap<>();
        }
        return autoRallyPveSoldier;
    }

    public List<String> getAutoRallyPveHeros() {
        if (autoRallyPveHeros == null) {
            autoRallyPveHeros = new ArrayList<>();
        }
        return autoRallyPveHeros;
    }

    public List<ArmyType> getAutoRallyArmyType() {
        if (autoRallyArmyType == null) {
            //烽火台默认占用WorldBoss的次数
            autoRallyArmyType = new ArrayList<>(List.of(
                    ArmyType.RALLY_WORLD_BOSS,
                    ArmyType.RALLY_SEVEN_CAPTURE,
                    ArmyType.WORLD_EXPLORE_EVENT_RALLY,
                    ArmyType.RALLY_PVE_WHISPERER
            ));
        }
        return autoRallyArmyType;
    }

    public long onSoldierWounded(long loseFp) {
        if (loseFp <= 0) {
            return woundedSoldierFP;
        }

        long maxCanAdd = Long.MAX_VALUE - woundedSoldierFP;
        if (maxCanAdd <= loseFp) {
            woundedSoldierFP = Long.MAX_VALUE;
        } else {
            woundedSoldierFP += loseFp;
        }

        return woundedSoldierFP;
    }

    public void incrTotalRmb(double rmb) {
        this.totalRmb += rmb;
    }

    public void incrDayRechargeRmb(double rmb) {
        dayRechargeRmb += rmb;
    }

    public void onResetDayData(Role role, boolean isEnterWorld) {
        woundedSoldierFP = 0;
        allianceRecruitCount = 0;
    }

    /**
     * 获取玩家在一个服上对应的坐标 来回迁服时使用，迁服之前保存一下当前服的坐标，取一下目标服的坐标
     *
     * @param serverId
     * @return
     */
    public Point getServerPosMap(int serverId) {
        return serverPosMap != null ? serverPosMap.get(serverId) : null;
    }

    public void savePos(int serverId, Point point) {
        getServerPosMap().put(serverId, point);
    }

    public Map<Integer, Point> getServerPosMap() {
        if (serverPosMap == null) {
            serverPosMap = new HashMap<>();
        }
        return serverPosMap;
    }

    public void addPlunderRecord(RolePlunderRecord record) {
        this.dailyUsedLoad += record.getLoad();
        this.plunderRecords.add(record);
    }

    public void resetPlunderRecord() {
        this.plunderRecords.clear();
        this.dailyUsedLoad = 0;
    }

    public Map<String, AlipayReportProgressInfo> getAlipayReportProgressInfoMap() {
        if (alipayReportProgressInfoMap == null) {
            alipayReportProgressInfoMap = new HashMap<>(4);
        }
        return alipayReportProgressInfoMap;
    }


    public AlipayReportProgressInfo getAlipayReportProgressInfo(String id) {
        if (id == null || id.trim().isEmpty()) {
            return null;
        }
        return this.getAlipayReportProgressInfoMap().computeIfAbsent(id, AlipayReportProgressInfo::new);
    }



}
