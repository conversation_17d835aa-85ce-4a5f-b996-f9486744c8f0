# SGF监控模块

基于Micrometer和Prometheus的轻量级监控模块，专为游戏服务器设计，支持JVM、WebSocket网络和业务方法监控。

## 特性

- **JVM监控**: 内存、GC、线程、CPU等系统指标
- **WebSocket监控**: 连接数、消息传输、处理时间等网络指标  
- **业务监控**: 方法执行时间、调用次数、成功率等业务指标
- **高性能**: 异步指标收集，最小化对游戏性能的影响
- **零侵入**: 基于注解和AOP，无需修改现有业务代码
- **Prometheus集成**: 开箱即用的指标导出

## 快速开始

### 1. 添加依赖

在游戏模块的`pom.xml`中添加：

```xml
<dependency>
    <groupId>com.lc.billion</groupId>
    <artifactId>sgf-monitoring</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 启用监控

在Spring配置类上添加注解：

```java
@Configuration
@EnableMonitoring(
    applicationName = "icefire-game",
    enableJvmMetrics = true,
    enableWebSocketMetrics = true,
    enableMethodMetrics = true
)
public class GameConfig {
    // 其他配置...
}
```

### 3. 业务方法监控

在需要监控的业务方法上添加注解：

```java
@Service
public class RescueService {
    
    @Timed(name = "rescue.operation.time", description = "救援操作执行时间")
    @Counted(name = "rescue.operation.count", description = "救援操作调用次数")
    public GcRescueResult rescue(Role role) {
        // 业务逻辑
        return result;
    }
    
    @Timed(name = "rescue.reward.time", description = "获取救援奖励时间")
    public GcRescueRewardResult getRescueReward(Role role) {
        // 业务逻辑
        return result;
    }
}
```

### 4. WebSocket监控集成

在Netty管道中添加监控Handler：

```java
public class WebSocketServerInitializer extends ChannelInitializer<SocketChannel> {
    
    @Autowired
    private WebSocketMetricsCollector webSocketMetricsCollector;
    
    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();
        
        // 添加WebSocket监控Handler（在业务Handler之前）
        pipeline.addLast("websocket-monitoring", 
            new WebSocketMonitoringHandler(webSocketMetricsCollector));
        
        // 其他Handler...
        pipeline.addLast("websocket-handler", new WebSocketServerHandler());
        pipeline.addLast("game-handler", new GameHandler());
    }
}
```

### 5. 配置属性

在`application.properties`或`monitoring.properties`中配置：

```properties
# 应用名称
monitoring.application.name=icefire-game

# Prometheus导出配置
monitoring.prometheus.port=9090
monitoring.prometheus.path=/metrics
monitoring.prometheus.host=0.0.0.0

# 启用各种监控
monitoring.jvm.enabled=true
monitoring.websocket.enabled=true
monitoring.method.enabled=true
```

## 监控指标

### JVM指标
- `jvm.memory.heap.usage.ratio` - 堆内存使用率
- `jvm.threads.active` - 活跃线程数
- `jvm.gc.pause` - GC暂停时间
- `system.cpu.count` - CPU核心数

### WebSocket指标
- `websocket.connections.active` - 当前活跃连接数
- `websocket.connections.total` - 总连接数
- `websocket.messages.received.total` - 接收消息总数
- `websocket.messages.sent.total` - 发送消息总数
- `websocket.message.processing.time` - 消息处理时间

### 业务指标
- `method.execution.time.*` - 方法执行时间
- `method.invocations.*` - 方法调用次数
- `rescue.operation.time` - 救援操作时间（示例）

## 访问监控数据

### Prometheus指标端点
```
http://<server-ip>:9090/metrics
```

### 健康检查端点
```
http://<server-ip>:9090/health
```

**注意**: `<server-ip>` 会自动显示为服务器的实际IP地址，不再是硬编码的localhost。

### 配置说明

- `monitoring.prometheus.host`: 可选配置，指定HTTP服务器绑定的主机地址
  - 如果不配置或为空，默认绑定到所有网络接口 (0.0.0.0)
  - 可以设置为特定IP地址，如 `*************`
  - 日志中会自动显示实际的服务器IP地址，而不是localhost

## 性能考虑

1. **异步处理**: 所有指标记录都是异步进行，不会阻塞游戏线程
2. **安全执行**: 监控错误不会影响业务逻辑
3. **内存优化**: 使用轻量级数据结构，最小化内存占用
4. **智能采样**: 支持固定比例和自适应采样策略，显著降低高频场景的性能影响

### 采样功能详解

#### 采样策略类型

**1. 固定比例采样 (Fixed Rate)**
- 按配置的固定比例进行采样（如10%）
- 适用于流量相对稳定的场景
- 配置简单，性能开销最小

**2. 自适应采样 (Adaptive)**
- 根据指标访问频率动态调整采样率
- 低频指标：高采样率（最大50%）确保数据完整性
- 高频指标：低采样率（最小1%）减少性能影响
- 自动优化，无需人工调优

#### 采样配置

```properties
# 启用采样
monitoring.sampling.enabled=true
monitoring.sampling.strategy=adaptive

# 固定比例采样配置
monitoring.sampling.fixed.rate=0.1

# 自适应采样配置
monitoring.sampling.adaptive.base.rate=0.1
monitoring.sampling.adaptive.window.size.ms=60000
monitoring.sampling.adaptive.high.frequency.threshold=100
monitoring.sampling.adaptive.min.rate=0.01
monitoring.sampling.adaptive.max.rate=0.5
```

#### 性能效果

根据测试，在高频场景下（>1000 req/s），采样功能可以带来：
- **CPU开销降低**: 60-90%
- **内存使用减少**: 50-80%
- **延迟影响最小化**: <1ms
- **数据质量保证**: 重要趋势和异常仍可准确监控

#### 采样最佳实践

1. **生产环境建议**: 使用自适应采样，让系统自动优化
2. **测试环境**: 可使用较高的固定采样率
3. **关键指标**: 对于重要的业务指标，可单独配置更高采样率
4. **监控采样效果**: 定期检查采样统计，确保数据质量

## 与Prometheus集成

### Prometheus配置示例

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'icefire-game'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
```

### Grafana仪表板

监控模块提供的指标可以轻松在Grafana中创建仪表板：

1. JVM性能仪表板
2. WebSocket连接监控
3. 业务操作统计
4. 错误率趋势

## 最佳实践

1. **合理使用注解**: 不要在高频调用的小方法上添加@Timed注解
2. **自定义指标名称**: 为重要的业务方法提供有意义的指标名称
3. **标签使用**: 合理使用标签对指标进行分类
4. **监控告警**: 结合Prometheus AlertManager设置关键指标告警

## 故障排除

### 常见问题

1. **指标不显示**: 检查配置文件和注解是否正确
2. **性能影响**: 确认异步处理已启用，考虑启用采样
3. **连接问题**: 检查Prometheus端口是否被占用

### 日志调试

启用DEBUG日志查看详细监控信息：

```properties
logging.level.com.lc.billion.monitoring=DEBUG
``` 