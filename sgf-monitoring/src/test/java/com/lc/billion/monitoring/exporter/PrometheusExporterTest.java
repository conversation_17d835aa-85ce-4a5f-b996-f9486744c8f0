package com.lc.billion.monitoring.exporter;

import io.micrometer.prometheusmetrics.PrometheusConfig;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PrometheusExporter 测试类
 */
public class PrometheusExporterTest {

    private PrometheusMeterRegistry registry;
    private PrometheusExporter exporter;

    @BeforeEach
    void setUp() {
        registry = new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }

    @AfterEach
    void tearDown() {
        if (exporter != null && exporter.isRunning()) {
            exporter.stop();
        }
    }

    @Test
    void testConstructorWithoutHost() {
        exporter = new PrometheusExporter(registry, 9091, "/metrics");
        
        assertEquals(9091, exporter.getPort());
        assertEquals("/metrics", exporter.getPath());
        assertFalse(exporter.isRunning());
    }

    @Test
    void testConstructorWithHost() {
        exporter = new PrometheusExporter(registry, 9092, "/metrics", "127.0.0.1");
        
        assertEquals(9092, exporter.getPort());
        assertEquals("/metrics", exporter.getPath());
        assertFalse(exporter.isRunning());
    }

    @Test
    void testStartAndStop() {
        exporter = new PrometheusExporter(registry, 9093, "/metrics");
        
        // 启动服务器
        exporter.start();
        assertTrue(exporter.isRunning());
        
        // 获取服务器地址
        String serverAddress = exporter.getServerAddress();
        assertNotNull(serverAddress);
        assertFalse("localhost".equals(serverAddress) && serverAddress.contains("0.0.0.0"));
        
        // 停止服务器
        exporter.stop();
    }

    @Test
    void testGetServerAddressWhenNotRunning() {
        exporter = new PrometheusExporter(registry, 9094, "/metrics");
        
        String serverAddress = exporter.getServerAddress();
        assertEquals("localhost", serverAddress);
    }

    @Test
    void testStartWithSpecificHost() {
        exporter = new PrometheusExporter(registry, 9095, "/metrics", "127.0.0.1");
        
        // 启动服务器
        exporter.start();
        assertTrue(exporter.isRunning());
        
        // 获取服务器地址
        String serverAddress = exporter.getServerAddress();
        assertEquals("127.0.0.1", serverAddress);
        
        // 停止服务器
        exporter.stop();
    }
}
