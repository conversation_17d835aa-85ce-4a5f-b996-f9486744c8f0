package com.lc.billion.monitoring.sampling;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 采样策略测试
 * <p>
 * 验证采样功能在高频场景下的性能优化效果
 * </p>
 */
public class SamplingStrategyTest {
    
    private FixedRateSamplingStrategy fixedRateStrategy;
    private AdaptiveSamplingStrategy adaptiveStrategy;
    
    @BeforeEach
    void setUp() {
        fixedRateStrategy = new FixedRateSamplingStrategy(0.1); // 10%采样率
        adaptiveStrategy = new AdaptiveSamplingStrategy();
    }
    
    @Test
    void testFixedRateSampling() {
        System.out.println("=== 固定比例采样测试 ===");
        
        int totalRequests = 10000;
        int sampledCount = 0;
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < totalRequests; i++) {
            if (fixedRateStrategy.shouldSample("test.metric", "type", "test")) {
                sampledCount++;
            }
        }
        
        long endTime = System.currentTimeMillis();
        double actualRate = (double) sampledCount / totalRequests;
        
        System.out.printf("总请求数: %d%n", totalRequests);
        System.out.printf("采样数: %d%n", sampledCount);
        System.out.printf("实际采样率: %.3f%n", actualRate);
        System.out.printf("配置采样率: 0.100%n");
        System.out.printf("处理时间: %d ms%n", endTime - startTime);
        
        // 验证采样率在合理范围内（允许一定的随机波动）
        assertTrue(actualRate >= 0.08 && actualRate <= 0.12, 
                "采样率应该在8%-12%之间，实际: " + actualRate);
        
        // 验证统计信息
        var stats = fixedRateStrategy.getStats();
        assertEquals(totalRequests, stats.getTotalRequests());
        assertEquals(sampledCount, stats.getSampledRequests());
        
        System.out.println(stats);
        System.out.println();
    }
    
    @Test
    void testAdaptiveSampling() {
        System.out.println("=== 自适应采样测试 ===");
        
        // 模拟不同频率的指标
        testMetricFrequency("low.frequency.metric", 10, "低频指标");
        testMetricFrequency("medium.frequency.metric", 500, "中频指标");
        testMetricFrequency("high.frequency.metric", 2000, "高频指标");
        
        adaptiveStrategy.printStats();
        System.out.println();
    }
    
    private void testMetricFrequency(String metricName, int requestCount, String description) {
        int sampledCount = 0;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < requestCount; i++) {
            if (adaptiveStrategy.shouldSample(metricName, "operation", "test")) {
                sampledCount++;
            }
            
            // 模拟一些处理时间
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        long endTime = System.currentTimeMillis();
        double actualRate = requestCount > 0 ? (double) sampledCount / requestCount : 0.0;
        double requestsPerSecond = (double) requestCount * 1000 / (endTime - startTime);
        
        System.out.printf("%s: 请求数=%d, 采样数=%d, 采样率=%.3f, 频率=%.1f req/s%n",
                description, requestCount, sampledCount, actualRate, requestsPerSecond);
    }
    
    @Test
    void testSamplingPerformanceImpact() {
        System.out.println("=== 采样性能影响测试 ===");
        
        int iterations = 100000;
        
        // 测试无采样的性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            // 模拟监控记录操作
            simulateMonitoringRecord(true);
        }
        long noSamplingTime = System.currentTimeMillis() - startTime;
        
        // 测试有采样的性能（10%采样率）
        startTime = System.currentTimeMillis();
        int actualRecords = 0;
        for (int i = 0; i < iterations; i++) {
            if (fixedRateStrategy.shouldSample("performance.test", "iteration", String.valueOf(i))) {
                simulateMonitoringRecord(true);
                actualRecords++;
            } else {
                simulateMonitoringRecord(false); // 模拟跳过采样的轻量操作
            }
        }
        long samplingTime = System.currentTimeMillis() - startTime;
        
        System.out.printf("无采样时间: %d ms (处理 %d 条记录)%n", noSamplingTime, iterations);
        System.out.printf("有采样时间: %d ms (处理 %d 条记录，跳过 %d 条)%n", 
                samplingTime, actualRecords, iterations - actualRecords);
        System.out.printf("性能提升: %.1f%%(%d ms -> %d ms)%n", 
                (double)(noSamplingTime - samplingTime) / noSamplingTime * 100,
                noSamplingTime, samplingTime);
        
        // 验证采样确实减少了处理量
        assertTrue(actualRecords < iterations * 0.15, // 应该少于15%
                "采样后的记录数应该显著减少");
        System.out.println();
    }
    
    /**
     * 模拟监控记录操作
     */
    private void simulateMonitoringRecord(boolean actualRecord) {
        if (actualRecord) {
            // 模拟完整的监控记录操作（较重）
            for (int i = 0; i < 10; i++) {
                Math.random(); // 模拟一些计算
            }
        } else {
            // 模拟采样检查操作（很轻）
            Math.random();
        }
    }
    
    @Test
    void testSamplingWithDifferentTags() {
        System.out.println("=== 不同标签采样测试 ===");
        
        // 测试相同指标名但不同标签的采样独立性
        String metricName = "user.action";
        String[] tags1 = {"action", "login", "user_type", "normal"};
        String[] tags2 = {"action", "battle", "user_type", "vip"};
        String[] tags3 = {"action", "purchase", "user_type", "normal"};
        
        int requests = 1000;
        int sampled1 = 0, sampled2 = 0, sampled3 = 0;
        
        for (int i = 0; i < requests; i++) {
            if (fixedRateStrategy.shouldSample(metricName, tags1)) sampled1++;
            if (fixedRateStrategy.shouldSample(metricName, tags2)) sampled2++;
            if (fixedRateStrategy.shouldSample(metricName, tags3)) sampled3++;
        }
        
        System.out.printf("login操作采样: %d/%d (%.1f%%)%n", sampled1, requests, 
                (double)sampled1/requests*100);
        System.out.printf("battle操作采样: %d/%d (%.1f%%)%n", sampled2, requests, 
                (double)sampled2/requests*100);
        System.out.printf("purchase操作采样: %d/%d (%.1f%%)%n", sampled3, requests, 
                (double)sampled3/requests*100);
        
        // 验证各个标签的采样都在合理范围内
        assertTrue(sampled1 >= requests * 0.05 && sampled1 <= requests * 0.15);
        assertTrue(sampled2 >= requests * 0.05 && sampled2 <= requests * 0.15);
        assertTrue(sampled3 >= requests * 0.05 && sampled3 <= requests * 0.15);
    }
} 