package com.lc.billion.monitoring.example;

import com.lc.billion.monitoring.annotation.EnableMonitoring;
import com.lc.billion.monitoring.metrics.WebSocketMetricsCollector;
import com.lc.billion.monitoring.netty.WebSocketMonitoringHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * 监控模块配置示例
 * <p>
 * 展示如何在游戏项目中配置和使用监控功能
 * </p>
 */
@Configuration
@EnableMonitoring(
    applicationName = "icefire-game",
    enableJvmMetrics = true,
    enableWebSocketMetrics = true,
    enableMethodMetrics = true
)
@PropertySource("classpath:monitoring.properties")
public class MonitoringExampleConfiguration {
    
    @Autowired
    private WebSocketMetricsCollector webSocketMetricsCollector;
    
    /**
     * WebSocket监控Handler工厂
     * 用于在Netty管道中创建监控Handler
     */
    @Bean
    public WebSocketMonitoringHandlerFactory webSocketMonitoringHandlerFactory() {
        return new WebSocketMonitoringHandlerFactory(webSocketMetricsCollector);
    }
    
    /**
     * WebSocket监控Handler工厂类
     */
    public static class WebSocketMonitoringHandlerFactory {
        private final WebSocketMetricsCollector metricsCollector;
        
        public WebSocketMonitoringHandlerFactory(WebSocketMetricsCollector metricsCollector) {
            this.metricsCollector = metricsCollector;
        }
        
        public WebSocketMonitoringHandler createHandler() {
            return new WebSocketMonitoringHandler(metricsCollector);
        }
    }
} 