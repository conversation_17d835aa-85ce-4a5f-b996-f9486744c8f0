package com.lc.billion.monitoring.example;

import com.lc.billion.monitoring.annotation.Timed;
import com.lc.billion.monitoring.annotation.Counted;
import org.springframework.stereotype.Service;

/**
 * 救援服务监控使用示例
 * <p>
 * 展示如何在游戏业务服务中集成监控功能
 * </p>
 */
@Service
@Timed(name = "rescue.service", description = "救援服务总体性能")
public class MonitoredRescueService {

    /**
     * 执行救援操作 - 监控执行时间和调用次数
     */
    @Timed(
        name = "rescue.operation.execution.time", 
        description = "救援操作执行时间",
        extraTags = {"operation", "rescue"}
    )
    @Counted(
        name = "rescue.operation.invocations", 
        description = "救援操作调用次数",
        extraTags = {"operation", "rescue"}
    )
    public MockGcRescueResult rescue(MockRole role) {
        // 模拟业务逻辑
        try {
            // 模拟数据库查询
            Thread.sleep(10);
            
            // 模拟业务处理
            if (role.getLevel() < 10) {
                throw new IllegalArgumentException("等级不足");
            }
            
            // 模拟成功处理
            Thread.sleep(5);
            
            return new MockGcRescueResult("SUCCESS", "救援成功");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("救援操作被中断", e);
        }
    }

    /**
     * 获取救援奖励 - 只监控执行时间
     */
    @Timed(
        name = "rescue.reward.execution.time", 
        description = "获取救援奖励执行时间",
        extraTags = {"operation", "get_reward"}
    )
    public MockGcRescueRewardResult getRescueReward(MockRole role) {
        try {
            // 模拟奖励计算
            Thread.sleep(8);
            
            return new MockGcRescueRewardResult("SUCCESS", "奖励发放成功");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取奖励被中断", e);
        }
    }

    /**
     * 获取救援进度 - 高频调用，只记录失败情况
     */
    @Counted(
        name = "rescue.progress.errors", 
        description = "获取救援进度错误次数",
        recordFailuresOnly = true,
        extraTags = {"operation", "get_progress"}
    )
    public MockGcRescueProgress getRescueProgress(MockRole role) {
        try {
            // 模拟快速查询
            Thread.sleep(2);
            
            // 模拟偶尔的错误
            if (Math.random() < 0.1) {
                throw new RuntimeException("数据获取失败");
            }
            
            return new MockGcRescueProgress(role.getRescueProgress(), 100);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取进度被中断", e);
        }
    }

    /**
     * 批量救援 - 长时间运行的任务
     */
    @Timed(
        name = "rescue.batch.execution.time", 
        description = "批量救援执行时间",
        longTask = true,
        extraTags = {"operation", "batch_rescue"}
    )
    public void batchRescue(java.util.List<MockRole> roles) {
        for (MockRole role : roles) {
            try {
                // 模拟每个救援操作
                Thread.sleep(50);
                rescue(role);
            } catch (Exception e) {
                // 记录但不中断批量操作
                System.err.println("批量救援中的错误: " + e.getMessage());
            }
        }
    }

    // 模拟数据类
    public static class MockRole {
        private final int level;
        private final int rescueProgress;

        public MockRole(int level, int rescueProgress) {
            this.level = level;
            this.rescueProgress = rescueProgress;
        }

        public int getLevel() { return level; }
        public int getRescueProgress() { return rescueProgress; }
    }

    public static class MockGcRescueResult {
        private final String status;
        private final String message;

        public MockGcRescueResult(String status, String message) {
            this.status = status;
            this.message = message;
        }

        public String getStatus() { return status; }
        public String getMessage() { return message; }
    }

    public static class MockGcRescueRewardResult {
        private final String status;
        private final String message;

        public MockGcRescueRewardResult(String status, String message) {
            this.status = status;
            this.message = message;
        }

        public String getStatus() { return status; }
        public String getMessage() { return message; }
    }

    public static class MockGcRescueProgress {
        private final int current;
        private final int total;

        public MockGcRescueProgress(int current, int total) {
            this.current = current;
            this.total = total;
        }

        public int getCurrent() { return current; }
        public int getTotal() { return total; }
    }
} 