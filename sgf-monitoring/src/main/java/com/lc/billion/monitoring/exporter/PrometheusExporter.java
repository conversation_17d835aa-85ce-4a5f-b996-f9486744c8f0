package com.lc.billion.monitoring.exporter;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Prometheus指标导出器
 * <p>
 * 提供HTTP端点导出Prometheus格式的指标数据
 * 使用轻量级的HttpServer，避免依赖重型Web框架
 * </p>
 *
 */
public class PrometheusExporter {
    
    private static final Logger log = LoggerFactory.getLogger(PrometheusExporter.class);
    
    private final PrometheusMeterRegistry prometheusMeterRegistry;
    private final int port;
    private final String path;
    private final String host;

    private HttpServer httpServer;
    private ExecutorService executorService;

    public PrometheusExporter(PrometheusMeterRegistry prometheusMeterRegistry, int port, String path) {
        this(prometheusMeterRegistry, port, path, null);
    }

    public PrometheusExporter(PrometheusMeterRegistry prometheusMeterRegistry, int port, String path, String host) {
        this.prometheusMeterRegistry = prometheusMeterRegistry;
        this.port = port;
        this.path = path;
        this.host = host;
    }
    
    @PostConstruct
    public void start() {
        try {
            // 创建HTTP服务器
            InetSocketAddress bindAddress;
            if (host != null && !host.trim().isEmpty()) {
                bindAddress = new InetSocketAddress(host, port);
            } else {
                bindAddress = new InetSocketAddress(port);
            }
            httpServer = HttpServer.create(bindAddress, 0);
            
            // 创建线程池
            executorService = Executors.newFixedThreadPool(2, r -> {
                Thread thread = new Thread(r, "prometheus-exporter");
                thread.setDaemon(true);
                return thread;
            });
            
            httpServer.setExecutor(executorService);
            
            // 注册指标端点
            httpServer.createContext(path, new MetricsHandler());
            
            // 注册健康检查端点
            httpServer.createContext("/health", new HealthHandler());
            
            // 启动服务器
            httpServer.start();

            // 获取实际绑定的地址
            String serverAddress = getServerAddress();

            log.info("Prometheus指标导出器已启动，端口: {}, 路径: {}", port, path);
            log.info("指标访问地址: http://{}:{}{}", serverAddress, port, path);
            log.info("健康检查地址: http://{}:{}/health", serverAddress, port);
        } catch (IOException e) {
            log.error("启动Prometheus导出器失败", e);
            throw new RuntimeException("无法启动Prometheus导出器", e);
        }
    }
    
    @PreDestroy
    public void stop() {
        log.info("正在关闭Prometheus指标导出器...");
        
        if (httpServer != null) {
            httpServer.stop(5); // 5秒内停止
        }
        
        if (executorService != null) {
            executorService.shutdown();
        }
        
        log.info("Prometheus指标导出器已关闭");
    }
    
    /**
     * 指标处理器
     */
    private class MetricsHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"GET".equals(exchange.getRequestMethod())) {
                sendResponse(exchange, 405, "Method Not Allowed");
                return;
            }
            
            try {
                // 获取Prometheus格式的指标数据
                String metricsData = prometheusMeterRegistry.scrape();
                
                // 设置响应头
                exchange.getResponseHeaders().set("Content-Type", "text/plain; version=0.0.4; charset=utf-8");
                exchange.getResponseHeaders().set("Cache-Control", "no-cache, no-store, must-revalidate");
                
                // 发送响应
                sendResponse(exchange, 200, metricsData);
                
                log.debug("返回了 {} 字节的指标数据", metricsData.getBytes(StandardCharsets.UTF_8).length);
            } catch (Exception e) {
                log.error("获取指标数据时发生错误", e);
                sendResponse(exchange, 500, "Internal Server Error: " + e.getMessage());
            }
        }
    }
    
    /**
     * 健康检查处理器
     */
    private class HealthHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"GET".equals(exchange.getRequestMethod())) {
                sendResponse(exchange, 405, "Method Not Allowed");
                return;
            }
            
            try {
                // 简单的健康检查
                long meterCount = prometheusMeterRegistry.getMeters().size();
                String healthData = String.format("{\"status\":\"UP\",\"metrics_count\":%d,\"timestamp\":%d}", 
                        meterCount, System.currentTimeMillis());
                
                exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
                sendResponse(exchange, 200, healthData);
            } catch (Exception e) {
                log.error("健康检查时发生错误", e);
                String errorData = String.format("{\"status\":\"DOWN\",\"error\":\"%s\",\"timestamp\":%d}", 
                        e.getMessage(), System.currentTimeMillis());
                exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
                sendResponse(exchange, 503, errorData);
            }
        }
    }
    
    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }
    
    /**
     * 获取服务器端口
     */
    public int getPort() {
        return port;
    }
    
    /**
     * 获取指标路径
     */
    public String getPath() {
        return path;
    }
    
    /**
     * 检查服务器是否正在运行
     */
    public boolean isRunning() {
        return httpServer != null;
    }

    /**
     * 获取服务器实际绑定的地址
     */
    public String getServerAddress() {
        if (httpServer != null) {
            InetSocketAddress address = httpServer.getAddress();
            if (address != null) {
                String hostAddress = address.getAddress().getHostAddress();
                // 如果绑定的是 0.0.0.0，则尝试获取本机的实际IP地址
                if ("0.0.0.0".equals(hostAddress)) {
                    String localIp = getLocalIpAddress();
                    return localIp != null ? localIp : hostAddress;
                }
                return hostAddress;
            }
        }
        return "localhost";
    }

    /**
     * 获取本机IP地址
     * 优先返回非回环地址的IPv4地址
     */
    private String getLocalIpAddress() {
        try {
            List<String> ipList = getLocalIpList(true);
            if (!ipList.isEmpty()) {
                // 返回第一个非回环地址
                return ipList.get(0);
            }
        } catch (Exception e) {
            log.debug("获取本机IP地址失败", e);
        }
        return null;
    }

    /**
     * 获取本机所有IP地址列表
     * 参考 OsUtil.getLocalIpList 的实现
     *
     * @param excludeLoopback 是否排除回环地址
     * @return IP地址列表
     */
    private List<String> getLocalIpList(boolean excludeLoopback) throws SocketException {
        List<String> ipList = new ArrayList<>();
        Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();

        while (networkInterfaces.hasMoreElements()) {
            NetworkInterface networkInterface = networkInterfaces.nextElement();

            // 跳过非活动的网络接口
            if (!networkInterface.isUp()) {
                continue;
            }

            Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
            while (inetAddresses.hasMoreElements()) {
                InetAddress inetAddress = inetAddresses.nextElement();

                // 跳过IPv6地址
                if (inetAddress.getAddress().length != 4) {
                    continue;
                }

                // 根据参数决定是否跳过回环地址
                if (excludeLoopback && inetAddress.isLoopbackAddress()) {
                    continue;
                }

                ipList.add(inetAddress.getHostAddress());
            }
        }

        return ipList;
    }
}