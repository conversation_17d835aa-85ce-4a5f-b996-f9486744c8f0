package com.lc.billion.monitoring.sampling;

import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 固定比例采样策略
 * <p>
 * 基于配置的采样率进行固定比例采样，适用于大多数场景
 * </p>
 */
public class FixedRateSamplingStrategy implements SamplingStrategy {
    
    private final double samplingRate;
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong sampledRequests = new AtomicLong(0);
    
    /**
     * @param samplingRate 采样率，0.0-1.0之间，例如0.1表示采样10%
     */
    public FixedRateSamplingStrategy(double samplingRate) {
        if (samplingRate < 0.0 || samplingRate > 1.0) {
            throw new IllegalArgumentException("采样率必须在0.0-1.0之间: " + samplingRate);
        }
        this.samplingRate = samplingRate;
    }
    
    @Override
    public boolean shouldSample(String metricName, String... tags) {
        totalRequests.incrementAndGet();
        
        // 采样率为0则不采样
        if (samplingRate <= 0.0) {
            return false;
        }
        
        // 采样率为1则全部采样
        if (samplingRate >= 1.0) {
            sampledRequests.incrementAndGet();
            return true;
        }
        
        // 使用ThreadLocalRandom提高性能
        boolean sample = ThreadLocalRandom.current().nextDouble() < samplingRate;
        if (sample) {
            sampledRequests.incrementAndGet();
        }
        
        return sample;
    }
    
    @Override
    public void reset() {
        totalRequests.set(0);
        sampledRequests.set(0);
    }
    
    @Override
    public String getStrategyName() {
        return "FixedRate(" + samplingRate + ")";
    }
    
    /**
     * 获取当前采样统计信息
     */
    public SamplingStats getStats() {
        long total = totalRequests.get();
        long sampled = sampledRequests.get();
        double actualRate = total > 0 ? (double) sampled / total : 0.0;
        
        return new SamplingStats(total, sampled, actualRate, samplingRate);
    }
    
    /**
     * 采样统计信息
     */
    public static class SamplingStats {
        private final long totalRequests;
        private final long sampledRequests;
        private final double actualSamplingRate;
        private final double configuredSamplingRate;
        
        public SamplingStats(long totalRequests, long sampledRequests, 
                           double actualSamplingRate, double configuredSamplingRate) {
            this.totalRequests = totalRequests;
            this.sampledRequests = sampledRequests;
            this.actualSamplingRate = actualSamplingRate;
            this.configuredSamplingRate = configuredSamplingRate;
        }
        
        public long getTotalRequests() { return totalRequests; }
        public long getSampledRequests() { return sampledRequests; }
        public double getActualSamplingRate() { return actualSamplingRate; }
        public double getConfiguredSamplingRate() { return configuredSamplingRate; }
        
        @Override
        public String toString() {
            return String.format("采样统计[总数=%d, 采样数=%d, 实际采样率=%.3f, 配置采样率=%.3f]",
                    totalRequests, sampledRequests, actualSamplingRate, configuredSamplingRate);
        }
    }
} 