package com.lc.billion.monitoring.registry;

import com.lc.billion.monitoring.sampling.SamplingStrategy;
import io.micrometer.core.instrument.*;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * 指标注册表管理器
 * <p>
 * 提供统一的指标创建和管理接口，支持异步处理和采样以减少性能影响
 * </p>
 */
public class MeterRegistryManager {
    
    private static final Logger log = LoggerFactory.getLogger(MeterRegistryManager.class);
    
    private final PrometheusMeterRegistry meterRegistry;
    private final String applicationName;
    private final SamplingStrategy samplingStrategy;
    
    public MeterRegistryManager(PrometheusMeterRegistry meterRegistry, String applicationName) {
        this(meterRegistry, applicationName, null);
    }
    
    public MeterRegistryManager(PrometheusMeterRegistry meterRegistry, String applicationName, 
                               SamplingStrategy samplingStrategy) {
        this.meterRegistry = meterRegistry;
        this.applicationName = applicationName;
        this.samplingStrategy = samplingStrategy;
        
        // 添加通用标签
        meterRegistry.config().commonTags("application", applicationName);
        
        String samplingInfo = samplingStrategy != null ? 
                ", 采样策略: " + samplingStrategy.getStrategyName() : "";
        log.info("监控注册表管理器初始化完成，应用名: {}{}", applicationName, samplingInfo);
    }
    
    /**
     * 获取原始注册表
     */
    public PrometheusMeterRegistry getRegistry() {
        return meterRegistry;
    }
    
    /**
     * 创建计时器
     */
    public Timer timer(String name, String description, String... tags) {
        return Timer.builder(name)
                .description(description)
                .tags(tags)
                .publishPercentileHistogram(true)
                .register(meterRegistry);
    }
    
    /**
     * 创建计数器
     */
    public Counter counter(String name, String description, String... tags) {
        return Counter.builder(name)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }
    
    /**
     * 创建测量仪
     */
    public <T> Gauge gauge(String name, String description, T stateObject, 
                          java.util.function.ToDoubleFunction<T> valueFunction, String... tags) {
        return Gauge.builder(name, stateObject, valueFunction)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }
    
    /**
     * 创建长任务计时器
     */
    public LongTaskTimer longTaskTimer(String name, String description, String... tags) {
        return LongTaskTimer.builder(name)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }
    
    /**
     * 记录计时器样本 - 支持采样的版本
     */
    public void recordTimer(String name, long duration, TimeUnit unit, String... tags) {
        // 检查是否应该采样
        if (samplingStrategy != null && !samplingStrategy.shouldSample(name, tags)) {
            return; // 跳过采样
        }
        
        try {
            Timer timer = timer(name, "", tags);
            timer.record(duration, unit);
        } catch (Exception e) {
            log.warn("记录计时器指标失败: {}", name, e);
        }
    }
    
    /**
     * 增加计数器 - 支持采样的版本
     */
    public void incrementCounter(String name, String... tags) {
        // 检查是否应该采样
        if (samplingStrategy != null && !samplingStrategy.shouldSample(name, tags)) {
            return; // 跳过采样
        }
        
        try {
            Counter counter = counter(name, "", tags);
            counter.increment();
        } catch (Exception e) {
            log.warn("增加计数器指标失败: {}", name, e);
        }
    }
    
    /**
     * 记录数值 - 支持采样的版本
     */
    public void recordValue(String name, double value, String... tags) {
        // 检查是否应该采样
        if (samplingStrategy != null && !samplingStrategy.shouldSample(name, tags)) {
            return; // 跳过采样
        }
        
        try {
            DistributionSummary.builder(name)
                    .tags(tags)
                    .register(meterRegistry)
                    .record(value);
        } catch (Exception e) {
            log.warn("记录数值指标失败: {}", name, e);
        }
    }
    
    /**
     * 获取采样策略
     */
    public SamplingStrategy getSamplingStrategy() {
        return samplingStrategy;
    }
    
    /**
     * 是否启用采样
     */
    public boolean isSamplingEnabled() {
        return samplingStrategy != null;
    }
    
    /**
     * 安全执行指标记录
     */
    public void safeExecute(Runnable metricsAction) {
        try {
            metricsAction.run();
        } catch (Exception e) {
            log.warn("执行指标记录时发生异常", e);
        }
    }
    
    /**
     * 获取应用名称
     */
    public String getApplicationName() {
        return applicationName;
    }
} 