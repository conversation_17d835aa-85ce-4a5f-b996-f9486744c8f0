package com.lc.billion.monitoring.sampling;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 自适应采样策略
 * <p>
 * 根据指标的访问频率动态调整采样率：
 * - 高频指标：降低采样率以减少性能影响
 * - 低频指标：提高采样率以保证数据完整性
 * </p>
 */
public class AdaptiveSamplingStrategy implements SamplingStrategy {
    
    private final double baseSamplingRate;
    private final long windowSizeMs;
    private final long highFrequencyThreshold;
    private final double minSamplingRate;
    private final double maxSamplingRate;
    
    // 每个指标的访问统计
    private final ConcurrentHashMap<String, MetricStats> metricStatsMap = new ConcurrentHashMap<>();
    
    /**
     * @param baseSamplingRate 基础采样率
     * @param windowSizeMs 时间窗口大小(毫秒)
     * @param highFrequencyThreshold 高频阈值(每秒请求数)
     * @param minSamplingRate 最小采样率
     * @param maxSamplingRate 最大采样率
     */
    public AdaptiveSamplingStrategy(double baseSamplingRate, long windowSizeMs, 
                                  long highFrequencyThreshold, double minSamplingRate, 
                                  double maxSamplingRate) {
        this.baseSamplingRate = baseSamplingRate;
        this.windowSizeMs = windowSizeMs;
        this.highFrequencyThreshold = highFrequencyThreshold;
        this.minSamplingRate = minSamplingRate;
        this.maxSamplingRate = maxSamplingRate;
    }
    
    /**
     * 使用默认参数的构造函数
     */
    public AdaptiveSamplingStrategy() {
        this(0.1, 60000, 100, 0.01, 0.5);
    }
    
    @Override
    public boolean shouldSample(String metricName, String... tags) {
        String metricKey = buildMetricKey(metricName, tags);
        MetricStats stats = metricStatsMap.computeIfAbsent(metricKey, k -> new MetricStats());
        
        long now = System.currentTimeMillis();
        stats.recordRequest(now);
        
        // 计算当前采样率
        double currentRate = calculateSamplingRate(stats, now);
        
        // 采样决策
        boolean sample = ThreadLocalRandom.current().nextDouble() < currentRate;
        if (sample) {
            stats.recordSample();
        }
        
        return sample;
    }
    
    @Override
    public void reset() {
        metricStatsMap.clear();
    }
    
    @Override
    public String getStrategyName() {
        return "Adaptive(base=" + baseSamplingRate + ")";
    }
    
    /**
     * 计算采样率
     */
    private double calculateSamplingRate(MetricStats stats, long now) {
        // 计算时间窗口内的请求频率
        long windowStart = now - windowSizeMs;
        long requestsInWindow = stats.getRequestsInWindow(windowStart, now);
        double requestsPerSecond = (double) requestsInWindow * 1000 / windowSizeMs;
        
        // 根据频率调整采样率
        if (requestsPerSecond <= 1.0) {
            // 低频：使用最大采样率
            return maxSamplingRate;
        } else if (requestsPerSecond >= highFrequencyThreshold) {
            // 高频：使用最小采样率
            return minSamplingRate;
        } else {
            // 中频：线性插值
            double ratio = Math.log(requestsPerSecond) / Math.log(highFrequencyThreshold);
            return maxSamplingRate - (maxSamplingRate - minSamplingRate) * ratio;
        }
    }
    
    /**
     * 构建指标唯一键
     */
    private String buildMetricKey(String metricName, String... tags) {
        if (tags.length == 0) {
            return metricName;
        }
        
        StringBuilder keyBuilder = new StringBuilder(metricName);
        for (int i = 0; i < tags.length; i += 2) {
            if (i + 1 < tags.length) {
                keyBuilder.append(":").append(tags[i]).append("=").append(tags[i + 1]);
            }
        }
        return keyBuilder.toString();
    }
    
    /**
     * 获取采样统计信息
     */
    public void printStats() {
        System.out.println("=== 自适应采样统计 ===");
        metricStatsMap.forEach((key, stats) -> {
            long now = System.currentTimeMillis();
            long windowStart = now - windowSizeMs;
            long requestsInWindow = stats.getRequestsInWindow(windowStart, now);
            double requestsPerSecond = (double) requestsInWindow * 1000 / windowSizeMs;
            double currentRate = calculateSamplingRate(stats, now);
            
            System.out.printf("指标: %s, 频率: %.2f req/s, 采样率: %.3f, 总请求: %d, 总采样: %d%n",
                    key, requestsPerSecond, currentRate, 
                    stats.totalRequests.get(), stats.totalSamples.get());
        });
    }
    
    /**
     * 单个指标的统计信息
     */
    private static class MetricStats {
        private final AtomicLong totalRequests = new AtomicLong(0);
        private final AtomicLong totalSamples = new AtomicLong(0);
        private final LongAdder requestsInCurrentWindow = new LongAdder();
        private volatile long windowStartTime = System.currentTimeMillis();
        
        void recordRequest(long timestamp) {
            totalRequests.incrementAndGet();
            
            // 检查是否需要重置窗口
            long currentWindowStart = windowStartTime;
            if (timestamp - currentWindowStart > 60000) { // 1分钟窗口
                synchronized (this) {
                    if (windowStartTime == currentWindowStart) {
                        windowStartTime = timestamp;
                        requestsInCurrentWindow.reset();
                    }
                }
            }
            
            requestsInCurrentWindow.increment();
        }
        
        void recordSample() {
            totalSamples.incrementAndGet();
        }
        
        long getRequestsInWindow(long windowStart, long windowEnd) {
            // 简化实现：返回当前窗口的请求数
            long currentWindowStart = windowStartTime;
            if (windowStart <= currentWindowStart && currentWindowStart < windowEnd) {
                return requestsInCurrentWindow.sum();
            }
            return 0;
        }
    }
} 