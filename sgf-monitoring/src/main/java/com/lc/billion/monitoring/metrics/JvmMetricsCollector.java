package com.lc.billion.monitoring.metrics;

import com.lc.billion.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.binder.jvm.*;
import io.micrometer.core.instrument.binder.system.FileDescriptorMetrics;
import io.micrometer.core.instrument.binder.system.ProcessorMetrics;
import io.micrometer.core.instrument.binder.system.UptimeMetrics;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JVM指标收集器
 * <p>
 * 自动收集JVM相关的监控指标，包括内存、线程、GC等
 * </p>
 *
 */
public class JvmMetricsCollector {
    
    private static final Logger log = LoggerFactory.getLogger(JvmMetricsCollector.class);
    
    private final MeterRegistryManager meterRegistryManager;
    private final boolean enabled;
    
    // JVM指标绑定器
    private JvmMemoryMetrics jvmMemoryMetrics;
    private JvmGcMetrics jvmGcMetrics;
    private JvmThreadMetrics jvmThreadMetrics;
    private ClassLoaderMetrics classLoaderMetrics;
    private JvmCompilationMetrics jvmCompilationMetrics;
    
    // 系统指标绑定器
    private ProcessorMetrics processorMetrics;
    private UptimeMetrics uptimeMetrics;
    private FileDescriptorMetrics fileDescriptorMetrics;
    
    public JvmMetricsCollector(MeterRegistryManager meterRegistryManager, boolean enabled) {
        this.meterRegistryManager = meterRegistryManager;
        this.enabled = enabled;
    }
    
    @PostConstruct
    public void init() {
        if (!enabled) {
            log.info("JVM指标收集已禁用");
            return;
        }
        
        try {
            var registry = meterRegistryManager.getRegistry();
            
            // 注册JVM指标
            jvmMemoryMetrics = new JvmMemoryMetrics();
            jvmMemoryMetrics.bindTo(registry);
            
            jvmGcMetrics = new JvmGcMetrics();
            jvmGcMetrics.bindTo(registry);
            
            jvmThreadMetrics = new JvmThreadMetrics();
            jvmThreadMetrics.bindTo(registry);
            
            classLoaderMetrics = new ClassLoaderMetrics();
            classLoaderMetrics.bindTo(registry);
            
            jvmCompilationMetrics = new JvmCompilationMetrics();
            jvmCompilationMetrics.bindTo(registry);
            
            // 注册系统指标
            processorMetrics = new ProcessorMetrics();
            processorMetrics.bindTo(registry);
            
            uptimeMetrics = new UptimeMetrics();
            uptimeMetrics.bindTo(registry);
            
            fileDescriptorMetrics = new FileDescriptorMetrics();
            fileDescriptorMetrics.bindTo(registry);
            
            // 注册自定义指标
            registerCustomMetrics(registry);
            
            log.info("JVM指标收集器初始化成功");
        } catch (Exception e) {
            log.error("JVM指标收集器初始化失败", e);
        }
    }
    
    /**
     * 注册自定义JVM相关指标
     */
    private void registerCustomMetrics(PrometheusMeterRegistry registry) {
        // 堆内存使用率
        meterRegistryManager.gauge("jvm.memory.heap.usage.ratio", 
                "堆内存使用率", 
                this, 
                collector -> {
                    Runtime runtime = Runtime.getRuntime();
                    return (double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.maxMemory();
                },
                "type", "heap");
        
        // 活跃线程数
        meterRegistryManager.gauge("jvm.threads.active", 
                "活跃线程数", 
                Thread.class, 
                cls -> (double) Thread.activeCount(),
                "type", "active");
        
        // 可用处理器数
        meterRegistryManager.gauge("system.cpu.count", 
                "可用处理器数", 
                Runtime.class, 
                runtime -> (double) Runtime.getRuntime().availableProcessors(),
                "type", "available");
    }
    
    @PreDestroy
    public void destroy() {
        if (!enabled) {
            return;
        }
        
        log.info("JVM指标收集器正在关闭");
        
        // 清理资源
        try {
            if (jvmGcMetrics != null) {
                jvmGcMetrics.close();
            }
        } catch (Exception e) {
            log.warn("关闭JVM GC指标时发生异常", e);
        }
    }
    
    /**
     * 手动触发GC指标更新
     */
    public void forceGcMetricsUpdate() {
        if (enabled && jvmGcMetrics != null) {
            // Micrometer会自动更新，这里可以添加自定义逻辑
            log.debug("触发GC指标更新");
        }
    }
    
    /**
     * 检查JVM指标收集器是否启用
     */
    public boolean isEnabled() {
        return enabled;
    }
} 