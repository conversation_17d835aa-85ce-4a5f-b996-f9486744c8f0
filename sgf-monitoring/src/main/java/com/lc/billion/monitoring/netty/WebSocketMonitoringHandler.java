package com.lc.billion.monitoring.netty;

import com.lc.billion.monitoring.metrics.WebSocketMetricsCollector;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.websocketx.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * WebSocket监控处理器
 * <p>
 * 无侵入地监控WebSocket连接、消息传输和处理性能
 * 专为游戏服务器的高频消息场景优化
 * </p>
 *
 */
public class WebSocketMonitoringHandler extends ChannelDuplexHandler {
    
    private static final Logger log = LoggerFactory.getLogger(WebSocketMonitoringHandler.class);
    
    private final WebSocketMetricsCollector metricsCollector;
    
    // 性能优化：缓存frequently accessed data
    private volatile boolean isWebSocketConnected = false;
    private volatile long handshakeStartTime = 0;
    
    public WebSocketMonitoringHandler(WebSocketMetricsCollector metricsCollector) {
        this.metricsCollector = metricsCollector;
    }
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        if (metricsCollector.isEnabled()) {
            handshakeStartTime = System.nanoTime();
            metricsCollector.recordConnection(ctx);
        }
        super.channelActive(ctx);
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        if (metricsCollector.isEnabled()) {
            metricsCollector.recordDisconnection(ctx);
        }
        super.channelInactive(ctx);
    }
    
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (!metricsCollector.isEnabled()) {
            super.channelRead(ctx, msg);
            return;
        }
        
        long messageStartTime = System.nanoTime();
        
        try {
            // 处理WebSocket握手
            if (msg instanceof FullHttpRequest) {
                handleHttpRequest(ctx, (FullHttpRequest) msg);
            }
            // 处理WebSocket帧
            else if (msg instanceof WebSocketFrame) {
                handleWebSocketFrame(ctx, (WebSocketFrame) msg, messageStartTime);
            }
            // 处理二进制数据 (游戏协议消息)
            else if (msg instanceof ByteBuf) {
                handleBinaryMessage(ctx, (ByteBuf) msg, messageStartTime);
            }
            
            super.channelRead(ctx, msg);
            
        } catch (Exception e) {
            // 记录处理错误
            metricsCollector.recordMessageError(-1, e.getClass().getSimpleName());
            throw e;
        }
    }
    
    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        if (!metricsCollector.isEnabled()) {
            super.write(ctx, msg, promise);
            return;
        }
        
        try {
            // 监控发送的WebSocket帧
            if (msg instanceof WebSocketFrame) {
                handleOutboundWebSocketFrame((WebSocketFrame) msg);
            }
            // 监控发送的二进制数据
            else if (msg instanceof ByteBuf) {
                handleOutboundBinaryMessage((ByteBuf) msg);
            }
            
            super.write(ctx, msg, promise);
            
        } catch (Exception e) {
            metricsCollector.recordConnectionError("write_error");
            throw e;
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        if (metricsCollector.isEnabled()) {
            String errorType = cause.getClass().getSimpleName();
            
            if (!isWebSocketConnected && handshakeStartTime > 0) {
                // 握手阶段的错误
                metricsCollector.recordHandshakeError(errorType);
            } else {
                // 连接或消息处理错误
                metricsCollector.recordConnectionError(errorType);
            }
        }
        
        super.exceptionCaught(ctx, cause);
    }
    
    /**
     * 处理HTTP请求（WebSocket握手）
     */
    private void handleHttpRequest(ChannelHandlerContext ctx, FullHttpRequest request) {
        // 检查是否是WebSocket升级请求
        String connection = request.headers().get("Connection");
        String upgrade = request.headers().get("Upgrade");
        
        if ("Upgrade".equalsIgnoreCase(connection) && "websocket".equalsIgnoreCase(upgrade)) {
            // WebSocket握手开始
            log.debug("WebSocket握手开始: {}", ctx.channel().id().asShortText());
        }
    }
    
    /**
     * 处理WebSocket帧
     */
    private void handleWebSocketFrame(ChannelHandlerContext ctx, WebSocketFrame frame, long startTime) {
        if (frame instanceof BinaryWebSocketFrame) {
            // 处理二进制帧（游戏消息）
            BinaryWebSocketFrame binaryFrame = (BinaryWebSocketFrame) frame;
            int messageSize = binaryFrame.content().readableBytes();
            
            // 记录消息接收（这里使用通用类型，具体类型由业务层决定）
            metricsCollector.recordMessageReceived(0, messageSize);
            
            // 标记WebSocket连接已建立
            if (!isWebSocketConnected) {
                isWebSocketConnected = true;
                if (handshakeStartTime > 0) {
                    long handshakeDuration = startTime - handshakeStartTime;
                    metricsCollector.recordHandshakeTime(handshakeDuration, TimeUnit.NANOSECONDS);
                }
            }
            
        } else if (frame instanceof TextWebSocketFrame) {
            // 处理文本帧（通常不用于游戏协议）
            TextWebSocketFrame textFrame = (TextWebSocketFrame) frame;
            int messageSize = textFrame.content().readableBytes();
            metricsCollector.recordMessageReceived(-1, messageSize); // -1表示文本消息
            
        } else if (frame instanceof CloseWebSocketFrame) {
            // 处理关闭帧
            metricsCollector.recordDisconnection(ctx);
            
        } else if (frame instanceof PingWebSocketFrame || frame instanceof PongWebSocketFrame) {
            // 处理心跳帧 - 这里可以记录心跳统计
            int messageSize = frame.content().readableBytes();
            metricsCollector.recordMessageReceived(-2, messageSize); // -2表示心跳消息
        }
    }
    
    /**
     * 处理二进制消息（游戏协议）
     */
    private void handleBinaryMessage(ChannelHandlerContext ctx, ByteBuf buffer, long startTime) {
        int messageSize = buffer.readableBytes();
        
        // 尝试从buffer中提取消息类型（如果可能）
        int messageType = extractMessageType(buffer);
        
        metricsCollector.recordMessageReceived(messageType, messageSize);
        
        // 为消息处理计时做准备（这会在业务处理完成后记录）
        ctx.channel().attr(MESSAGE_START_TIME_KEY).set(startTime);
        ctx.channel().attr(MESSAGE_TYPE_KEY).set(messageType);
    }
    
    /**
     * 处理发送的WebSocket帧
     */
    private void handleOutboundWebSocketFrame(WebSocketFrame frame) {
        if (frame instanceof BinaryWebSocketFrame) {
            BinaryWebSocketFrame binaryFrame = (BinaryWebSocketFrame) frame;
            int messageSize = binaryFrame.content().readableBytes();
            metricsCollector.recordMessageSent(0, messageSize);
            
        } else if (frame instanceof TextWebSocketFrame) {
            TextWebSocketFrame textFrame = (TextWebSocketFrame) frame;
            int messageSize = textFrame.content().readableBytes();
            metricsCollector.recordMessageSent(-1, messageSize);
        }
    }
    
    /**
     * 处理发送的二进制消息
     */
    private void handleOutboundBinaryMessage(ByteBuf buffer) {
        int messageSize = buffer.readableBytes();
        int messageType = extractMessageType(buffer);
        
        metricsCollector.recordMessageSent(messageType, messageSize);
    }
    
    /**
     * 从ByteBuf中提取消息类型
     * 这个方法需要根据具体的游戏协议格式来实现
     */
    private int extractMessageType(ByteBuf buffer) {
        if (buffer.readableBytes() < 8) {
            return -1; // 消息太短，无法确定类型
        }
        
        // 保存当前读取位置
        int readerIndex = buffer.readerIndex();
        
        try {
            // 根据游戏协议格式提取消息类型
            // 这里假设消息格式是: [flag:2][sequence:2][type:2][length:4][body:...]
            buffer.skipBytes(4); // 跳过flag和sequence
            int messageType = buffer.readUnsignedShort();
            return messageType;
        } catch (Exception e) {
            log.debug("无法提取消息类型", e);
            return -1;
        } finally {
            // 恢复读取位置
            buffer.readerIndex(readerIndex);
        }
    }
    
    // 用于在Channel中存储消息处理开始时间和类型的键
    private static final io.netty.util.AttributeKey<Long> MESSAGE_START_TIME_KEY = 
            io.netty.util.AttributeKey.valueOf("monitoring.message.startTime");
    private static final io.netty.util.AttributeKey<Integer> MESSAGE_TYPE_KEY = 
            io.netty.util.AttributeKey.valueOf("monitoring.message.type");
    
    /**
     * 记录消息处理完成（由业务层调用）
     */
    public static void recordMessageProcessingComplete(ChannelHandlerContext ctx, 
                                                      WebSocketMetricsCollector metricsCollector) {
        if (!metricsCollector.isEnabled()) {
            return;
        }
        
        Long startTime = ctx.channel().attr(MESSAGE_START_TIME_KEY).getAndSet(null);
        Integer messageType = ctx.channel().attr(MESSAGE_TYPE_KEY).getAndSet(null);
        
        if (startTime != null && messageType != null) {
            long duration = System.nanoTime() - startTime;
            metricsCollector.recordMessageProcessingTime(messageType, duration, TimeUnit.NANOSECONDS);
        }
    }
} 