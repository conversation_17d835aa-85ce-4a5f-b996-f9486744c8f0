package com.lc.billion.monitoring.annotation;

import java.lang.annotation.*;

/**
 * 方法执行时间监控注解
 * <p>
 * 用于监控方法执行时间，支持百分位数统计
 * </p>
 *
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Timed {
    
    /**
     * 指标名称
     */
    String name() default "";
    
    /**
     * 指标描述
     */
    String description() default "";
    
    /**
     * 是否启用百分位数直方图
     */
    boolean percentiles() default true;
    
    /**
     * 额外的标签
     */
    String[] extraTags() default {};
    
    /**
     * 是否长时间运行的任务
     */
    boolean longTask() default false;
} 