package com.lc.billion.monitoring.sampling;

/**
 * 采样策略接口
 * <p>
 * 定义不同的采样算法，用于在高频场景下降低监控开销
 * </p>
 */
public interface SamplingStrategy {
    
    /**
     * 判断是否应该进行采样
     * 
     * @param metricName 指标名称
     * @param tags 指标标签
     * @return true表示应该采样，false表示跳过
     */
    boolean shouldSample(String metricName, String... tags);
    
    /**
     * 重置采样状态（如果需要）
     */
    default void reset() {
        // 默认不需要重置
    }
    
    /**
     * 获取采样策略名称
     */
    String getStrategyName();
} 