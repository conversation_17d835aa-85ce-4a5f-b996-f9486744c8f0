package com.lc.billion.monitoring.metrics;

import com.lc.billion.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * WebSocket指标收集器
 * <p>
 * 专为游戏WebSocket协议设计的高性能指标收集器
 * 监控连接数、消息处理、数据传输等关键指标
 * </p>
 *
 */
public class WebSocketMetricsCollector {
    
    private static final Logger log = LoggerFactory.getLogger(WebSocketMetricsCollector.class);
    
    private final MeterRegistryManager meterRegistryManager;
    private final boolean enabled;
    
    // 连接相关指标
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong totalDisconnections = new AtomicLong(0);
    
    // 消息相关指标
    private final AtomicLong messagesReceived = new AtomicLong(0);
    private final AtomicLong messagesSent = new AtomicLong(0);
    private final AtomicLong bytesReceived = new AtomicLong(0);
    private final AtomicLong bytesSent = new AtomicLong(0);
    
    // 错误相关指标
    private final AtomicLong handshakeErrors = new AtomicLong(0);
    private final AtomicLong messageErrors = new AtomicLong(0);
    private final AtomicLong connectionErrors = new AtomicLong(0);
    
    // Micrometer指标
    private Counter connectionsTotal;
    private Counter disconnectionsTotal;
    private Counter messagesReceivedTotal;
    private Counter messagesSentTotal;
    private Counter bytesReceivedTotal;
    private Counter bytesSentTotal;
    private Counter handshakeErrorsTotal;
    private Counter messageErrorsTotal;
    private Counter connectionErrorsTotal;
    private Timer messageProcessingTime;
    private Timer handshakeTime;
    
    // 连接状态追踪
    private final ConcurrentHashMap<String, Long> connectionTimes = new ConcurrentHashMap<>();
    
    public WebSocketMetricsCollector(MeterRegistryManager meterRegistryManager, boolean enabled) {
        this.meterRegistryManager = meterRegistryManager;
        this.enabled = enabled;
    }
    
    @PostConstruct
    public void init() {
        if (!enabled) {
            log.info("WebSocket指标收集已禁用");
            return;
        }
        
        try {
            // 创建计数器
            connectionsTotal = meterRegistryManager.counter("websocket.connections.total", 
                    "WebSocket连接总数");
            
            disconnectionsTotal = meterRegistryManager.counter("websocket.disconnections.total", 
                    "WebSocket断开连接总数");
            
            messagesReceivedTotal = meterRegistryManager.counter("websocket.messages.received.total", 
                    "接收到的WebSocket消息总数");
            
            messagesSentTotal = meterRegistryManager.counter("websocket.messages.sent.total", 
                    "发送的WebSocket消息总数");
            
            bytesReceivedTotal = meterRegistryManager.counter("websocket.bytes.received.total", 
                    "接收的字节总数");
            
            bytesSentTotal = meterRegistryManager.counter("websocket.bytes.sent.total", 
                    "发送的字节总数");
            
            handshakeErrorsTotal = meterRegistryManager.counter("websocket.handshake.errors.total", 
                    "WebSocket握手错误总数");
            
            messageErrorsTotal = meterRegistryManager.counter("websocket.message.errors.total", 
                    "消息处理错误总数");
            
            connectionErrorsTotal = meterRegistryManager.counter("websocket.connection.errors.total", 
                    "连接错误总数");
            
            // 创建计时器
            messageProcessingTime = meterRegistryManager.timer("websocket.message.processing.time",
                    "WebSocket消息处理时间");

            handshakeTime = meterRegistryManager.timer("websocket.handshake.time",
                    "WebSocket握手时间");

            // 创建仪表
            meterRegistryManager.gauge("websocket.connections.active",
                    "当前活跃的WebSocket连接数",
                    activeConnections,
                    AtomicLong::get);

            log.info("WebSocket指标收集器初始化成功");
        } catch (Exception e) {
            log.error("WebSocket指标收集器初始化失败", e);
        }
    }
    
    /**
     * 记录新连接
     */
    public void recordConnection(ChannelHandlerContext ctx) {
        if (!enabled) return;
        
        meterRegistryManager.safeExecute(() -> {
            String channelId = ctx.channel().id().asShortText();
            
            activeConnections.incrementAndGet();
            totalConnections.incrementAndGet();
            connectionsTotal.increment();
            
            // 记录连接时间
            connectionTimes.put(channelId, System.currentTimeMillis());
            
            log.debug("记录新WebSocket连接: {}, 当前活跃连接数: {}", 
                    channelId, activeConnections.get());
        });
    }
    
    /**
     * 记录连接断开
     */
    public void recordDisconnection(ChannelHandlerContext ctx) {
        if (!enabled) return;
        
        meterRegistryManager.safeExecute(() -> {
            String channelId = ctx.channel().id().asShortText();
            
            activeConnections.decrementAndGet();
            totalDisconnections.incrementAndGet();
            disconnectionsTotal.increment();
            
            // 计算连接持续时间
            Long connectTime = connectionTimes.remove(channelId);
            if (connectTime != null) {
                long duration = System.currentTimeMillis() - connectTime;
                meterRegistryManager.recordValue("websocket.connection.duration", 
                        duration, "unit", "milliseconds");
            }
            
            log.debug("记录WebSocket连接断开: {}, 当前活跃连接数: {}", 
                    channelId, activeConnections.get());
        });
    }
    
    /**
     * 记录消息接收
     */
    public void recordMessageReceived(int messageType, int messageSize) {
        if (!enabled) return;
        
        meterRegistryManager.safeExecute(() -> {
            messagesReceived.incrementAndGet();
            bytesReceived.addAndGet(messageSize);
            
            messagesReceivedTotal.increment();

            // 使用MeterRegistryManager记录带标签的指标
            meterRegistryManager.incrementCounter("websocket.messages.received.by.type",
                    "type", String.valueOf(messageType));

            bytesReceivedTotal.increment(messageSize);

            // 使用MeterRegistryManager记录带标签的字节数指标
            meterRegistryManager.recordValue("websocket.bytes.received.by.type", messageSize,
                    "type", String.valueOf(messageType));
        });
    }
    
    /**
     * 记录消息发送
     */
    public void recordMessageSent(int messageType, int messageSize) {
        if (!enabled) return;
        
        meterRegistryManager.safeExecute(() -> {
            messagesSent.incrementAndGet();
            bytesSent.addAndGet(messageSize);
            
            messagesSentTotal.increment();

            // 使用MeterRegistryManager记录带标签的指标
            meterRegistryManager.incrementCounter("websocket.messages.sent.by.type",
                    "type", String.valueOf(messageType));

            bytesSentTotal.increment(messageSize);

            // 使用MeterRegistryManager记录带标签的字节数指标
            meterRegistryManager.recordValue("websocket.bytes.sent.by.type", messageSize,
                    "type", String.valueOf(messageType));
        });
    }
    
    /**
     * 记录消息处理时间
     */
    public void recordMessageProcessingTime(int messageType, long duration, TimeUnit unit) {
        if (!enabled) return;
        
        meterRegistryManager.safeExecute(() -> {
            messageProcessingTime.record(duration, unit);
            meterRegistryManager.recordTimer("websocket.message.processing.time.by.type", 
                    duration, unit, "type", String.valueOf(messageType));
        });
    }
    
    /**
     * 记录握手时间
     */
    public void recordHandshakeTime(long duration, TimeUnit unit) {
        if (!enabled) return;
        
        meterRegistryManager.safeExecute(() -> {
            handshakeTime.record(duration, unit);
        });
    }
    
    /**
     * 记录握手错误
     */
    public void recordHandshakeError(String errorType) {
        if (!enabled) return;
        
        meterRegistryManager.safeExecute(() -> {
            handshakeErrors.incrementAndGet();
            handshakeErrorsTotal.increment();

            // 使用MeterRegistryManager记录带标签的指标
            meterRegistryManager.incrementCounter("websocket.handshake.errors.by.type",
                    "error_type", errorType);
        });
    }
    
    /**
     * 记录消息错误
     */
    public void recordMessageError(int messageType, String errorType) {
        if (!enabled) return;
        
        meterRegistryManager.safeExecute(() -> {
            messageErrors.incrementAndGet();
            messageErrorsTotal.increment();

            // 使用MeterRegistryManager记录带标签的指标
            meterRegistryManager.incrementCounter("websocket.message.errors.by.type",
                    "message_type", String.valueOf(messageType),
                    "error_type", errorType);
        });
    }
    
    /**
     * 记录连接错误
     */
    public void recordConnectionError(String errorType) {
        if (!enabled) return;
        
        meterRegistryManager.safeExecute(() -> {
            connectionErrors.incrementAndGet();
            connectionErrorsTotal.increment();

            // 使用MeterRegistryManager记录带标签的指标
            meterRegistryManager.incrementCounter("websocket.connection.errors.by.type",
                    "error_type", errorType);
        });
    }
    
    /**
     * 获取当前活跃连接数
     */
    public long getActiveConnections() {
        return activeConnections.get();
    }
    
    /**
     * 获取总连接数
     */
    public long getTotalConnections() {
        return totalConnections.get();
    }
    
    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return enabled;
    }
} 