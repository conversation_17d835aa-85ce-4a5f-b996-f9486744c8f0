package com.lc.billion.monitoring.config;

import com.lc.billion.monitoring.aop.CountedAspect;
import com.lc.billion.monitoring.aop.TimedAspect;
import com.lc.billion.monitoring.exporter.PrometheusExporter;
import com.lc.billion.monitoring.metrics.JvmMetricsCollector;
import com.lc.billion.monitoring.metrics.WebSocketMetricsCollector;
import com.lc.billion.monitoring.registry.MeterRegistryManager;
import com.lc.billion.monitoring.sampling.AdaptiveSamplingStrategy;
import com.lc.billion.monitoring.sampling.FixedRateSamplingStrategy;
import com.lc.billion.monitoring.sampling.SamplingStrategy;
import io.micrometer.prometheusmetrics.PrometheusConfig;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.Environment;

import java.util.Optional;

/**
 * 监控模块配置类
 * <p>
 * 自动配置所有监控组件
 * </p>
 *
 */
@Configuration
@EnableAspectJAutoProxy
public class MonitoringConfiguration {

    /**
     * Prometheus注册表
     */
    @Bean
    public PrometheusMeterRegistry prometheusMeterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }

    /**
     * 采样策略
     */
    @Bean
    public SamplingStrategy samplingStrategy(Environment environment) {
        boolean samplingEnabled = Boolean.parseBoolean(
                environment.getProperty("monitoring.sampling.enabled", "false"));
        
        if (!samplingEnabled) {
            return null; // 禁用采样
        }
        
        String strategy = environment.getProperty("monitoring.sampling.strategy", "fixed");
        
        switch (strategy.toLowerCase()) {
            case "fixed":
                double fixedRate = Double.parseDouble(
                        environment.getProperty("monitoring.sampling.fixed.rate", "0.1"));
                return new FixedRateSamplingStrategy(fixedRate);
                
            case "adaptive":
                double baseRate = Double.parseDouble(
                        environment.getProperty("monitoring.sampling.adaptive.base.rate", "0.1"));
                long windowSize = Long.parseLong(
                        environment.getProperty("monitoring.sampling.adaptive.window.size.ms", "60000"));
                long threshold = Long.parseLong(
                        environment.getProperty("monitoring.sampling.adaptive.high.frequency.threshold", "100"));
                double minRate = Double.parseDouble(
                        environment.getProperty("monitoring.sampling.adaptive.min.rate", "0.01"));
                double maxRate = Double.parseDouble(
                        environment.getProperty("monitoring.sampling.adaptive.max.rate", "0.5"));
                
                return new AdaptiveSamplingStrategy(baseRate, windowSize, threshold, minRate, maxRate);
                
            default:
                throw new IllegalArgumentException("不支持的采样策略: " + strategy);
        }
    }
    
    /**
     * 注册表管理器
     */
    @Bean
    public MeterRegistryManager meterRegistryManager(PrometheusMeterRegistry prometheusMeterRegistry,
                                                     Environment environment,
                                                     SamplingStrategy samplingStrategy) {
        String applicationName = Optional.ofNullable(environment.getProperty("monitoring.application.name"))
                .orElse(environment.getProperty("spring.application.name", "unknown"));
        
        return new MeterRegistryManager(prometheusMeterRegistry, applicationName, samplingStrategy);
    }

    /**
     * JVM指标收集器
     */
    @Bean
    public JvmMetricsCollector jvmMetricsCollector(MeterRegistryManager meterRegistryManager,
                                                   Environment environment) {
        boolean enableJvm = Boolean.parseBoolean(
                environment.getProperty("monitoring.jvm.enabled", "true"));
        
        return new JvmMetricsCollector(meterRegistryManager, enableJvm);
    }

    /**
     * WebSocket指标收集器
     */
    @Bean
    public WebSocketMetricsCollector webSocketMetricsCollector(MeterRegistryManager meterRegistryManager,
                                                               Environment environment) {
        boolean enableWebSocket = Boolean.parseBoolean(
                environment.getProperty("monitoring.websocket.enabled", "true"));
        
        return new WebSocketMetricsCollector(meterRegistryManager, enableWebSocket);
    }

    /**
     * 计时切面
     */
    @Bean
    public TimedAspect timedAspect(MeterRegistryManager meterRegistryManager) {
        return new TimedAspect(meterRegistryManager);
    }

    /**
     * 计数切面
     */
    @Bean
    public CountedAspect countedAspect(MeterRegistryManager meterRegistryManager) {
        return new CountedAspect(meterRegistryManager);
    }

    /**
     * Prometheus导出器
     */
    @Bean
    public PrometheusExporter prometheusExporter(PrometheusMeterRegistry prometheusMeterRegistry,
                                                 Environment environment) {
        int port = Integer.parseInt(environment.getProperty("monitoring.prometheus.port", "9090"));
        String path = environment.getProperty("monitoring.prometheus.path", "/metrics");
        String host = environment.getProperty("monitoring.prometheus.host");

        return new PrometheusExporter(prometheusMeterRegistry, port, path, host);
    }
} 