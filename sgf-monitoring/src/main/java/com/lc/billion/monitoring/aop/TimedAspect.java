package com.lc.billion.monitoring.aop;

import com.lc.billion.monitoring.annotation.Timed;
import com.lc.billion.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.LongTaskTimer;
import io.micrometer.core.instrument.Timer;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 计时监控切面
 * <p>
 * 处理@Timed注解的方法，记录执行时间
 * 使用异步记录避免影响游戏性能
 * </p>
 *
 */
@Aspect
@Order(1) // 高优先级，确保监控不被其他切面干扰
public class TimedAspect {
    
    private static final Logger log = LoggerFactory.getLogger(TimedAspect.class);
    
    private final MeterRegistryManager meterRegistryManager;
    
    public TimedAspect(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }
    
    /**
     * 处理方法级别的@Timed注解
     */
    @Around("@annotation(timed)")
    public Object timeMethod(ProceedingJoinPoint joinPoint, Timed timed) throws Throwable {
        return processTimedExecution(joinPoint, timed);
    }
    
    /**
     * 处理类级别的@Timed注解
     */
    @Around("@within(timed) && execution(public * *(..))")
    public Object timeClass(ProceedingJoinPoint joinPoint, Timed timed) throws Throwable {
        // 检查方法是否有自己的@Timed注解，如果有则跳过
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        if (method.isAnnotationPresent(Timed.class)) {
            return joinPoint.proceed();
        }
        
        return processTimedExecution(joinPoint, timed);
    }
    
    /**
     * 处理计时执行逻辑
     */
    private Object processTimedExecution(ProceedingJoinPoint joinPoint, Timed timed) throws Throwable {
        String metricName = buildMetricName(joinPoint, timed);
        String description = timed.description().isEmpty() ? 
                "执行时间: " + joinPoint.getSignature().toShortString() : timed.description();
        
        if (timed.longTask()) {
            return processLongTaskTiming(joinPoint, metricName, description, timed);
        } else {
            return processRegularTiming(joinPoint, metricName, description, timed);
        }
    }
    
    /**
     * 处理常规计时
     */
    private Object processRegularTiming(ProceedingJoinPoint joinPoint, String metricName, 
                                      String description, Timed timed) throws Throwable {
        long startTime = System.nanoTime();
        boolean success = false;
        Throwable exception = null;
        
        try {
            Object result = joinPoint.proceed();
            success = true;
            return result;
        } catch (Throwable t) {
            exception = t;
            throw t;
        } finally {
            long endTime = System.nanoTime();
            long duration = endTime - startTime;
            
            // 异步记录指标，避免影响业务性能
            recordTimingMetrics(metricName, description, duration, success, exception, timed);
        }
    }
    
    /**
     * 处理长任务计时
     */
    private Object processLongTaskTiming(ProceedingJoinPoint joinPoint, String metricName, 
                                       String description, Timed timed) throws Throwable {
        LongTaskTimer.Sample sample = null;
        
        try {
            // 开始长任务计时
            LongTaskTimer longTaskTimer = meterRegistryManager.longTaskTimer(
                    metricName, description, buildTags(timed));
            sample = longTaskTimer.start();
            
            return joinPoint.proceed();
        } finally {
            if (sample != null) {
                sample.stop();
            }
        }
    }
    
    /**
     * 异步记录计时指标
     */
    private void recordTimingMetrics(String metricName, String description, long durationNanos, 
                                   boolean success, Throwable exception, Timed timed) {
        // 使用安全执行包装，确保监控错误不影响业务
        meterRegistryManager.safeExecute(() -> {
            String[] tags = buildTags(timed, success, exception);
            
            // 记录执行时间
            Timer timer = meterRegistryManager.timer(metricName, description, tags);
            timer.record(durationNanos, TimeUnit.NANOSECONDS);
            
            // 如果启用百分位数，记录额外的统计信息
            if (timed.percentiles()) {
                meterRegistryManager.recordTimer(metricName + ".detailed", 
                        durationNanos, TimeUnit.NANOSECONDS, tags);
            }
            
            // 记录成功/失败统计
            String resultTag = success ? "success" : "failure";
            String[] resultTags = {"result", resultTag};
            meterRegistryManager.incrementCounter(metricName + ".executions",
                    combineArrays(tags, resultTags));

            // 如果有异常，记录异常类型
            if (exception != null) {
                String[] exceptionTags = {"exception", exception.getClass().getSimpleName()};
                meterRegistryManager.incrementCounter(metricName + ".exceptions",
                        combineArrays(tags, exceptionTags));
            }
        });
    }
    
    /**
     * 构建指标名称
     */
    private String buildMetricName(ProceedingJoinPoint joinPoint, Timed timed) {
        if (!timed.name().isEmpty()) {
            return timed.name();
        }
        
        // 自动生成指标名称
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        return String.format("method.execution.time.%s.%s", 
                className.toLowerCase(), methodName);
    }
    
    /**
     * 构建标签数组
     */
    private String[] buildTags(Timed timed) {
        return timed.extraTags();
    }
    
    /**
     * 构建包含执行结果的标签数组
     */
    private String[] buildTags(Timed timed, boolean success, Throwable exception) {
        String[] extraTags = timed.extraTags();
        String[] resultTags = {"result", success ? "success" : "failure"};
        
        if (exception != null) {
            String[] exceptionTags = {"exception_type", exception.getClass().getSimpleName()};
            return combineArrays(extraTags, resultTags, exceptionTags);
        }
        
        return combineArrays(extraTags, resultTags);
    }
    
    /**
     * 合并多个数组
     */
    private String[] combineArrays(String[]... arrays) {
        int totalLength = 0;
        for (String[] array : arrays) {
            totalLength += array.length;
        }
        
        String[] result = new String[totalLength];
        int index = 0;
        
        for (String[] array : arrays) {
            System.arraycopy(array, 0, result, index, array.length);
            index += array.length;
        }
        
        return result;
    }
} 