package com.simfun.sgf.compat;

/**
 * 工作器状态枚举
 */
public enum WorkerState {
    INIT("Worker initialized but not started"),
    START("Worker is running"),
    STOP("Worker has been stopped");

    private final String description;

    WorkerState(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public boolean isRunning() {
        return this == START;
    }

    public boolean canAcceptTasks() {
        return this == START;
    }
} 