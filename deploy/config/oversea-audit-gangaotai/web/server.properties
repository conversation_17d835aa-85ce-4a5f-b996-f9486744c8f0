# meta dir
config.fileDir=webapps/ROOT/meta

geolpdata=webapps/ROOT/data/GeoLite2-Country.mmdb
ip2regiondata=webapps/ROOT/data/ip2region.xdb

## dev:/data/tomcat/webapps/head/
uploadhead=../uploadhead/
uploadkey=uug0VpN1bvPlXpc4lFqE9IT8bQcpR9



# check ignore
# 是否验证签名
# 开发环境测试充值无需验签
checkIgnore=false

# google\u652F\u4ED8\u65B0\u9A8C\u8BC1\u65B9\u5F0F
googlePayNewValidator=true

#google包名列表 name1,name2,name3.....
#一定要与下面map一一对应，否则会有问题
googlePackageList=com.lc.lssaw.gp,com.lc.lssaw.gpvn

#google包名对应账号ID映射
googleAccountMap={'com.lc.lssaw.gp':'<EMAIL>','com.lc.lssaw.gpvn':'<EMAIL>'}
#google包名对应P12文件
googleP12Map={'com.lc.lssaw.gp':'../data/api-6711335536439627375-271677-980d320ccf45.p12','com.lc.lssaw.gpvn':'../data/pc-api-7763919483800650018-824-a836079a63a9.p12'}

#这个用不上
# apple.store.type
#appleStoreType=sandbox

#apple包名列表 name1,name2,name3.....
#一定要与下面map一一对应，否则会有问题
applePackageList=com.lc.lssaw.apple,com.lc.ls.ios.jkt,com.lc.lssaw.applevn

#apple支付参数Map<packageName, subKey>
appleSubKeyMap={'com.lc.lssaw.apple':'e7d5fca902e142519e376ca261c56dab','com.lc.lssaw.ios.jkt':'47db41ff7a7d4466b271e1b05e731ded','com.lc.lssaw.applevn':'1427c8bafd5443b89bf669c598f23444'}

# notify.config
notifyReceiver=

devtag=true

isForCommandPlayer=false

autoInc=1



#\u9000\u6B3E\u67E5\u8BE2
refundOrderTest=true

#bi\u6253\u70B9\u53C2\u6570
biUrl=
biProd=118000
biTest=true



