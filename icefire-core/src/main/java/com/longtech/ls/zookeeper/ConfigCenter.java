package com.longtech.ls.zookeeper;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.longtech.cod.common.utils.NamedThreadFactory;
import com.longtech.cod.common.utils.OsUtil;
import com.longtech.cod.common.zookeeper.SimpleCurator;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig.Instance;
import com.longtech.ls.zookeeper.GameServerConfig.KingdomInfo;
import com.longtech.ls.zookeeper.GameServerConfig.KvkInstance;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;


/**
 * 读取并缓存ZK中的所有配置信息。
 * 
 * <code>
 * //【与 spring 整合】
 * StaticApplicationContext parent = new StaticApplicationContext();
 * parent.getBeanFactory().registerSingleton("configCenter", ServerConfigManager.getInstance().getConfigCenter());
 * parent.refresh();
 * ApplicationContext applicationContext = new ClassPathXmlApplicationContext(new String[] {"spring/applicationContext*.xml"}, true, parent);
 * 
 * applicationContext.xml
 * 	<bean id="redisClient" class=
"com.lc.billion.icefire.game.biz.redis.JedisClient">
 * 		<property name="host" value=
"#{configCenter.currentGameServerConfig.redis.host}" />
 * 		<property name="port" value=
"#{configCenter.currentGameServerConfig.redis.port}" />
 * 		<property name="password" value=
"#{configCenter.currentGameServerConfig.redis.password}" />
 * 		<property name="timeout" value="30000" />
 * 		<property name="maxTotal" value="50" />
 * 		<property name="maxIdle" value="5" />
 * 		<property name="mxWaitMillis" value="5000" />
 * 		<property name="dbindex" value="0" />
 * 	</bean>
 * </code>
 * 
 * <code>
 * //【与 spring web 整合】
 *  <bean id="propertyConfigurer" class=
"org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
 *     <property name="locations">
 *         <list>
 *             <value>/WEB-INF/config/server.properties</value>
 *             <value>/WEB-INF/config/platform.properties</value>
 *             <value>/WEB-INF/config/zookeeper.properties</value>
 *         </list>
 *     </property>
 *  </bean>
 * 	<bean id="configCenter"
 * 		class="com.longtech.ls.zookeeper.ConfigCenter">
 * 		<constructor-arg name="zkConnectString" value="${zk.connectString}" />
 * 		<constructor-arg name="base" value="${zk.configPath}" />
 * 	</bean>
 *  <!-- cache 配置,存在冗余属性，选择jedis时，codis的属性无效；同理，选择codis时，jedis的属性无效 -->
 * 	<bean id="redisClient" class=
"com.lc.billion.icefire.game.biz.redis.JedisClient">
 * 		<property name="host" value=
"#{configCenter.LsConfig.webServer.redis.host}" />
 * 		<property name="port" value=
"#{configCenter.LsConfig.webServer.redis.port}" />
 * 		<property name="password" value=
"#{configCenter.LsConfig.webServer.redis.password}" />
 * 		<property name="timeout" value="30000" />
 * 		<property name="maxTotal" value="50" />
 * 		<property name="maxIdle" value="5" />
 * 		<property name="mxWaitMillis" value="5000" />
 * 		<property name="dbindex" value="0" />
 * 	</bean>
 * </code>
 * 
 * <AUTHOR>
 *
 */
public class ConfigCenter {

	private static final Logger logger = LoggerFactory.getLogger(ConfigCenter.class);

	private boolean devKvk;

	public boolean isDevKvk() {
		return devKvk;
	}

	public ConfigCenter() {
	}

	//
	// GameServerConfigListener 管理
	//

	public boolean addGameServerConfigListener(IZkGameServerConfigListener l) {
		return config.addGameServerConfigListener(l);
	}

	public boolean removeGameServerConfigListener(IZkGameServerConfigListener l) {
		return config.removeGameServerConfigListener(l);
	}

	public void clearGameServerConfigListener() {
		config.clearGameServerConfigListener();
	}

	public ConfigCenter(String envKey_GameServerId,String envKey_ZkPath,String envKey_ZkUrl, String zkConnectString, String base, IServerTypeDeterminer serverTypeDeterminer) {
		this.currentTimeMillis = System.currentTimeMillis();
		ExecutorService executor = Executors.newFixedThreadPool(1, new NamedThreadFactory("ConfigCenter-zookeeper-"));

		if(JavaUtils.bool(envKey_ZkPath)){
			var envPath = getEnvValue(envKey_ZkPath);
			if(JavaUtils.bool(envPath)){
				//替换为环境变量
				base = envPath;
				devKvk = true;
			}
		}

		if(JavaUtils.bool(envKey_ZkUrl)){
			var envUrl = getEnvValue(envKey_ZkUrl);
			if(JavaUtils.bool(envUrl)){
				//替换为环境变量
				zkConnectString = envUrl;
			}
		}

		// 建立zk连接
		zk = new SimpleCurator(zkConnectString, executor);

		this.serverTypeDeterminer = serverTypeDeterminer;

		long start = System.currentTimeMillis();
		config = new LsConfig(zk, base, serverTypeDeterminer);

		// 1）连接ZK，读取所有配置信息，同时获取自己的ServerID
		this.currentGameServerConfig = findCurrentGameServerConfig(envKey_GameServerId, zk);
		logger.info("ZK上的所有配置信息读取完毕 costTime:{}", System.currentTimeMillis() - start);

	}

	private SimpleCurator zk; // ZK客户端

	private LsConfig config; // ZK上的所有配置信息

	private List<String> localhostIpList; // 本机所有网卡的IP地址

	private long currentTimeMillis; // 本次启动的时刻

	private IServerTypeDeterminer serverTypeDeterminer; // 用来判断服务器类型

	private GameServerConfig currentGameServerConfig; // 本进程对应的GameServer配置，如果是WebServer，这个字段应该为null

	/**
	 * 这个只有赛季服才有
	 */
	private int currentKvkSeasonServerGroupConfig_kServerId = -1; // 如果本进程是【KVK赛季服】，并且已经进入第2～N赛季，此字段是对应的当前赛季分组信息。否则为null

	/**
	 * 根据本机IP地址找到ZK上的配置。
	 * 
	 * @param zk
	 */
	private GameServerConfig findCurrentGameServerConfig(String envKeyServerId, SimpleCurator zk) {
		GameServerConfig rtn = findCurrentGameServerConfigByEnv(envKeyServerId);
		rtn = rtn == null ? findCurrentGameServerConfigByIP() : rtn;
		return rtn;
	}

	private String getEnvValue(String key){
		String envServerId = null;
		if (key != null && !key.isEmpty()) {
			envServerId = System.getProperty(key);
		}
		if (envServerId == null) {
			if (!System.getenv().containsKey(key)) {
				return null;
			} else {
				envServerId = System.getenv(key);
			}
		}
		return envServerId;
	}

	private GameServerConfig findCurrentGameServerConfigByEnv(String envKeyServerId) {
		String envServerId = getEnvValue(envKeyServerId);
		if (envServerId == null) {
			return null;
		}
		Integer serverId;
		try {
			serverId = Integer.valueOf(envServerId.trim());
		} catch (NumberFormatException e) {
			logger.warn("LsConfig 从环境变量" + envKeyServerId + "=" + envServerId + "获取ServerID，格式化时报错：" + envServerId, e);
			return null;
		}
		GameServerConfig rtn = config.getGameServers().get(serverId);
		logger.info("LsConfig 从环境变量" + envKeyServerId + "=" + envServerId + "获取CurrentGameServerConfig：" + rtn);
		return rtn;
	}

	private GameServerConfig findCurrentGameServerConfigByIP() {
		localhostIpList = OsUtil.getLocalIpList(true);
		logger.debug("LsConfig本机IpList：" + localhostIpList.toString());

		List<Entry<Integer, GameServerConfig>> reversedSortedByServerId = config.getGameServers().entrySet().stream().sorted(Map.Entry.<Integer, GameServerConfig>comparingByKey().reversed()).collect(Collectors.toList()); // IP 重用后（前面的IP被废弃），所以要从后面查找
		
		for (Entry<Integer, GameServerConfig> e : reversedSortedByServerId) {
			GameServerConfig gameServer = e.getValue();
			if (localhostIpList.contains(gameServer.getRpcIp())) { // 这里只能用RpcIp，RpcBindIp可能都配置成了0.0.0.0。注意这个机制只能适应独立机器的环境，如果GvGBattleServer容器化，此机制就失效了。需要另建立其他机制（比如运维的部署工具在部署时gameserver/conf里建立一个serverId的文件）。
				logger.debug("LsConfig 从IP地址" + gameServer.getRpcIp() + "获取CurrentGameServerConfig：" + gameServer);
				return gameServer;
			}
		}

		return null;
	}

	//
	// 对外提供的便捷方法
	//

	/**
	 * 所有服务器、全部配置
	 * 
	 * @return
	 */
	public LsConfig getLsConfig() {
		return config;
	}

	//
	// for WebServer only
	//

	// 更新单个 GameServer 的配置

	private GameServerConfig findServerConfig(int serverId) {
		GameServerConfig serverConfig = config.getGameServers().get(serverId);
		if (serverConfig == null) {
			throw new RuntimeException("LsConfig无法找到GameServerConfig配置！" + config.toString() + " serverId:" + serverId);
		}
		return serverConfig;
	}

	public void updateGameServerEnable(int serverId, boolean enable) {
		findServerConfig(serverId).setEnable(enable);
	}

	public void updateGameServerInServerList(int serverId, boolean inServerList) {
		findServerConfig(serverId).setInServerList(inServerList);
	}

	public void updateGameServerMultiRoleServerShow(int serverId, boolean multiRoleServerShow) {
		findServerConfig(serverId).setMultiRoleServerShow(multiRoleServerShow);
	}

	// 重启某个GameServer

	public void updateGameServerRestartTimeMs(int serverId, long restartTimeMs) {
		findServerConfig(serverId).setRestartTimeMs(restartTimeMs);
	}

	// 停止某个GameServer

	public void updateGameServerStopTimeMs(int serverId, long stopTimeMs) {
		findServerConfig(serverId).setStopTimeMs(stopTimeMs);
	}

	// 登陆IP白名单

	private NewPlayerConfig findNewPlayerConfig() {
		if (config.getNewPlayer() == null) {
			throw new RuntimeException("LsConfig无法找到NewPlayerConfig配置！" + config.toString());
		}
		return config.getNewPlayer();
	}

	public void updateLoginIpWhiteList(List<String> ips) {
		findNewPlayerConfig().setLoginIpWhiteList(ips);
	}

	public void updateLoginUuidWhiteList(List<String> uuids) {
		findNewPlayerConfig().setLoginUuidWhiteList(uuids);
	}

	public List<String> getLoginIpWhiteList() {
		return findNewPlayerConfig().getLoginIpWhiteList();

	}

	public List<String> getLoginIpInfoWhiteList() {
		return findNewPlayerConfig().getLoginIpInfoWhiteList();

	}

	public List<String> getLoginUuidWhiteList() {
		return findNewPlayerConfig().getLoginUuidWhiteList();
	}

	public List<String> getLoginUuidInfoWhiteList() {
		return findNewPlayerConfig().getLoginUuidInfoWhiteList();
	}

	public boolean isInLoginIpWhiteList(String ip) {
		return findNewPlayerConfig().isInLoginIpWhiteList(ip);

	}

	public boolean isInLoginUuidWhiteList(String uuid) {
		return findNewPlayerConfig().isInLoginUuidWhiteList(uuid);
	}

	public void addLoginUuidWhiteList(String uuid) {
		findNewPlayerConfig().addLoginUuidWhiteList(uuid);
	}

	// 更新导量配置

	public void updateGameServerDiversionConfig(int serverId, int wise, String countrys, String apps, int diversionSwitchI) {
		GameServerConfig gsc = findServerConfig(serverId);
		gsc.getDiversionConfig().setOtherwise(wise);
		gsc.getDiversionConfig().setByAppString(apps);
		gsc.getDiversionConfig().setByCountryString(countrys);
		gsc.setDiversionSwitch(GameServerConfig.DiversionSwitch.values()[diversionSwitchI]);
		gsc.saveDiversionConfig();
	}

	//
	// for GameServer only
	//

	/**
	 * 需要把启动流程从 构造函数中 提取出来，等ConfigCenter对象建立成功后，再调用，避免阻挡Spring初始化过程。
	 */
	public void launch() {

		// 2）通过ServerID范围，判断自己是哪一种服务器。
		// this.currentGameServerType =
		// this.serverTypeDeterminer.getServerType(this.currentGameServerConfig.getGameServerId());
		// 先检测一下是否有进程占用本服务器
		this.checkCurrentGameServer_Instance();

		// 3）
		if (this.currentGameServerConfig.serverType() == ServerType.KVK_SEASON) {
			// 3.A）
			// 对于【赛季服】：根据当前时刻 和
			// ZK中的赛季分组信息（赛季分组信息是【KVK中控服】每天11点根据策划配置表生成）获取本赛季服当前赛季信息：第几赛季、有哪几个原服。
			KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = getKvkSeasonServerGroupConfigByKServerId(this.currentGameServerConfig.getGameServerId(),
					this.currentTimeMillis);
			if (kvkSeasonServerGroupConfig != null) {
				this.currentKvkSeasonServerGroupConfig_kServerId = kvkSeasonServerGroupConfig.getKServerId();
			}
			KvkSeasonServerGroupConfig currentKvkSeasonServerGroupConfig = this.getCurrentKvkSeasonServerGroupConfig();
			// 3.A.A）
			if (null == currentKvkSeasonServerGroupConfig) {
				// 如果（没找到当前时刻对应的赛季信息）
				// 输出信息：
				// 没找到本赛季服当前时刻对应的赛季信息，所以终止后续启动流程，等待ZK重启指令
				logger.error("没找到本【赛季服】{}，当前时刻{}，对应的赛季信息，所以终止后续启动流程，等待ZK重启指令!", this.currentGameServerConfig.getGameServerId(), this.currentTimeMillis);
				// 然后卡住启动流程，只监听ZK，等待下一次重启。
				this.waitingForRestart();
			} else {
				// 1、 分组内的O服还有启动的，包括非赛和赛季的
				List<Integer> oServerIdsHasInstance = this.oServerHasInstance();
				if (oServerIdsHasInstance.size() > 0) {
					// 如果（赛季信息找到，但是本组内的任一原服还存在【原始游戏服】进程，即：任一原服节点下有instance）
					// 输出信息：
					// 第几赛季，本服属于XX组，本服是赛季服，赛季已开始，但是因为如下原始游戏服尚未关闭：XXX服、XXX服、XXX服，所以终止后续启动流程，等待ZK重启指令
					logger.error("本【赛季服】{}，当前时刻{}，对应的赛季信息是{}，但是因为如下原始游戏服尚未关闭{}，所以终止后续启动流程，等待ZK重启指令!", this.currentGameServerConfig.getGameServerId(),
							this.currentTimeMillis, currentKvkSeasonServerGroupConfig, oServerIdsHasInstance);
					// 然后卡住启动流程，只监听ZK，等待下一次重启。
					this.waitingForRestart();
				}
				// 2、分组内的O服在其他分组内
				Map<Integer, KvkInstance> oServerIdsHasKvkInstance = this.oServerHasKvkInstance();
				if (oServerIdsHasKvkInstance.size() > 0) {
					// 如果（赛季信息找到，但是本组内的任一原服已经有【KVK赛季服】进程，即：任一原服节点下有kvk_instance）
					// 输出信息：
					// 第几赛季，本服属于XX组，本服是赛季服，赛季已开始，但是因为如下原始游戏服有对应的【KVK赛季服】启动：XXX服、XXX服、XXX服，所以终止后续启动流程，等待ZK重启指令
					logger.error("本【赛季服】{}，当前时刻{}，对应的赛季信息是{}，但是因为如下原始游戏服有对应的【KVK赛季服】启动{}，所以终止后续启动流程，等待ZK重启指令!", this.currentGameServerConfig.getGameServerId(),
							this.currentTimeMillis, currentKvkSeasonServerGroupConfig, oServerIdsHasKvkInstance);
					// 然后卡住启动流程，只监听ZK，等待下一次重启。
					this.waitingForRestart();
				}
				// 3、当前分组的原服serverId存在更高赛季且赛季已开始
				Set<Integer> oServerIds = currentKvkSeasonServerGroupConfig.getOServerIds();
				for (Integer oServerId : oServerIds) {
					KvkSeasonServerGroupConfig serverGroupByOServerIdAndTime = config.getKvkSeasons().getServerGroupByOServerIdAndTime(oServerId, currentTimeMillis);
					if (serverGroupByOServerIdAndTime != null && currentKvkSeasonServerGroupConfig.getSeason() < serverGroupByOServerIdAndTime.getSeason()) {
						String errMsg = "本【赛季服】" + this.currentGameServerConfig.getGameServerId() + "，当前时刻" + this.currentTimeMillis + "，对应的赛季信息是"
								+ currentKvkSeasonServerGroupConfig + "，但是因为分组内的原服【" + oServerId + "】同时存在于赛季服【" + serverGroupByOServerIdAndTime.getKServerId() + "】中，且赛季信息更高"
								+ serverGroupByOServerIdAndTime + "，所以终止后续启动流程，等待ZK重启指令!";
						logger.error(errMsg);
						throw new RuntimeException(errMsg);
					}
				}
			}
		} else {
			// 3.B）
			// 对于非【赛季服】：检查ZK上本服ID下是否有kvkInstance字段，如果存在，终止启动过程。
			// 输出信息：因赛季服XXX已经启动，所以终止【原始游戏服】启动过程。
			// 非【赛季服 目前是【原始游戏服】【GVG战斗服】【GVG中控服】【KVK中控服】
			if (this.currentGameServerConfig.getKvkInstance() != null) {
				String errMsg = "因【赛季服】" + this.currentGameServerConfig.getKvkInstance() + "已经启动，所以终止本【原始游戏服】" + this.currentGameServerConfig.getGameServerId() + "的启动过程。";
				logger.error(errMsg);
				throw new RuntimeException(errMsg);
			}
			// 如果【原始游戏服】自己所在赛季已经到达启动时间（根据当前时间能拿到赛季配置信息），就不能继续启动。【GVG战斗服】【GVG中控服】【KVK中控服】不会存在赛季信息。
			KvkSeasonServerGroupConfig serverGroupConfig = this.getKvkSeasonServerGroupConfigByOServerId(this.currentGameServerConfig.getGameServerId(), currentTimeMillis);
			if (null != serverGroupConfig) {
				String errMsg = "因 赛季 " + serverGroupConfig + "已经开启（当前时间在赛季时间范围内），所以终止本【原始游戏服】" + this.currentGameServerConfig.getGameServerId() + "的启动过程。";
				logger.error(errMsg);
				throw new RuntimeException(errMsg);
			}
		}

		// 4）
		// 剩余情况下，表示【原始游戏服】、【赛季服】可以继续启动

		// 启动最初，就占上进程ID，防止多重启动。

		// 4.1）在ZK上自己的服ID下建立instance临时节点。
		Instance instance = new Instance(OsUtil.getPid(), false, 0, 0, OsUtil.getTotalPhysicalMemorySizeG(), 0, 0);
		// 4.2）对于【赛季服】，还要根据当前赛季配置，将本组内的所有【原服】下建立kvkInstance临时节点
		// ，如：kvkInstance={KServerId:80001}
		List<GameServerConfig> oServers = new ArrayList<>();
		KvkSeasonServerGroupConfig currentKvkSeasonServerGroupConfig = this.getCurrentKvkSeasonServerGroupConfig();
		if (currentKvkSeasonServerGroupConfig != null) {
			for (int oServerId : currentKvkSeasonServerGroupConfig.getOServerIds()) {
				GameServerConfig gsc = config.getGameServers().get(oServerId);
				oServers.add(gsc);
			}
		}
		currentGameServerConfig.setOServers(oServers);

		currentGameServerConfig.initInstance(instance);

		logger.info("本服务器ID{}，类型{}，ZK配置信息：{}", this.currentGameServerConfig.getGameServerId(), this.currentGameServerConfig.serverType(), this.currentGameServerConfig.toString());
	}

	private void waitingForRestart() {
		do {
			try {
				Thread.currentThread().sleep(10000); // 等待10秒
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}
		} while (true);
	}

	private List<Integer> oServerHasInstance() {
		List<Integer> rtn = new ArrayList<>();
		for (Integer oServerId : this.getCurrentKvkSeasonServerGroupConfig().getOServerIds()) {
			// 非赛的
			GameServerConfig gsc = config.getGameServers().get(oServerId);
			if (gsc.getInstance() != null) {
				rtn.add(oServerId);
			}
			// 赛季的
			KvkInstance kvkInstance = gsc.getKvkInstance();
			if (kvkInstance != null) {
				int kServerId = kvkInstance.getkServerId();
				GameServerConfig gameServerConfig = config.getGameServers().get(kServerId);
				if (gameServerConfig != null && gameServerConfig.getInstance() != null) {
					rtn.add(kServerId);
				}
			}
		}
		return rtn;
	}

	/**
	 * 当前赛季的O服处于赛季种
	 */
	private Map<Integer, KvkInstance> oServerHasKvkInstance() {
		Map<Integer, KvkInstance> rtn = new HashMap<>();
		for (Integer oServerId : this.getCurrentKvkSeasonServerGroupConfig().getOServerIds()) {
			GameServerConfig gsc = config.getGameServers().get(oServerId);
			KvkInstance ki = gsc.getKvkInstance();
			if (ki != null) {
				rtn.put(oServerId, ki);
			}
		}
		return rtn;
	}

	/**
	 * 获取当前服务器配置
	 * 
	 * @return
	 */
	public GameServerConfig getCurrentGameServerConfig() {
		return currentGameServerConfig;
	}

	/**
	 * 获取当前服务器（启动）类型
	 * 
	 * @return
	 */
	public ServerType getCurrentGameServerType() {
		return this.currentGameServerConfig.serverType();
	}

	private void checkCurrentGameServer_Instance() {
		if (currentGameServerConfig == null) {
			throw new RuntimeException("LsConfig无法根据本机IP找到未启动过的GameServerConfig配置！" + config.toString() + " " + localhostIpList.toString());
		} else if (null != currentGameServerConfig.getInstance() && currentGameServerConfig.getInstance().getOsPid() != OsUtil.getPid()) {
			throw new RuntimeException("LsConfig CurrentGameServerConfig对应的osPid，与本进程不同！" + currentGameServerConfig + " " + OsUtil.getPid());
		}
	}

	public Instance updateCurrentGameServerStatus(int registedRole, int inMemoryRole, int onlineRole, int registerSpeed_RoleSize, int registerSpeed_SpendMillis) {
		checkCurrentGameServer_Instance();
		// 更新本服务器状态信息
		if (getCurrentGameServerType() != ServerType.GVG_BATTLE && getCurrentGameServerType() != ServerType.GVG_CONTROL) {
			// GVG不上报该信息
			this.currentGameServerConfig.setRegistedRole(registedRole);
		}
		Instance instance = new Instance(OsUtil.getPid(), true, inMemoryRole, onlineRole, OsUtil.getTotalPhysicalMemorySizeG(), registerSpeed_RoleSize, registerSpeed_SpendMillis);

        // 如果第一次启动成功(finished为false，现在finished为true)，就也报到zk，让zk监听者可以及时拿到服务器已经启动的状态。
        // GVG依赖这个，需要依赖中控服比游戏服先启动 todo: 去掉此逻辑，改为不依赖顺序
        boolean isFirstLaunchFinished = ! currentGameServerConfig.getInstance().isLaunchFinished();

        if (isFirstLaunchFinished || ! GameServerConfig.USE_REDIS_SAVE_GAME_SERVER_INSTANCE_DATA) {
            this.currentGameServerConfig.setZkInstance(instance);

            logger.info("ServerStatus {} 状态上报zk完成", instance);
        }

        return instance;
	}

	public void updateCurrentGameServerKingdomInfo(String serverName, String currentKingName, String currentKingAllianceAliasName, String kingHead, Long kingId, AllianceFlag allianceFlag) {
		checkCurrentGameServer_Instance();
		this.currentGameServerConfig.setKingdomInfo(new KingdomInfo(serverName, currentKingName, currentKingAllianceAliasName, kingHead, kingId, allianceFlag));
	}

	public void updateSeasonOGameServerKingdomInfo(Integer serverId, String serverName, String currentKingName, String currentKingAllianceAliasName, String kingHead, Long kingId, AllianceFlag allianceFlag) {
		GameServerConfig gameServerConfig = getLsConfig().getGameServers().get(serverId);
		gameServerConfig.setKingdomInfo(new KingdomInfo(serverName, currentKingName, currentKingAllianceAliasName, kingHead, kingId, allianceFlag));
	}

	public void updateCurrentGameServerOpenTimeMs(long openTimeMs) {
		checkCurrentGameServer_Instance();
		this.currentGameServerConfig.setOpenTimeMs(openTimeMs);
	}

	public boolean currentGameServerIsDiversionOrInDiversionableList() {
		checkCurrentGameServer_Instance();
		return this.config.gameServerInDiversionableList(this.currentGameServerConfig.getGameServerId()) || this.currentGameServerConfig.isDiversionFlag();
	}

	public boolean checkGameServersDbVersion() {
		String dbVersion = this.currentGameServerConfig.getDbVersion();
		if (this.currentGameServerConfig.isGuideServer()) {
			return true;
		}
		// 灰度版本
		String currentCanaryVersion = this.currentGameServerConfig.getVersion();
		for (GameServerConfig gameServer : config.getGameServers().values()) {
			if (gameServer.isGuideServer()) {
				continue;
			}
			//赛季服但是还没开放
			if (gameServer.serverType() == ServerType.KVK_SEASON && gameServer.getOpenTimeMs() == 0) {
				continue;
			}
			if (gameServer.serverType() == ServerType.GAME
					|| (gameServer.serverType() == ServerType.KVK_SEASON && null != this.getKvkSeasonServerGroupConfigByKServerId(gameServer.getGameServerId(), currentTimeMillis)
							&& !isKvkSeasonServerGroupConfigExpired(getKvkSeasonServerGroupConfigByKServerId(gameServer.getGameServerId(), currentTimeMillis)))) {
				String canaryVersion = gameServer.getVersion();
				// 只有灰度版本相同的服务器才检查数据库版本
				if (!Objects.equals(canaryVersion, currentCanaryVersion)) {
					continue;
				}
				// 所有【原始游戏服】和 已经启动赛季的【赛季服】才被检查数据库版本。
				if (!dbVersion.equals(gameServer.getDbVersion())) {
					logger.debug("LsConfig checkGameServersDbVersion：服务器{}的数据库版本 {} 与当前服{}版本 {} 不一致", gameServer.getGameServerId(), gameServer.getDbVersion(),
							currentGameServerConfig.getGameServerId(), dbVersion);
					return false;
				}
			} else {
				// 某些极端情况下GVG_CONTROL可能被单独发版，这会导致GVG_CONTROL和GVG_BATTLE的数据库版本比GAME/KVK_SEASON高。
				// KVK_CONTROL也有可能发生这种情况。所以要忽略这三类服务器。
				continue;
			}
		}
		return true;
	}

	public boolean serverTypeIsGAME_or_KVKSEASON(ServerType st) {
		return st == ServerType.GAME || st == ServerType.KVK_SEASON;
	}

	public boolean currentServerTypeIsGAME_or_KVKSEASON() {
		if (currentGameServerConfig == null) {
			return false;
		}
		return serverTypeIsGAME_or_KVKSEASON(this.currentGameServerConfig.serverType());
	}

	public ServerType getServerType(int serverId) {
		return serverTypeDeterminer.getServerType(serverId);
	}

	public boolean isGuideGameServer(int serverId) {
		return serverTypeDeterminer.isGuideGameServer(serverId);
	}

	public boolean isBattleServer(int serverId) {
		return serverTypeDeterminer.getServerType(serverId) == ServerType.GVG_BATTLE || serverTypeDeterminer.getServerType(serverId) == ServerType.TVT_BATTLE;
	}

	/**
	 * 关闭
	 */
	public void close() {
		zk.close();
	}

	//
	// Kvk赛季相关
	//

	/**
	 * 
	 * 根据【赛季服ID】和当前时间来判断，当前是否进入某赛季（可以获得赛季分组信息）。
	 * 
	 * @param kServerId
	 * @return
	 */
	public KvkSeasonServerGroupConfig getKvkSeasonServerGroupConfigByKServerId(int kServerId, long currentTimeMillis) {
		// 注意，此处传进来的gameServerId都是【赛季服】ID，也是【K服】ID
		KvkSeasonServerGroupConfig serverGroup = this.config.getKvkSeasons().getServerGroupByKServerId(kServerId);
		if (serverGroup != null) {
			// 若能找到，说明 此kServerId是（第2～N赛季）【赛季服】
			// 但是当前是否在这个赛季里，还要看当前时刻是否处于此赛季时间范围内。
			if (serverGroup.afterStartTime(currentTimeMillis)) {
				// 当前时刻处于此赛季时间范围内
				return serverGroup;
			} else {
				// 不在赛季时间范围内
				return null;
			}
		} else {
			// 策划没有配置赛季信息
			return null;
		}
	}

	/**
	 * 
	 * 根据【原始游戏服ID】和当前时间来判断，赛季是否已经开始（【原始游戏服】就不能继续启动了）。
	 * 
	 * @param oServerId
	 * @return
	 */
	public KvkSeasonServerGroupConfig getKvkSeasonServerGroupConfigByOServerId(int oServerId, long currentTimeMillis) {
		// 注意，此处传进来的gameServerId都是【原始游戏服】ID。
		KvkSeasonServerGroupConfig serverGroup = this.config.getKvkSeasons().getServerGroupByOServerIdAndTime(oServerId, currentTimeMillis);
		// 根据【原始游戏服】ID和当前时刻，查找当前处于哪个赛季
		if (serverGroup != null) {
			// 若能找到，说明 此oServerId是（第2～N赛季）的【原服】，并且当前时刻处于此赛季时间范围内
			return serverGroup;
		} else {
			// 策划没有配置赛季信息，或者 当前时刻不在此赛季时间范围内
			return null;
		}
	}

	public KvkSeasonServerGroupConfig getCurrentKvkSeasonServerGroupConfig() {
		if (config == null || config.getKvkSeasons() == null) {
			return null;
		}
		KvkSeasonServerGroupConfig serverGroup = this.config.getKvkSeasons().getServerGroupByKServerId(currentKvkSeasonServerGroupConfig_kServerId);

		return serverGroup;

	}

	@Override
	public String toString() {
		return "ConfigCenter [config=" + config + ", localhostIpList=" + localhostIpList + ", currentTimeMillis=" + currentTimeMillis + ", currentGameServerConfig="
				+ currentGameServerConfig + ", currentKvkSeasonServerGroupConfig_kServerId=" + currentKvkSeasonServerGroupConfig_kServerId + "]";
	}

	public long getOpenTimeMs(int serverId) {
		GameServerConfig gameServerConfig = config.getGameServers().get(serverId);
		if (gameServerConfig == null) {
			String errorMsg = "根据服务器id=" + serverId + "获得不到GameServerConfig";
			logger.error(errorMsg, new RuntimeException(errorMsg));
			return 0;
		} else {
			return gameServerConfig.getOpenTimeMs();
		}
	}

	public int getSeason() {
		KvkSeasonServerGroupConfig currentKvkSeasonServerGroupConfig = this.getCurrentKvkSeasonServerGroupConfig();
		if (currentKvkSeasonServerGroupConfig == null) {
			return 1;
		}
		return currentKvkSeasonServerGroupConfig.getSeason();
	}

	public boolean isKvkSeasonServerGroupConfigExpired(KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig) {
		long currentTimeMillis = TimeUtil.getNow();
		if (!kvkSeasonServerGroupConfig.afterSettleTime(currentTimeMillis)) {
			// 检测的K服还没到结算时间，是生效的
			return false;
		}
		int kServerId = kvkSeasonServerGroupConfig.getKServerId();
		GameServerConfig gameServerConfig = getLsConfig().getGameServers().get(kServerId);
		if (gameServerConfig != null && gameServerConfig.isAlive()) {
			// 服务器还活着
			return false;
		}
		int season = kvkSeasonServerGroupConfig.getSeason();
		Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
		// 检测的K服下的所有O服对应的下一赛季的K服都已经进入准备阶段且起服了
		KvkSeasonsConfig kvkSeasons = getLsConfig().getKvkSeasons();
		for (Integer oServerId : oServerIds) {
			KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig2 = kvkSeasons.getKvkSeasonServerGroupConfigByOServerIdAndSeason(oServerId, season + 1);
			if (kvkSeasonServerGroupConfig2 == null) {
				// 没有新赛季
				return false;
			}
			boolean afterReadyTime = kvkSeasonServerGroupConfig2.afterStartTime(currentTimeMillis);
			if (!afterReadyTime) {
				// 还没到新赛季准备时间
				return false;
			}
		}
		return true;
	}

	/**
	 * 获取灰度版本获取对应的BattleServerHost
	 * @return
	 */
	public String getBattleServerHost() {
		if (this.currentGameServerConfig.isCanary())
		{
			return this.getLsConfig().getBattleServer().getCanaryHost();
		} else {
			return this.getLsConfig().getBattleServer().getHost();
		}
	}

	/**
	 * 是否新手服
	 * @return
	 */
	public boolean isNewPlayerGameServer() {
		int id = getCurrentGameServerConfig().getGameServerId();
		return isGuideGameServer(id);
	}

    /**
     * 是否是gm服
     *
     * 注意，这个只能在仿真和线上使用，因为仿真和线上才有独立的gm服
     * 其它环境都是跟 login 共用一个进程
     */
    public boolean isGmServer() {
        String gmHostnamePrefix = "gm-server";
        try {
            // 获取本地主机对象
            InetAddress localHost = InetAddress.getLocalHost();
            // 获取主机名
            String hostName = localHost.getHostName();
            if (hostName == null || !hostName.startsWith(gmHostnamePrefix)) {
                logger.info("isGmServer 主机名不是gm-server，忽略: {} ", hostName);
                return false;
            }
            return true;
        } catch (UnknownHostException e) {
            logger.info("isGmServer 无法获取本机主机名: {}", e.getMessage());
            return false;
        }
    }
}
