package com.longtech.ls.zookeeper;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lc.billion.icefire.core.utils.CollectionUtils;
import com.longtech.cod.common.utils.ToStringUtil;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public class KvkSeasonServerGroupConfig {
	private KvkSeasonServerGroupConfig() {
		// for jackson
	}

	// 第几赛季
	private int season;

	/**
	 * 本赛季，本组内的【K服】ID 注意这个ID，也被用作此分组的ID，也是此分组对应的【赛季服】的ID
	 * 
	 * 因为第一赛季特殊：没有准备阶段，且第一赛季只有一个原服，没有K服。
	 * 
	 * 所以其实第一赛季的服，就没有对应的KvkSeasonServerGroupConfig。
	 */
	private int kServerId;

	// 本赛季，本组内由那些【原服】组成
	private final Set<Integer> oServerIds = new HashSet<>();

	@JsonIgnore
	private Set<Integer> unmodifiableOServerIds = null;

	/**
	 * 
	 * 【20211015】
	 * 
	 * 每个赛季由如下阶段组成：
	 * 
	 * 准备阶段（readyTime至battleTime）：readyTime之后，运营人员可以通过GM激活该赛季，即启动【赛季服】，此时多个【原服】和一个【K服】已经在一个进程内，但是【K服】尚不允许【原服】的玩家迁入。此阶段固定时长，如：3天。
	 * 
	 * 战争阶段（battleTime至settleTime）：【K服】允许迁入迁出。玩家在K服进行各种争夺，【K服】中赛季结算相关的积分榜可以进行积分排名。此阶段固定时长，如：70天。
	 * 
	 * 结算时刻（settleTime）：此时刻触发【KVK中控服】通知【赛季服】计算各种奖励&发奖。
	 * 
	 * 结算后阶段：结算完成之后，就是结算后阶段。此阶段允许玩家继续在【K服】，但是总积分榜锁定，不再变化。此阶段时长不固定，一直等待运营人员开启下一赛季，可以利用此时间来等待下一赛季的所有原服结算都结束，再由运营人员通过GM操作切换下一赛季。
	 * 
	 * 赛季切换操作，分两种情况： 「由第一赛季进入第二赛季的」 界面显示：按【赛季服】分组，每个赛季服下挂多个原始游戏服。
	 * （参看之前的启服ZK控制逻辑）切换赛季只需要一步点击按钮，按钮具体逻辑就是依次停掉该赛季服下所有原始游戏服进程，待所有进程停止后，再通知对应【赛季服】重启即可。
	 * 注意：【赛季服】能正常启动成功的条件之一是，当前时刻已经大于此赛季的readyTime。
	 * 
	 * 「第二赛季之后，由第n赛季进入第n+1赛季的」
	 * 界面显示：按【第（n+1）赛季服】分组，每个赛季服下可能需要显示包含的每个原始游戏服当前属于哪个【第（n）赛季服】。 切换赛季需要多步：
	 * 1）检查：依次查看 该赛季服下 对应的所有【第（n）赛季服】，是否都已经到达结算后阶段。任何一个不到达，都不能操作切换赛季，需要继续等待。
	 * 2）若第一步检查通过，可以依次结束所有【第（n）赛季服】：将所有在【K服】的玩家提回原服后，终止进程。
	 * 3）所有【第（n）赛季服】都停止之后，再通知对应【第（n+1）赛季服】重启即可。
	 * 注意：【第（n+1）赛季服】能正常启动成功的条件之一是，当前时刻已经大于此赛季的readyTime。
	 * 
	 * 第一赛季特殊：没有准备阶段，且第一赛季只有一个原服，没有K服。
	 * 适合策划（热更）改下一赛季KVK分组配置的阶段是：准备阶段、战争阶段、结算后阶段。（如果下一赛季服配置没有在ZK上出现，给玩家显示匹配中，否则显示下一赛季的相关信息）
	 * 
	 */

	// 开启时刻
	private long startTime;
	// 结算时刻（也就是结算后阶段开始时刻，过了此时刻，就是结算后阶段，等待GM操作开启下一赛季）
	private long settleTime;
	// 开始匹配
	private long matchTime;
	// 开始展示
	private long matchShowTime;
	// 全部结束
	private long endTime;

	public int getSeason() {
		return season;
	}

	public int getKServerId() {
		return kServerId;
	}

	public Set<Integer> getOServerIds() {
		if (null == unmodifiableOServerIds) {
			// KvkSeasonServerGroupConfig
			// 是个Json序列化/反序列化对象，因此只能使用这种方式初始化（字段上直接初始化，可能导致gameServerIds是一个老的空集合，而后面gameServerIds已经被反序列化给替换了）
			unmodifiableOServerIds = Collections.unmodifiableSet(oServerIds); // 对外用的。避免大量创建只读容器
		}
		return unmodifiableOServerIds;
	}

	public long getStartTime() {
		return startTime;
	}

	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}

	public long getSettleTime() {
		return settleTime;
	}

	public void setSettleTime(long settleTime) {
		this.settleTime = settleTime;
	}

	public KvkSeasonServerGroupConfig(int kServerId, int season, long startTime, long settleTime,
									  long matchTime, long matchShowTime, long endTime, Collection<Integer> oServerIds) {
		this.kServerId = kServerId;
		this.season = season;
		this.startTime = startTime;
		this.settleTime = settleTime;
		this.oServerIds.addAll(oServerIds);
		this.matchTime = matchTime;
		this.matchShowTime = matchShowTime;
		this.endTime = endTime;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null) {
			return false;
		}
		if (getClass() != obj.getClass()) {
			return false;
		}
		KvkSeasonServerGroupConfig k = (KvkSeasonServerGroupConfig) obj;
		return season == k.getSeason() && kServerId == k.getKServerId() && startTime == k.getStartTime()
				&& settleTime == k.getSettleTime() && CollectionUtils.isSetEquals(oServerIds, k.getOServerIds())
				&& matchTime == k.getMatchTime() && matchShowTime == k.getMatchShowTime() && endTime == k.getEndTime();
	}

	@Override
	public String toString() {
		return "KvkSeasonServerGroupConfig [season=" + season + ", kServerId=" + kServerId + ", oServerIds="
				+ oServerIds + ", startTime=" + startTime + ", endTime=" + endTime + ", battleTime="
				+  settleTime + ", matchTime=" + matchTime + ", matchShowTime=" + matchShowTime + "]";
	}
	
	public static String toJson(KvkSeasonServerGroupConfig obj) {
		return ToStringUtil.toJson(obj);
	}

	public static KvkSeasonServerGroupConfig fromJson(String json) {
		return ToStringUtil.fromJson(json, KvkSeasonServerGroupConfig.class);
	}

	//
	// 一些业务逻辑方法
	//

	/**
	 * 此时刻是否过了此赛季的准备时刻（由于赛季后阶段时长不固定，因此需要结合后续赛季信息，用于来判断当前时刻是否处于此赛季时间范围内）
	 * 
	 * @param currentTimeMillis
	 * @return
	 */
	public boolean afterStartTime(long currentTimeMillis) {
		return startTime <= currentTimeMillis;
	}

	public boolean afterSettleTime(long currentTimeMillis) {
		return settleTime <= currentTimeMillis;
	}

	public boolean isInMigrateTime(long currentTimeMillis) {
		return settleTime <= currentTimeMillis && currentTimeMillis <= matchTime;
	}

	public long getMatchTime() {
		return matchTime;
	}

	public void setMatchTime(long matchTime) {
		this.matchTime = matchTime;
	}

	public long getMatchShowTime() {
		return matchShowTime;
	}

	public void setMatchShowTime(long matchShowTime) {
		this.matchShowTime = matchShowTime;
	}

	public long getEndTime() {
		return endTime;
	}

	public void setEndTime(long endTime) {
		this.endTime = endTime;
	}
}
