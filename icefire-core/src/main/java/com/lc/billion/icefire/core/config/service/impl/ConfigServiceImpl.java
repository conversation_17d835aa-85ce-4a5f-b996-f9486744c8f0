package com.lc.billion.icefire.core.config.service.impl;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.ConfigMetaException;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.load.impl.DefaultConfigLoader;
import com.lc.billion.icefire.core.config.model.ConfigFileBaseInfo;
import com.lc.billion.icefire.core.config.model.ConfigFileInfo;
import com.lc.billion.icefire.core.config.model.ConfigFormat;
import com.lc.billion.icefire.core.config.model.ConfigHolder;
import com.lc.billion.icefire.core.config.service.ConfigResolver;
import com.lc.billion.icefire.core.config.service.IConfigChangeListener;
import com.simfun.sgf.thread.SingleThreadWorker;
import com.simfun.sgf.utils.MessageDigestUtils;
import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 
 *
 * <AUTHOR>
 */
public class ConfigServiceImpl {

	private static final Logger LOG = LoggerFactory.getLogger(ConfigServiceImpl.class);

	/** 监控的间隔时间，ms */
	private static final int WATCH_INTERVAL = 10000;

	private long lastUpdateConfigTime;

	@Autowired
	private ApplicationContext appCtx;

	@Autowired
	private DefaultConfigLoader configLoader;

	private Map<ConfigFormat, ConfigResolver> resolverMap;

	private String[] configPackages;

	private Map<Class<?>, ConfigHolder> configMap = new HashMap<>();

	private ConfigFileWatchdog watchDog;

	private final List<IConfigChangeListener> listeners = new ArrayList<>();

	public void setConfigPackages(String[] configPackages) {
		this.configPackages = configPackages;
	}

	@PostConstruct
	public void init() {
		try {
			// ConfigResolver
			Map<ConfigFormat, ConfigResolver> resolverMap = new EnumMap<ConfigFormat, ConfigResolver>(ConfigFormat.class);
			Map<String, ConfigResolver> beansOfResolver = appCtx.getBeansOfType(ConfigResolver.class, false, true);
			for (ConfigResolver b : beansOfResolver.values()) {
				ConfigResolver exists = resolverMap.put(b.type(), b);
				if (exists != null) {
					throw new IllegalStateException("ConfigResolver duplicate: " + exists.type());
				}
			}
			this.resolverMap = Collections.unmodifiableMap(resolverMap);
			Map<String, IConfigChangeListener> beansOfListener = appCtx.getBeansOfType(IConfigChangeListener.class, false, true);
			for (IConfigChangeListener listener : beansOfListener.values()) {
				if (listeners.contains(listener)) {
					throw new IllegalStateException("ConfigListener duplicate: " + listener.getClass().getName());
				}
				listeners.add(listener);
			}

		} catch (Exception e) {
			LOG.error("CatchedException", e);
		}

	}

	public void start() {
		for (String configPackage : this.configPackages) {
			Reflections reflections = new Reflections(configPackage);
			Set<Class<?>> configClasses = reflections.getTypesAnnotatedWith(Config.class);
			for (Class<?> clazz : configClasses) {
				ConfigHolder configHolder = configDataResolve(clazz);
				configMap.put(clazz, configHolder);
			}
		}

		LOG.info("config load finish.");

		watchDog = new ConfigFileWatchdog(WATCH_INTERVAL);
		watchDog.start();
	}

	public void stop() {
		if (watchDog != null) {
			watchDog.stop();
		}
	}

	public long getLastUpdateConfigTime() {
		return lastUpdateConfigTime;
	}

	private ConfigHolder configDataResolve(Class<?> clazz) {
		Config configAnno = clazz.getAnnotation(Config.class);
		String configName = configAnno.name();

		ConfigFormat configFormat = configAnno.format();
		ConfigResolver configResolver = resolverMap.get(configFormat);
		if (configResolver == null) {
			throw new ConfigMetaException("Not found config resolver. configName=" + configName + ", configFormat=" + configFormat);
		}

		try {
			long start = TimeUtil.getNow();

			ConfigFileInfo cfi = configLoader.load(configName);

			Object config = configResolver.resolve(clazz, configAnno, cfi.getContent());

			String md = MessageDigestUtils.sha1(cfi.getContent()); // 计算SHA-1
			ConfigHolder configHolder = new ConfigHolder(cfi.getBaseInfo(), md, config);

			long end = TimeUtil.getNow();

			LOG.info("Load config: {}, took {} ms to load", configName, (end - start));

			return configHolder;
		} catch (Exception e) {
			LOG.error("config resolver failed...configName: " + configName, e);
			throw new ConfigMetaException("config resolver failed. configName: " + configName, e);
		}
	}

	public <T> T gmLoadConfig(Class<T> clazz,String content){
		var configResolver = resolverMap.get(ConfigFormat.JSON_META);
		Config configAnno = clazz.getAnnotation(Config.class);
		Object config = configResolver.resolve(clazz, configAnno, content.getBytes());
		return (T) config;
	}

	public <T> T getConfig(Class<T> clazz) {
		ConfigHolder configHolder = configMap.get(clazz);
		if (configHolder == null) {
			return null;
		}
		@SuppressWarnings("unchecked")
		T config = (T) configHolder.getConfig();
		return config;
	}

	class ConfigFileWatchdog extends SingleThreadWorker {

		private int intervalTime;

		public ConfigFileWatchdog(int intervalTime) {
			this.intervalTime = intervalTime;
		}

		@Override
		protected void execute() throws InterruptedException {
			tryUpdate();
			Thread.sleep(intervalTime);
		}

		private void tryUpdate() {
			List<ConfigFileBaseInfo> checkList = new ArrayList<>();
			for (Map.Entry<Class<?>, ConfigHolder> e : configMap.entrySet()) {
				checkList.add(e.getValue().getBaseInfo());
			}
			Set<String> checkRst = configLoader.checkUpdate(checkList);
			if (checkRst.isEmpty()) {
				return;
			}

			Map<Class<?>, ConfigHolder> updateMap = new HashMap<>();
			for (Map.Entry<Class<?>, ConfigHolder> e : configMap.entrySet()) {
				Class<?> clazz = e.getKey();
				ConfigHolder configHolder = e.getValue();
				String configName = configHolder.getBaseInfo().getName();
				if (checkRst.contains(configName)) {
					ConfigHolder newConfigHolder = null;
					try {
						LOG.info("Reload config: {}", configName);
						newConfigHolder = configDataResolve(clazz);
					} catch (Exception ex) {
						LOG.error("ReLoad failed configName: " + configName, ex);
						continue;
					}
					if (!configHolder.getMd().equals(newConfigHolder.getMd())) {
						updateMap.put(clazz, newConfigHolder);
					} else {
						ConfigFileBaseInfo baseInfo = configHolder.getBaseInfo();
						ConfigFileBaseInfo newBaseInfo = newConfigHolder.getBaseInfo();
						baseInfo.setLastModified(newBaseInfo.getLastModified());
					}
				}
			}
			if (updateMap.isEmpty()) {
				return;
			}
			//组织要更新的meta名字列表
			List<String> configNames = new ArrayList<>(updateMap.size());
			for (ConfigHolder ch : updateMap.values()) {
				String confName = ch.getBaseInfo().getName();
				if (configNames.contains(confName)) {
					continue;
				}
				configNames.add(confName);
			}
			//将原configMap中 不在updateMap中的meta 补进updateMap中。
			for (Map.Entry<Class<?>, ConfigHolder> e : configMap.entrySet()) {
				Class<?> clazz = e.getKey();
				if (!updateMap.containsKey(e.getKey())) {
					updateMap.put(clazz, e.getValue());
				}
			}
			//替换内存中的meta数据
			configMap = updateMap;

			// 调用监听，只要变化map
			triggerConfigListener(configNames);
			lastUpdateConfigTime = TimeUtil.getNow();
		}
	}

	/**
	 * 触发监听器
	 * 
	 * @param configNames 本次需要热更新的所有meta名字的列表。
	 */
	private void triggerConfigListener(List<String> configNames) {
		try {
			if (configNames.isEmpty()) {
				return;
			}
			for (IConfigChangeListener listener : listeners) {
				if (listener == null) {
					continue;
				}
				listener.onChange(configNames);
			}
		} catch (Exception e) {
			// 吃掉异常，保证服务器配置不会因为监听器相关异常导致其他数据异常
			LOG.error("[triggerConfigListener] throw exception", e);
		}

	}

}
