package com.lc.billion.icefire.core.common;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.simfun.sgf.utils.Base64Coder;
import com.simfun.sgf.utils.MessageDigestUtils;

/**
 *
 * <AUTHOR>
 * @sine 2015年6月10日 下午3:44:51
 *
 */

public class TokenUtils {

	private static final Logger logger = LoggerFactory.getLogger(TokenUtils.class);

	private static final String LOGIN_SIGN_KEY = "doomsday_login_simple_key";

	private static final String WS_TOKEN_KEY = "doomsday_webservice_token_key";

	private static final String RECHARGE_TOKEN_KEY = "doomsday_recharge_token_key";

	public static String getLoginToken(long userId, long roleId, long timestamp) {
		logger.debug("登录计算sign；userId = " + userId + "、roleId = " + roleId + "、timestamp = " + timestamp + "、LOGIN_SIGN_KEY = " + LOGIN_SIGN_KEY);

		String str = "" + userId + roleId + timestamp + LOGIN_SIGN_KEY;

		logger.debug("登录计算sign；\"\" + userId + roleId + timestamp + LOGIN_SIGN_KEY = " + str);
		
		String md5 = MessageDigestUtils.md5(str);

		logger.debug("登录计算sign；md5 = " + md5);
		
		String base64 = Base64Coder.encodeString(md5);
		
		logger.debug("登录计算sign；base64 = " + base64);
		
		return base64;
	}

	public static String getWebserviceToken(long timestamp) {
		String md5 = MessageDigestUtils.md5(WS_TOKEN_KEY + timestamp);
		return Base64Coder.encodeString(md5);
	}

	public static String genSign(Map<String, Object> params) {
		StringBuffer buffer = new StringBuffer();

		for (Map.Entry<String, Object> entry : params.entrySet()) {
			buffer.append(entry.getKey()).append("=").append(entry.getValue().toString()).append("&");
		}
		buffer.append(RECHARGE_TOKEN_KEY);

		String md5 = MessageDigestUtils.md5(buffer.toString());
		return Base64Coder.encodeString(md5);
	}
}
