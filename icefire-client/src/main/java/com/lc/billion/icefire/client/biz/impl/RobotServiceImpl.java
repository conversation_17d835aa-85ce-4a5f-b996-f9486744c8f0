package com.lc.billion.icefire.client.biz.impl;

import com.lc.billion.icefire.client.jmeter.client.TestLoginClient;
import com.lc.billion.icefire.client.net.ClientNetSession;
import com.lc.billion.icefire.client.net.SocketClient;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.simfun.sgf.net.msg.NetMessage;
import org.apache.jmeter.config.Arguments;
import org.apache.jmeter.protocol.java.sampler.JavaSamplerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class RobotServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(RobotServiceImpl.class);

    private boolean isInit = false;

    @Autowired
    private ConfigServiceImpl configService;

    private List<SocketClient> clients;

    private MsgSendService messageService;

    private volatile AtomicInteger finishCnt = new AtomicInteger(0);
    private static int testCnt;

    private volatile AtomicInteger currTestCnt = new AtomicInteger(0);

    private static AtomicInteger TOTAL_TEST_CNT = new AtomicInteger(0);

    public void initRobot() {
        initService();

//        Map map = cfgService.getConfig(InnermapConfig.class).getMap();

//        System.out.println(map);

        testCnt = 1000;
        int begin = 1000;
        int end = begin + testCnt;

        clients = new ArrayList<>();
        Executor executor = new ScheduledThreadPoolExecutor(800);
        for (int i = begin; i < end; i++) {
            executor.execute(new ClientDemo(begin, i));
            try {
                Thread.sleep(2l);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public synchronized void initService() {
        if (isInit) {
            return;
        }
        isInit = true;
        configService.init();
        configService.start();
//        cfgService.init();
    }

    class ClientDemo implements Runnable {

        private int i;
        private int startNum;

        ClientDemo(int startNum, int i) {
            this.i = i;
            this.startNum = startNum;
        }

        @Override
        public void run() {
            Arguments arguments = new Arguments();
            arguments.addArgument("ip", "127.0.0.1");
//            arguments.addArgument("ip", "**********");
            arguments.addArgument("port", "443");
//            arguments.addArgument("webUrl", "http://127.0.0.1:8080/api/sanguo2/v1/login");
//            arguments.addArgument("webUrl", "https://138-sanguo2-sdk-test.bjxuejing.cn/api/sanguo2/v1/login");
            arguments.addArgument("webUrl", "https://138-sanguo2-sdk-test03.bjxuejing.cn/api/sanguo2/v1/login");
            arguments.addArgument("appId", "com.lc.gos");
            arguments.addArgument("startnum", String.valueOf(startNum));
            arguments.addArgument("total", String.valueOf(testCnt));

            JavaSamplerContext context = new JavaSamplerContext(arguments);
            TestLoginClient client = new TestLoginClient();
            client.setupTest(context);
            // 真正的执行
            client.runTest(context);
            client.teardownTest(context);
        }
    }

    public void receiveMsg(ClientNetSession session, NetMessage msg) {
        session.getClient().getJmeterClient().receiveMsg(session, msg);
    }

    public int addTestCnt() {
        return currTestCnt.incrementAndGet();
    }

    public static int setTotalTestCnt(int cnt) {
        TOTAL_TEST_CNT.compareAndSet(0, cnt);
        return TOTAL_TEST_CNT.get();
    }

    public synchronized void finishOneTest(String playerInfo) {
        int currFinish = finishCnt.incrementAndGet();
        int currBegin = TOTAL_TEST_CNT.get();
//        int currBegin = currTestCnt.get();
        logger.info("finish one client [{}] ~testCnt is {},finishCnt is {}", playerInfo, currBegin, currFinish);
        boolean finish = currBegin <= currFinish;
        if (finish) {
            System.exit(1);
        }
    }
}
