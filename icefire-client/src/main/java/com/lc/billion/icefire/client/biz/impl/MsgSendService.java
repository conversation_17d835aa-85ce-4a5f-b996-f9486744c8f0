package com.lc.billion.icefire.client.biz.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.client.biz.dao.PlayerData;
import com.lc.billion.icefire.client.jmeter.client.AbstractJmeterClient;
import com.lc.billion.icefire.client.net.ClientNetSession;
import com.lc.billion.icefire.client.net.SocketClient;
import com.lc.billion.icefire.core.common.JsonUtils;
import com.lc.billion.icefire.core.common.TokenUtils;
import com.lc.billion.icefire.core.config.model.IntKeyIntValue;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.biz.config.BuildingConfig;
import com.lc.billion.icefire.game.biz.config.CityWorkQueueConfig;
import com.lc.billion.icefire.game.biz.model.city.BuildingGroup;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.currency.CurrencyValue;
import com.lc.billion.icefire.game.biz.model.prop.AttributeValue;
import com.lc.billion.icefire.game.biz.model.prop.PropKey;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.*;
import com.lc.billion.icefire.protocol.structure.*;
import com.simfun.sgf.common.tuple.TwoTuple;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;

@Service
public class MsgSendService {

    private static final Logger logger = LoggerFactory.getLogger(MsgSendService.class);

    private static final int MAX_SETOUT_CNT = 5;

    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private WorldMapServiceImpl worldMapService;
    @Autowired
    private RobotServiceImpl robotService;

    public void sendMsg(ClientNetSession session, TBase msg) {
        session.getClient().getJmeterClient().offerMsg(msg);
    }

    public void login(AbstractJmeterClient client, int threadNum, String appId, int index) throws Exception {
        String name = client.getName();
        String webUrl = client.getWebUrl();
        try {
            Map<String, String> loginParams = new HashMap<>();
            loginParams.put("country", "CN");
            loginParams.put("gaid", "");
            loginParams.put("channel", "");
            loginParams.put("bundleid", appId);
            loginParams.put("appversion", "10.4.0");
            loginParams.put("id", "RobotClient_" + index);
            loginParams.put("lang", "English");
            loginParams.put("deviceid", String.valueOf(threadNum));
            loginParams.put("serverId", "-1");
            loginParams.put("platform", "11");
            loginParams.put("token", String.valueOf(threadNum));

            logger.info("web url is [{}]", loginParams);
            logger.info("client id: {}", loginParams.get("id"));
//			StringBuilder loginUrl = new StringBuilder();
//			loginUrl.append(webUrl).append("?").append("country=CN&gaid=&channel=&bundleid=").append(appId).append("&appversion=10.4.0&id=&lang=English&deviceid=").append(threadNum).append("&serverId=-1&platform=11&token=").append(threadNum);
//			logger.info("web url is [{}]", loginUrl.toString());
//			String accounntResp = HttpUtil.get(loginUrl.toString());
            String accounntResp = HttpUtil.post(webUrl, loginParams);
//			logger.info("web account response is {}", accounntResp);
            JsonNode node = JsonUtils.readTree(accounntResp);
            int serverStatus = node.path("serverStatus").asInt();
            if (serverStatus != 2) {
                logger.error("web login error~serverStatus is [{}]", serverStatus);
                throw new RuntimeException();
            }
            int port = node.path("serverPort").asInt();
            String ip = node.path("serverHost").asText();
            String loginToken = node.path("loginToken").asText();
//			String expectSign = node.path("sign").asText();
            SocketClient socketClient = new SocketClient(name, ip, port, 1, client);
            client.setSocketClient(socketClient, index);
            //建立游戏服连接
            socketClient.start();

            ClientNetSession session = client.getClientSession();
            long roleId = node.path("roleId").asLong();
            CgLogin login = new CgLogin();
            login.setUid(threadNum);
            login.setRoleId(roleId);
            login.setTimestamp(0L);
            String expectSign = TokenUtils.getLoginToken(login.getUid(), login.getRoleId(), login.getTimestamp());
            login.setSign(expectSign);
            login.setDevice(PsDeviceType.ANDROID);
            login.setLanguage("Chinese");
            login.setDeviceId(String.valueOf(threadNum));
            login.setRollback(false);
            login.setGaid(String.valueOf(threadNum));
            login.setAppKey("com.ninestudio.starlight.sg");
            login.setClientVersion("1.0.0");
            login.setCountry("CN");
            login.setPlatform(1);
            login.setChannelF("");
            login.setLoginToken(loginToken);
            sendMsg(session, login);
            session.getPlayerData().setMsgSendService(this);
            session.getPlayerData().setSession(session);
        } catch (Exception e) {
            logger.error("error when access account system", e);
        }
    }


    /**
     * *************玩家消息总处理方法***************
     *
     * @param jmeterClient
     */
    public void sendNewMsg(AbstractJmeterClient jmeterClient) {
        try {

            ClientNetSession session = jmeterClient.getClientSession();
            //先加资源
            logger.info("begin add currency");
            addMoney(session);
            logger.info("begin upgrade vip");
            upgradeVip(session);
            logger.info("begin lotteryHeroGet");
            lotteryHeroGet(session);
            logger.info("begin player move");
            playerMapMove(session);
            logger.info("begin level up castle by GM");
            gmLvlCastle(session);
            while (!jmeterClient.isFinish()) {
                try {
                    logger.info("begin syncTime");
                    syncTime(session);
                    logger.info("begin finishAndRewardChapterMission");
                    finishAndRewardChapterMission(session);
                    logger.info("begin finishAndRewardChapterMission");
                    finishAndRewardChapterMission(session);
                    logger.info("begin landBuild");
                    landBuild(session);
                    logger.info("begin castleUpgrade");
                    landUpgrade(session);
                    logger.info("begin finishAndRewardChapterMission");
                    finishAndRewardChapterMission(session);
                    logger.info("begin prepareSetout");
                    prepareSetout(session);
                    logger.info("begin searchNpc");
                    searchNpc(session);
                    logger.info("begin setout");
                    setout(session);
                    logger.info("begin createOrJoinAlliance");
                    createOrJoinAlliance(session);
                    logger.info("begin getMissionView");
                    getMissionView(session);
                    logger.info("begin rewardMission");
                    rewardMission(session);
                } catch (Exception e) {
                    logger.error("send new msg error in single loop~", e);
                }
            }
        } catch (Exception e) {
            logger.error("send new msg eerror~", e);
        }
    }

    public void addMoney(ClientNetSession session) {
        for (Currency currency : Currency.values()) {
            if (currency != null && currency.getId() < 20) {
                sendGMChatMsg(session, "#money " + currency.getId() + " 99999999");
            }
        }
    }

    public void gmLvlCastle(ClientNetSession session) {
        int buildLevel = new Random().nextInt(10) + 5;
        sendGMChatMsg(session, "#buildingSetLevel 10001 " + buildLevel);
    }

    public void upgradeVip(ClientNetSession session) {
        sendGMChatMsg(session, "#item 10108001 10000");
        sendGMChatMsg(session, "#itemUse 10108001 10000");
    }

    public void lotteryHeroGet(ClientNetSession session) {
        for (int i = 0; i < 10; i++) {
            CgOpenPubTreasureBoxPlus openBox = new CgOpenPubTreasureBoxPlus();
            openBox.setIsFree(false);
            openBox.setTenContinuous(true);
            openBox.setMetaId("15");
            sendMsg(session, openBox);
        }
    }

    private void sendGMChatMsg(ClientNetSession session, String msg) {
        CgChatRoomSendMessage message = new CgChatRoomSendMessage();
        message.setRoomId("country.room.3");
        message.setToken("-1|1565665491502");
        message.setMessage(msg);
        sendMsg(session, message);
    }

    public void syncTime(ClientNetSession session) {
        CgSynchronizeTime synchronizeTime = syncTime();
        sendMsg(session, synchronizeTime);
    }

    public CgSynchronizeTime syncTime() {
        CgSynchronizeTime synchronizeTime = new CgSynchronizeTime();
        synchronizeTime.setClientTime((int) (System.currentTimeMillis() / 1000));
        synchronizeTime.setClientUTCTime(getUTCTimeStr());
        return synchronizeTime;
    }

    public long getUTCTimeStr() {
        Calendar cal = Calendar.getInstance();
        TimeZone tz = TimeZone.getTimeZone("GMT");
        cal.setTimeZone(tz);
        return cal.getTimeInMillis();// 返回的UTC时间戳
    }

    private boolean isChapterFinish(PlayerData playerData, int chapterId) {
        GcChapterMissionInfo chapterMissionInfo = playerData.getChapterMissionInfo();
        if (chapterMissionInfo == null) {
            logger.info("chapter is null~");
            return false;
        }
        if (Integer.parseInt(chapterMissionInfo.chapterId) > chapterId) {
            return true;
        }
        return false;
    }

    /**
     * 批量校验建筑最大等级是否满足条件
     *
     * @param cityInfo
     * @param conditions
     * @return
     */
    private boolean checkBuildingLevel(PsCityInfo cityInfo, IntKeyIntValue... conditions) {
        for (IntKeyIntValue condition : conditions) {
            if (getMaxLandLevel(cityInfo, condition.getId()) < condition.getCount()) {
                return false;
            }
        }

        return true;
    }


    private int getMaxLandLevel(PsCityInfo cityInfo, int group) {
        PsLandInfo landInfo = getMaxLand(cityInfo, group);
        if (landInfo == null) {
            return 0;
        }
        return landInfo.getLevel();
    }

    private PsLandInfo getMaxLand(PsCityInfo cityInfo, int group) {
        int level = 0;
        PsLandInfo maxLand = null;
        for (PsLandInfo landInfo : cityInfo.lands) {
            if (landInfo.getBuildingGroupId() == group && landInfo.getLevel() >= level) {
                level = landInfo.getLevel();
                maxLand = landInfo;
            }
        }

        return maxLand;
    }

    private boolean hasEnoughCurrency(PlayerData playerData, IntKeyIntValue... costArr) {
        if (costArr == null || costArr.length == 0) {
            return true;
        }
        for (IntKeyIntValue cost : costArr) {
            if (!hasEnoughCurrency(playerData, Currency.findById(cost.getId()), cost.getCount())) {
                return false;
            }
        }

        return true;
    }

    private boolean hasEnoughCurrency(PlayerData playerData, Map<Currency, Long> costMap) {
        for (Currency currency : costMap.keySet()) {
            long value = costMap.get(currency);
            if (!hasEnoughCurrency(playerData, currency, value)) {
                return false;
            }
        }
        return true;
    }

    private boolean hasEnoughCurrency(PlayerData playerData, Currency currency, long value) {
        PropKey propKey = PropKey.getInstance(currency.getProp());
        if (propKey.getPsIntPropKey() == null)
            return false;
        long currencyValue = playerData.getPlayerInfo().getIntProperties().get(propKey.getPsIntPropKey());
        if (currencyValue < value) {
            return false;
        }

        return true;
    }

    private boolean hasHero(PlayerData playerData, String heroMetaId) {
        for (PsHeroInfo heroInfo : new ArrayList<>(playerData.getHeros().values())) {
            if (heroInfo.metaId.equals(heroMetaId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 驻扎建筑小屋
     * @param session
     * @return
     */
//	private boolean heroStation(ClientNetSession session) {
//		PlayerData playerData = session.getPlayerData();
//		Map<String , BuildingConfig.BuildingMeta> allMetas = configService.getConfig(BuildingConfig.class).getAll();
//
//		for(BuildingConfig.BuildingMeta meta : getConstructBuildMetas(allMetas)) {
//			PsLandInfo landInfo = getSingleLandByGroupId(playerData, meta.getGroup());
//			if(landInfo == null) {
//				logger.info("heroStation land should not be null~");
//				continue;
//			}
//			if(isBuildingStation(playerData, landInfo)) {
//				continue;
//			}
//
//			CgDispatchHeroToBuild build = new CgDispatchHeroToBuild();
//			build.setBuildId(landInfo.getId());
//
//			sendMsg(session, build);
//			break;
//		}
//
//		return true;
//	}


    /**
     * 建筑是否已经被驻扎过了
     *
     * @param playerData
     * @param landInfo
     * @return
     */
    private boolean isBuildingStation(PlayerData playerData, PsLandInfo landInfo) {
        Map<PsCityWorkQueueType, Map<Long, String>> stationMap = playerData.getHeroStationMap();
        PsCityWorkQueueType[] buildTypes = getBuildingWorkdType();
        for (PsCityWorkQueueType buildType : buildTypes) {

            Map<Long, String> buildingStation = stationMap.get(buildType);
            if (buildingStation != null && buildingStation.size() > 0) {
                return true;
            }
        }
        return false;
    }

    private PsCityWorkQueueType[] getBuildingWorkdType() {
        return new PsCityWorkQueueType[]{PsCityWorkQueueType.Build1, PsCityWorkQueueType.Build2, PsCityWorkQueueType.Build3, PsCityWorkQueueType.Build4, PsCityWorkQueueType.Build5, PsCityWorkQueueType.Build6, PsCityWorkQueueType.Build7, PsCityWorkQueueType.Build8};
    }

    /**
     * 获取某个groupId的建筑信息~每次都获取第一个
     *
     * @param playerData
     * @param groupId
     * @return
     */
    private PsLandInfo getSingleLandByGroupId(PlayerData playerData, int groupId) {
        List<PsLandInfo> all = getAllLandByGroupId(playerData, groupId);
        return all == null || all.size() == 0 ? null : all.get(0);
    }

    private List<PsLandInfo> getAllLandByGroupId(PlayerData playerData, int groupId) {
        List<PsLandInfo> all = new ArrayList<>();
        for (PsLandInfo landInfo : new ArrayList<>(playerData.getCityInfo().getLands())) {
            if (landInfo.getBuildingGroupId() == groupId) {
                all.add(landInfo);
            }
        }

        return all;
    }

    /**
     * 获取工人小屋的建筑列表
     *
     * @return
     */
    private List<BuildingConfig.BuildingMeta> getConstructBuildMetas(Map<String, BuildingConfig.BuildingMeta> allMetas) {
        List<BuildingConfig.BuildingMeta> metas = new ArrayList<>();
        for (BuildingConfig.BuildingMeta meta : allMetas.values()) {
            if (meta.getAttributeValues() == null) {
                continue;
            }
            for (AttributeValue attributeValue : meta.getAttributeValues()) {
                if (attributeValue.getAttributeType() == null) {
                    continue;
                }
                if (attributeValue.getAttributeType().getId() == PsIntProperty.BUILDING_QUEUE_NUM_ADD_VALUE.getValue()) {
                    metas.add(meta);
                    break;
                }
            }
        }

        return metas;
    }

    /**
     * 建筑建造
     *
     * @param session
     * @return
     */
    private boolean landBuild(ClientNetSession session) {
        Map<String, BuildingConfig.BuildingMeta> allMetas = configService.getConfig(BuildingConfig.class).getAll();
        for (BuildingConfig.BuildingMeta meta : allMetas.values()) {
            boolean isSuccess = doSingleLandBuild(session, meta.getGroup());
            if (!isSuccess) {
                break;
            }
        }

        return true;
    }

    private boolean doSingleLandBuild(ClientNetSession session, int group) {
        PlayerData playerData = session.getPlayerData();
        BuildingConfig.BuildingMeta meta = configService.getConfig(BuildingConfig.class).getWithMinLevel(group);
        if (meta.getGroup() == BuildingGroup.ROAD.getGroup()) {
            return true;
        }

        PsWorkQueue workQueue = hasFreeBuildQueue(playerData);
        if (workQueue == null) {
            return true;
        }
        if (!checkAndLvlupBuildCondition(session, meta.getConditions())) {
            return true;
        }
        TwoTuple<Integer, Integer> point = canBuild(playerData, meta);
        if (point != null) {
            String buildGmStr = "#landBuild " + point.getFirst() + " " + point.getSecond() + " " + meta.getId();
            sendGMChatMsg(session, buildGmStr);

            StringBuilder conditionStr = new StringBuilder();
            for (IntKeyIntValue c : meta.getConditions()) {
                conditionStr.append(c);
            }
            logger.info("begin build building  [{}] , currLevel is [{}], condition is [{}]", meta.getId(), meta.getLevel(), conditionStr.toString());

        } else {
            return false;
        }
        return true;
    }

    private PsWorkQueue hasFreeBuildQueue(PlayerData playerData) {
        if (playerData.getQueueMap() == null || playerData.getQueueMap().size() == 0) {
            return null;
        }
        PsCityWorkQueueType[] buildTypes = getBuildingWorkdType();
        for (PsCityWorkQueueType buildType : buildTypes) {
            PsWorkQueue queue = playerData.getQueueMap().get((short) buildType.getValue());
            if (queue == null) {
                continue;
            }

            if (queue.getWork() == null) {
                return queue;
            }
        }

        return null;
    }

    private TwoTuple<Integer, Integer> canBuild(PlayerData playerData, BuildingConfig.BuildingMeta meta) {
        //校验资源
        //校验资源
        Map<Currency, Long> costMap = new HashMap<>();
        for (CurrencyValue cost : meta.getNeedResources()) {
            costMap.put(cost.getCurrency(), cost.getValue());
        }
        if (!hasEnoughCurrency(playerData, costMap)) {
            logger.info("resource not enough~");
            return null;
        }
        //校验建造数量
        int alreadyHave = getAllLandByGroupId(playerData, meta.getGroup()).size();
        int canMaxCnt = getCanBuildMaxCount(playerData, meta);
        if (alreadyHave >= canMaxCnt) {
            return null;
        }

        Set<TwoTuple> occupyPoints = getOccupyPoints(playerData);

        PsLandInfo castle = getSingleLandByGroupId(playerData, BuildingGroup.CASTLE.getGroup());
        //寻找坐标点
        int buildSize = meta.getSize();
        int round = buildSize;
        int initI = castle.getX() - round;
        int initJ = castle.getY() - round;
        int roundEndI = castle.getX() + round;
        int roundEndJ = castle.getY() + round;
        int curI = initI;
        int curJ = initJ;
        boolean find = false;
        while (!find) {
            if (round >= 10) {
                logger.info("find build can not find point! round is [{}], point castle point is [{},{}], this round init point is [{},{}], current point is [{},{}]", round, castle.getX(), castle.getY(), initI, initJ, curI, curJ);
                break;
            }

            if (curI == initI) {
                if (curJ != roundEndJ) {
                    curJ++;
                } else {
                    curI++;
                }
            } else if (curI == roundEndI) {
                if (curJ != initJ) {
                    curJ--;
                } else {
                    curI--;
                }
            } else if (curJ == roundEndJ) {
                if (curI != roundEndI) {
                    curI++;
                } else {
                    curJ--;
                }
            } else if (curJ == initJ) {
                if (curI != initI + 1) {
                    curI--;
                } else {
                    round += 1;
                    initI = castle.getX() - round;
                    initJ = castle.getY() - round;
                    roundEndI = castle.getX() + round;
                    roundEndJ = castle.getY() + round;
                    curI = initI;
                    curJ = initJ;
                }
            }

            boolean isConfilicate = false;
            Set<TwoTuple<Integer, Integer>> newBuildingPoints = getSingleBuildOccupyPoints(meta.getGroup(), meta.getLevel(), curI, curJ);
            for (TwoTuple<Integer, Integer> newPoint : newBuildingPoints) {
                if (occupyPoints.contains(new TwoTuple(newPoint.getFirst(), newPoint.getSecond()))) {
                    isConfilicate = true;
                    break;
                }
                if (isPositionOccupy(playerData, meta.getGroup(), newPoint.getFirst(), newPoint.getSecond(), false)) {
                    isConfilicate = true;
                    break;
                }
            }

            if (isConfilicate) {
                continue;
            }

            find = true;
            logger.info("find build round is [{}], point castle point is [{},{}], this round init point is [{},{}], current point is [{},{}]", round, castle.getX(), castle.getY(), initI, initJ, curI, curJ);

            break;
        }
        if (!find) {
            return null;
        }

        logger.info("find point to create build {}", meta.getId());

        return new TwoTuple<>(curI, curJ);
    }

    /**
     * 检测坐标是否可放置
     *
     * @param playerData
     * @param groupId
     * @param x
     * @param y
     * @param ignoreZombie
     * @return
     */
    public boolean isPositionOccupy(PlayerData playerData, int groupId, int x, int y, boolean ignoreZombie) {
        // 2.建筑
        if (hasBuildCover(playerData, x, y)) {
            return true;
        }
        return false;
    }

    /**
     * 坐标上有没有建筑覆盖
     *
     * @param playerData
     * @param x
     * @param y
     * @return
     */
    public boolean hasBuildCover(PlayerData playerData, int x, int y) {
        Set<TwoTuple> buildPoints = getOccupyPoints(playerData);
        return buildPoints.contains(new TwoTuple(x, y));
    }

    private int getCanBuildMaxCount(PlayerData playerData, BuildingConfig.BuildingMeta meta) {
//		int baseNum = playerData.getBuildingCanBuildMax().getOrDefault(meta.getGroup(), 0);
//		int proptyNum = getPropertyBuildingNum(playerData, meta.getGroup());
//		Map<Integer, Integer> extraBuildingNum = playerData.getPlayerInfo().getExtraBuildingNums();
//		int extraNum = 0;
//		if(extraBuildingNum != null) {
//			extraNum = playerData.getPlayerInfo().getExtraBuildingNums().getOrDefault(meta.getGroup(), 0);
//		}
//
//		return baseNum + proptyNum + extraNum;
        // TODO: 2020/11/10 临时注释掉~每个建筑允许建造1个
        return 1;
    }

    private Set<TwoTuple> getOccupyPoints(PlayerData playerData) {
        Set<TwoTuple> points = new HashSet<>();
        for (PsLandInfo landInfo : new ArrayList<>(playerData.getCityInfo().lands)) {
            points.addAll(getSingleBuildOccupyPoints(landInfo.buildingGroupId, landInfo.level, landInfo.x, landInfo.y));
        }

        return points;
    }

    private Set<TwoTuple<Integer, Integer>> getSingleBuildOccupyPoints(int group, int level, int buildX, int buildY) {
        Set<TwoTuple<Integer, Integer>> points = new HashSet<>();
        BuildingConfig.BuildingMeta buildingMeta = configService.getConfig(BuildingConfig.class).get(group, level);
        if (buildingMeta == null) {
            return points;
        }
        int size = buildingMeta.getSize() - 1;
        for (int i = 0; i <= size; i++) {
            for (int j = 0; j <= size; j++) {
                int x = buildX + i;
                int y = buildY + j;
                points.add(new TwoTuple<Integer, Integer>(x, y));
            }
        }
        return points;
    }


    /**
     * 重算建筑可建造数量上限
     */
    public void resetBuildingMaxNum(PlayerData playerData) {
        List<PsLandInfo> landInfos = playerData.getCityInfo().getLands();
        Map<Integer, Integer> buildingGroupAndMaxLevel = new HashMap<>();
        for (PsLandInfo cityBuild : landInfos) {
            buildingGroupAndMaxLevel.compute((int) cityBuild.getBuildingGroupId(), (k, v) -> v == null || v < cityBuild.getLevel() ? cityBuild.getLevel() : v);
        }
        if (buildingGroupAndMaxLevel.size() <= 0) {
            return;
        }
        Map<Integer, Integer> buildingMaxNum = playerData.getBuildingCanBuildMax();
        buildingMaxNum.clear();
        BuildingConfig buildingConfig = configService.getConfig(BuildingConfig.class);
        for (Map.Entry<Integer, Integer> entry : buildingGroupAndMaxLevel.entrySet()) {
            Integer groupId = entry.getKey();
            Integer level = entry.getValue();
            // 所有等级
            for (int i = 0; i <= level; i++) {
                BuildingConfig.BuildingMeta buildingMeta = buildingConfig.get(groupId, i);
                if (buildingMeta != null) {
                    IntKeyIntValue[] newBuildings = buildingMeta.getNewBuildings();
                    if (newBuildings != null && newBuildings.length > 0) {
                        for (IntKeyIntValue keyValue : newBuildings) {
                            buildingMaxNum.compute(keyValue.getId(), (k, v) -> v == null ? keyValue.getCount() : keyValue.getCount() + v);
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查前置建筑
     *
     * @param session
     * @param conditions
     * @return
     */
    public boolean checkAndLvlupBuildCondition(ClientNetSession session, IntKeyIntValue[] conditions) {
//		PlayerData playerData = session.getPlayerData();
//		if (conditions == null || conditions.length == 0) {
//			return true;
//		}
//		boolean result = true;
//		for (IntKeyIntValue v : conditions) {
//			PsLandInfo cityBuild = getMaxLand(playerData.getCityInfo(), v.getId());
//			if (cityBuild == null || cityBuild.getLevel() < v.getCount()) {
//				result = false;
//				doSingleLandUpgradeOrBuild(session, v.getId());
//				continue;
//			}
//		}
//		return result;
        // TODO: 2020/11/10 临时注释~直接升级和建造~
        return true;
    }

    /**
     * 建筑升级
     *
     * @param session
     * @return
     */
    private boolean landUpgrade(ClientNetSession session) {
        PlayerData playerData = session.getPlayerData();

        PsHeroInfo freeHero = getQueueFreeHero(playerData);
        if (freeHero == null) {
            logger.info("free hero is null");
            return false;
        }
        for (PsLandInfo landInfo : new ArrayList<>(playerData.getCityInfo().getLands())) {
            doSingleLandUpgradeOrBuild(session, landInfo.buildingGroupId);
        }

        return true;
    }

    private void doSingleLandUpgradeOrBuild(ClientNetSession session, int group) {
        PlayerData playerData = session.getPlayerData();
        PsLandInfo landInfo = getMaxLand(playerData.getCityInfo(), group);
        if (landInfo == null) {
            //需要创建建筑
            doSingleLandBuild(session, group);
        } else {
            //需要建筑升级
            doSingleLandUpgrade(session, group);
        }
    }

    private void doSingleLandUpgrade(ClientNetSession session, int group) {
        PlayerData playerData = session.getPlayerData();
        PsLandInfo landInfo = getMaxLand(playerData.getCityInfo(), group);
//		if(landInfo.getBuildingGroupId() != BuildingGroup.CASTLE.getGroup() && landInfo.getBuildingGroupId() != BuildingGroup.BROADCASTING_STATION.getGroup()) {
//			return;
//		}
        PsWorkQueue workQueue = hasFreeBuildQueue(playerData);
        if (workQueue == null) {
            return;
        }
        BuildingConfig.BuildingMeta meta = configService.getConfig(BuildingConfig.class).get(landInfo.getBuildingGroupId(), landInfo.level + 1);
        if (meta == null) {
            return;
        }
        if (!checkAndLvlupBuildCondition(session, meta.getConditions())) {
            return;
        }
        // TODO: 2020/11/10 临时注释掉~先不判断cover的覆盖
//		if(landInfo.isSetCoverId()) {
//			String coverId = landInfo.coverId;
//			CoverConfig.CoverMeta coverMeta = configService.getConfig(CoverConfig.class).getCoverMetaById(coverId);
//			if(coverMeta == null) {
//				return;
//			}
//			if(!checkAndLvlupBuildCondition(session, coverMeta.getConditions())) {
//				return;
//			}
//			Byte innerMapId = Byte.parseByte(coverMeta.getRegion());
//			if(playerData.getCityInfo().getUnlocked() != null && !playerData.getCityInfo().getUnlocked().contains(innerMapId)) {
//				unlockInnerMap(session, String.valueOf(innerMapId), null);
//				return;
//			}
//		}

        PsHeroInfo freeHero = getQueueFreeHero(playerData);
        if (freeHero == null) {
            logger.info("free hero is null");
            return;
        }

        StringBuilder conditionStr = new StringBuilder();
        for (IntKeyIntValue c : meta.getConditions()) {
            conditionStr.append(c);
        }
        logger.info("begin upgrade building [{}] [{}] , currLevel is [{}], condition is [{}]", landInfo.getId(), meta.getId(), landInfo.getLevel(), conditionStr.toString());

        String upgradeGm = "#landUpgrade " + landInfo.getId() + " " + workQueue.id;
        sendGMChatMsg(session, upgradeGm);
//		CgLandUpgrade build = new CgLandUpgrade();
//		build.setAddTime(1L);
//		build.setBuildingId(landInfo.getId());
//		build.setImmediate(false);
//		build.setQueueId(workQueue.id);
//
//		sendMsg(session, build);
    }

    private PsHeroInfo getQueueFreeHero(PlayerData playerData) {
        for (PsWorkQueue workQueue : new ArrayList<>(playerData.getQueueMap().values())) {
            String queueId = workQueue.getId();
            CityWorkQueueConfig.CityWorkQueueMeta meta = configService.getConfig(CityWorkQueueConfig.class).getMeta(queueId);
            if (meta == null) {
                logger.info("getQueueFreeHero work queue meta is null");
                continue;
            }
            if (workQueue.getWork() != null) {
                continue;
            }
            int buildingGroup = meta.getGroupID();
            PsLandInfo landInfo = getSingleLandByGroupId(playerData, buildingGroup);
            if (landInfo == null) {
                logger.info("getQueueFreeHero landInfo is null");
                continue;
            }
            String heroStationInBuilding = getHeroStationInBuilding(playerData, landInfo.getId());
            if (heroStationInBuilding == null) {
                logger.info("getQueueFreeHero building no station hero");
                continue;
            }
            return getHero(playerData, heroStationInBuilding);
        }

        return null;
    }

    private String getHeroStationInBuilding(PlayerData playerData, long buildingId) {
        for (Map<Long, String> buildAndHero : new ArrayList<>(playerData.getHeroStationMap().values())) {
            String heroId = buildAndHero.get(buildingId);
            if (heroId != null) {
                return heroId;
            }
        }
        return null;
    }

    private PsHeroInfo getHero(PlayerData playerData, String heroId) {
        if (StringUtils.isBlank(heroId)) {
            return null;
        }
        return playerData.getHeros().values().stream().filter(hero -> hero.getMetaId().equals(heroId)).findAny().get();
    }

    private boolean finishAndRewardChapterMission(ClientNetSession session) {
        List<PsMission> chapters = session.getPlayerData().getChapterMissionInfo().missions;
        if (chapters != null && chapters.size() > 0) {
            boolean allFinished = true;
            for (PsMission mission : new ArrayList<>(chapters)) {
                if (mission.completed && !mission.award) {
                    CgChapterMissionReward chapterReward = new CgChapterMissionReward();
                    chapterReward.setMetaId(mission.metaId);
                    sendMsg(session, chapterReward);
                } else if (!mission.award) {
                    allFinished = false;
                }
            }

            if (allFinished) {
                CgChapterMissionRewardAll rewardAll = new CgChapterMissionRewardAll();
                rewardAll.setChapterId(session.getPlayerData().getChapterMissionInfo().chapterId);

                sendMsg(session, rewardAll);
            }
        }
        if (session.getPlayerData().getChapterMissionInfo().missions.size() != 0) {
            String msg = "#finishAllChapterMission";
            sendGMChatMsg(session, msg);
        }
        return true;
    }

    /**
     * 为了出征做准备
     *
     * @param session
     * @return
     */
    private boolean prepareSetout(ClientNetSession session) {
//		if(!session.getPlayerData().isSetoutPrepared()) {
        String msg = "#prepareSetout";
        sendGMChatMsg(session, msg);
        session.getPlayerData().setSetoutPrepared(true);
//		}
        return true;
    }

    private boolean searchNpc(ClientNetSession session) {
//		if(session.getPlayerData().getNpcPoint() == null) {
        CgMapSearch mapSearch = new CgMapSearch();
        mapSearch.setType(PsSearchType.NPC);
        mapSearch.setId("1");
        mapSearch.setLevel(1);
        sendMsg(session, mapSearch);
//		}
        return true;
    }

    private boolean setout(ClientNetSession session) {
        PlayerData playerData = session.getPlayerData();

//		if(playerData.getTruckMap() == null || playerData.getTruckMap().size() == 0) {
//			return false;
//		}
//
//		String truckId = getFreeTruck(playerData);
//		if(truckId == null) {
//			return false;
//		}
//		if(playerData.getSetoutCnt() >= MAX_SETOUT_CNT) {
//			return false;
//		}
//		if(playerData.getNpcPoint() != null && !playerData.isAlreadySetout()) {
//
//			playerData.setAlreadySetout(true);
//
//			CgArmySetout setout = new CgArmySetout();
//			setout.setSetoutType(PsArmyType.ATTACK);
//			setout.setNodeType(PsMapNodeType.NPC);
//			setout.setServerPos((byte) 0);
//			sendMsg(session, setout);
//
//			playerData.setSetoutCnt(playerData.getSetoutCnt() + 1);
//		}
//		return true;
        return false;
    }

    public void createOrJoinAlliance(ClientNetSession session) {
        PlayerData playerData = session.getPlayerData();

        sendGMChatMsg(session, "#totalCnt");

        String allianceId = playerData.getCityInfo().getAllianceId();
        if (StringUtils.isBlank(allianceId)) {
            int roleCnt = worldMapService.getRoleCnt();
            logger.info("createOrJoinAlliance log~totalCnt is [{}],isAlreadyCreateOrApply is [{}] ", roleCnt, playerData.isAlreadyCreateOrApply());
            if (!playerData.isAlreadyCreateOrApply() && roleCnt > 0) {
                long playerId = Long.parseLong(playerData.getCityInfo().id);
                int totalNum = worldMapService.getAllianceSimpleInfoMap().size();
                if (totalNum < roleCnt / AbstractJmeterClient.getAllianceRoleNum()) {
                    CgAllianceCreate create = new CgAllianceCreate();

                    create.setAutoAgree(true);
                    create.setBadge("lianmeng_tuan3");
                    create.setBadgeColor(3);
                    create.setBanner("lianmeng_beijing05");
                    create.setBannerColor(3);
                    create.setCountry("CN");
                    create.setDeclaration("123123123");
                    create.setLanguage("6");
                    create.setName(playerData.getCityInfo().name.replace(".", ""));
                    create.setShortName(playerData.getCityInfo().name.substring(playerData.getCityInfo().name.length() - 4));
                    //			create.setShortName(autoGenericCode(String.valueOf(playerData.getAllianceSimpleInfoMap().size()), 4));

                    sendMsg(session, create);

                    playerData.setAlreadyCreateOrApply(true);
                    sendGMChatMsg(session, "#totalCnt");

                } else {
                    CgAllianceAutoJoin request = new CgAllianceAutoJoin();
                    sendMsg(session, request);
                    playerData.setAlreadyCreateOrApply(true);
                }
            }
        } else {
            PsNewAllianceSimpleInfo allianceSimpleInfo = worldMapService.getAllianceSimpleInfoMap().get(allianceId);
            if (allianceSimpleInfo == null) {
                logger.error("alliance not exist~");
                return;
            }

            if (playerData.getRank() < 4) {
                return;
            }

            playerMapMove(session);

            Point point = worldMapService.getAvailblePoint(playerData, Point.getInstance(playerData.getCityInfo().x, playerData.getCityInfo().y), PsMapNodeType.ALLIANCE_FORT, WorldMapServiceImpl.ALLIANCE_FORT_ID);
            if (point == null) {
                logger.error("no point to create alliance building ");
                return;
            }

            sendGMChatMsg(session, "#allianceCurrencyAdd");
        }
    }

    /**
     * 不够位数的在前面补0，保留num的长度位数字
     *
     * @param code
     * @return
     */
    private String autoGenericCode(String code, int num) {
        String result = "";
        // 保留num的位数
        // 0 代表前面补充0
        // num 代表长度为4
        // d 代表参数为正数型
        result = String.format("%0" + num + "d", Integer.parseInt(code) + 1);

        return result;
    }

    private void playerMapMove(ClientNetSession session) {
        int centerX = session.getPlayerData().getCityInfo().getX();
        int centerY = session.getPlayerData().getCityInfo().getY();

        int rangeX = 3;
        int rangeY = 5;

        CgMapPlayerMove move = new CgMapPlayerMove();
        move.setServerGroup(1);
        move.setServerPos((byte) 0);

        sendMsg(session, move);

        move = new CgMapPlayerMove();
        move.setServerGroup(1);
        move.setServerPos((byte) 0);

        sendMsg(session, move);

        move = new CgMapPlayerMove();
        move.setServerGroup(1);
        move.setServerPos((byte) 0);

        sendMsg(session, move);

        move = new CgMapPlayerMove();
        move.setServerGroup(1);
        move.setServerPos((byte) 0);

        sendMsg(session, move);

        move = new CgMapPlayerMove();
        move.setServerGroup(1);
        move.setServerPos((byte) 0);
        move.setX(centerX);
        move.setY(centerY);

        sendMsg(session, move);
    }

    public void getMissionView(ClientNetSession session) {
        CgMissionList list = new CgMissionList();
        sendMsg(session, list);
    }

    public void rewardMission(ClientNetSession session) {
        PlayerData playerData = session.getPlayerData();

        Set<String> missionSet = new CopyOnWriteArraySet<>(playerData.getMissionMap().keySet());
        for (String missionId : missionSet) {
            PsMission mission = playerData.getMissionMap().get(missionId);
            if (mission != null && mission.completed) {
                CgMissionReward reward = new CgMissionReward();
                reward.setMetaId(missionId);
                sendMsg(session, reward);
            }
        }
    }


    // 模拟客户端执行顺序

    public void enterMap(ClientNetSession session, PsPoint point) {
        CgEnterMap enterMap = new CgEnterMap();
        enterMap.setX(point.getX());
        enterMap.setY(point.getY());
        sendMsg(session, enterMap);
    }

    public void getCityWorkQueueList(ClientNetSession session) {
        CgCityWorkQueueList list = new CgCityWorkQueueList();
        sendMsg(session, list);
    }

    public void getPubTreasureBoxPlusInfo(ClientNetSession session) {
        CgPubTreasureBoxPlusInfo list = new CgPubTreasureBoxPlusInfo();
        sendMsg(session, list);
    }

    public void getRecordList(ClientNetSession session, PsRecordType type) {
        CgRecordList list = new CgRecordList();
        list.setType(type);
        sendMsg(session, list);
    }

    public void getCgWatchtowerList(ClientNetSession session) {
        CgWatchtowerList list = new CgWatchtowerList();
        sendMsg(session, list);
    }

    public void getCgFavoriteGet(ClientNetSession session) {
        CgFavoriteGet list = new CgFavoriteGet();
        sendMsg(session, list);
    }

    public void getCgAcitivityList(ClientNetSession session) {
        CgAcitivityList list = new CgAcitivityList();
        sendMsg(session, list);
    }

    public void getCgMilestoneList(ClientNetSession session) {
        CgMilestoneList list = new CgMilestoneList();
        sendMsg(session, list);
    }

    public void getCgStoreList(ClientNetSession session) {
        CgStoreList list = new CgStoreList();
        sendMsg(session, list);
    }

    public void gmUnlockAllFuncs(ClientNetSession session) {
        sendGmCommand(session, "#unlockAllFuncs");
    }

    public void gmAddAllHeros(ClientNetSession session, int level, int star) {
        sendGmCommand(session, "#heroGetAll " + level + " " + star);
    }

    public void gmAddAllBuildings(ClientNetSession session, int level) {
        sendGmCommand(session, "#upgradeAllBuilding " + level);
    }

    private void sendGmCommand(ClientNetSession session, String message) {
        CgChatRoomSendMessage msg = new CgChatRoomSendMessage();
        msg.setRoomId("GM");
        msg.setMessage(message);
        msg.setToken("");
        sendMsg(session, msg);
    }


    // 竞技场挑战
    public void arenaChallenge(ClientNetSession session, Long rivalId, List<Integer> heroIds) {
        CgArenaChallenge msg = new CgArenaChallenge();
        msg.setRivalId(rivalId);
        msg.setHeroIds(heroIds);
        sendMsg(session, msg);
    }

    public void arenaSetDefence(ClientNetSession session, List<Integer> heroIds) {
        CgArenaSetDefenceLineup msg = new CgArenaSetDefenceLineup();
        msg.setHeroIds(heroIds);
        sendMsg(session, msg);
    }

    public void changeName(ClientNetSession session, String name) {
        CgPlayerNameChange msg = new CgPlayerNameChange();
        msg.setName(name);
        sendMsg(session, msg);
    }


}
