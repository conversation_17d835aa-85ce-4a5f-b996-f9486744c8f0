package com.lc.billion.icefire.client;

import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.IServerTypeDeterminer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.context.support.StaticApplicationContext;

import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

public class ClientMain {

    private static final Logger logger = LoggerFactory.getLogger(ClientMain.class);
    private static final ReentrantLock LOCK = new ReentrantLock();
    private static final Condition STOP = LOCK.newCondition();

    private static boolean isLaunch = false;

    public static void main(String[] args) {
        logger.info("begin start client...");
        launch();
        com.lc.billion.icefire.client.Application.initNet();
        logger.info("end start client...");

        //主线程阻塞等待，守护线程释放锁后退出
        try {
//            Thread.sleep(10000000l);
            LOCK.lock();
            STOP.await();
        } catch (InterruptedException e) {
            logger.info(" service   stopped, interrupted by other thread!");
            e.printStackTrace();
        } finally {
            LOCK.unlock();
            logger.info("finally quit game......");
        }
    }

    public synchronized static void launch() {
        if(!isLaunch) {
            isLaunch = true;
            ClassLoader origLoader = Thread.currentThread().getContextClassLoader();
            ClassLoader extensionLoader = com.lc.billion.icefire.client.LaunchHelper.class.getClassLoader();
            Thread.currentThread().setContextClassLoader(extensionLoader);
            try {
                // 初始化spring
                StaticApplicationContext parent = new StaticApplicationContext();
                parent.getBeanFactory().registerSingleton("configCenter", new ConfigCenter("LS_ENV_KEY_GAMESERVER_ID","127.0.0.1:2181","/LS",new IServerTypeDeterminer() {

                    @Override
                    public ServerType getServerType(int serverId) {
                        return ServerType.GAME;
                    }

                }));
                parent.refresh();

                ApplicationContext applicationContext = new ClassPathXmlApplicationContext(
                        new String[] { "spring/applicationContext*.xml" }, true, parent);
                Application.setApplicationContext(applicationContext);
                com.lc.billion.icefire.game.Application.setApplicationContext(applicationContext);

                // 加载配置
//                ServerConfigManager.getInstance().loadConfig();

                LaunchHelper.preLaunch();

                // 加载配置
                ClientConfigManager.getInstance().loadConfig();

                addHook();
            } catch (Exception e) {
                logger.error("spring init error", e);
                isLaunch = false;
            } finally {
                Thread.currentThread().setContextClassLoader(origLoader);
            }
        } else {
            logger.info("spring already inited~");
        }
    }

    /**
     * Created on 2017年12月12日
     * <p>
     * Discription:[添加一个守护线程]
     *
     *
     */
    private static void addHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {

            logger.info("jvm exit, all service stopped.");

            try {
                LOCK.lock();
                STOP.signal();
            } finally {
                LOCK.unlock();
            }
        }, "ClientMain-shutdown-hook"));
    }

}
