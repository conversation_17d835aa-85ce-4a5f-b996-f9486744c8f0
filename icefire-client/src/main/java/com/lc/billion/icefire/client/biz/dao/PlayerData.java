package com.lc.billion.icefire.client.biz.dao;

import com.lc.billion.icefire.client.biz.impl.MsgSendService;
import com.lc.billion.icefire.client.jmeter.client.AbstractJmeterClient;
import com.lc.billion.icefire.client.net.ClientNetSession;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.PsCityWorkQueueType;
import com.lc.billion.icefire.protocol.constant.PsRecordType;
import com.lc.billion.icefire.protocol.structure.*;
import com.simfun.sgf.common.tuple.TwoTuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class PlayerData {
    public static final Logger logger = LoggerFactory.getLogger(PlayerData.class);
    private Long serverTime;
    private PsPlayerInfo playerInfo;
    private PsCityInfo cityInfo;
    private Map<Short, PsWorkQueue> queueMap;
    private Map<String, PsCitySoldier> soldierMap;
    private Map<String, PsSoldierInfo> woundSoldierMap;
    private PsMission mainMission;
    private Map<String, PsMission> missionMap;
    private List<String> technologies;
    private Map<Integer, Long> intPropertyMap;
    private Map<Integer, Double> doublePropertyMap;
    private Map<String, PsHeroInfo> heros;
    //<队列类型，<建筑id,英雄id> >
    private Map<PsCityWorkQueueType, Map<Long, String>> heroStationMap;
    private GcChapterMissionInfo chapterMissionInfo;
    private PsAllianceInfo allianceInfo;
    private int rank = -1;
    private Map<Point, PsMapNode> mapNodes = new HashMap<>();
    private MsgSendService msgSendService;
    private ClientNetSession session;


    //客户端自己计算的缓存
    private Map<Integer, Integer> buildingCanBuildMax = new HashMap<>();
    private boolean isSetoutPrepared = false;
    private TwoTuple<Integer, Integer> npcPoint = null;
    private boolean alreadySetout = false;
    //是否创建 或者 申请加入 联盟了
    private boolean alreadyCreateOrApply = false;
    private int setoutCnt = 0;

    private Map<String, Long> usableEmoji = new HashMap<>();
    /**
     * 背包  key entityId， PsItem
     */
    private Map<String, PsItem> bagMap = null;
    private Map<String, PsItem> itemMap = null;

    /**
     * 竞技场信息
     *
     * @return
     */
    private PsArenaPlayerInfo arenaPlayerInfo;
    private List<Long> arenaRivalIds = new ArrayList<>();

    public PsPlayerInfo getPlayerInfo() {
        return playerInfo;
    }

    public void setPlayerInfo(PsPlayerInfo playerInfo) {
        this.playerInfo = playerInfo;
    }

    public PsCityInfo getCityInfo() {
        return cityInfo;
    }

    public void setCityInfo(PsCityInfo cityInfo) {
        this.cityInfo = cityInfo;
    }

    public Map<Short, PsWorkQueue> getQueueMap() {
        return queueMap;
    }

    public void setQueueMap(Map<Short, PsWorkQueue> queueMap) {
        this.queueMap = queueMap;
    }

    public Map<String, PsCitySoldier> getSoldierMap() {
        return soldierMap;
    }

    public void setSoldierMap(Map<String, PsCitySoldier> soldierMap) {
        this.soldierMap = soldierMap;
    }

    public Map<String, PsSoldierInfo> getWoundSoldierMap() {
        return woundSoldierMap;
    }

    public void setWoundSoldierMap(Map<String, PsSoldierInfo> woundSoldierMap) {
        this.woundSoldierMap = woundSoldierMap;
    }

    public PsMission getMainMission() {
        return mainMission;
    }

    public void setMainMission(PsMission mainMission) {
        this.mainMission = mainMission;
    }

    public Map<String, PsMission> getMissionMap() {
        if (missionMap == null) {
            this.missionMap = new HashMap<>();
        }
        return missionMap;
    }

    public void setMissionMap(Map<String, PsMission> missionMap) {
        this.missionMap = missionMap;
    }

    public List<String> getTechnologies() {
        return technologies;
    }

    public void setTechnologies(List<String> technologies) {
        this.technologies = technologies;
    }

    public Map<Integer, Double> getDoublePropertyMap() {
        return doublePropertyMap;
    }

    public void setDoublePropertyMap(Map<Integer, Double> doublePropertyMap) {
        this.doublePropertyMap = doublePropertyMap;
    }

    public Map<String, PsHeroInfo> getHeros() {
        return heros;
    }

    public void setHeros(Map<String, PsHeroInfo> heros) {
        this.heros = heros;
    }

    public GcChapterMissionInfo getChapterMissionInfo() {
        return chapterMissionInfo;
    }

    public void setChapterMissionInfo(GcChapterMissionInfo chapterMissionInfo) {
        this.chapterMissionInfo = chapterMissionInfo;
    }

    public Map<Integer, Integer> getBuildingCanBuildMax() {
        return buildingCanBuildMax;
    }

    public void setBuildingCanBuildMax(Map<Integer, Integer> buildingCanBuildMax) {
        this.buildingCanBuildMax = buildingCanBuildMax;
    }

    public Map<Integer, Long> getIntPropertyMap() {
        return intPropertyMap;
    }

    public void setIntPropertyMap(Map<Integer, Long> intPropertyMap) {
        this.intPropertyMap = intPropertyMap;
    }

    public boolean isSetoutPrepared() {
        return isSetoutPrepared;
    }

    public void setSetoutPrepared(boolean setoutPrepared) {
        isSetoutPrepared = setoutPrepared;
    }

    public TwoTuple<Integer, Integer> getNpcPoint() {
        return npcPoint;
    }

    public void setNpcPoint(TwoTuple<Integer, Integer> npcPoint) {
        this.npcPoint = npcPoint;
    }

    public boolean isAlreadySetout() {
        return alreadySetout;
    }

    public void setAlreadySetout(boolean alreadySetout) {
        this.alreadySetout = alreadySetout;
    }

    public PsAllianceInfo getAllianceInfo() {
        return allianceInfo;
    }

    public void setAllianceInfo(PsAllianceInfo allianceInfo) {
        this.allianceInfo = allianceInfo;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public boolean isAlreadyCreateOrApply() {
        return alreadyCreateOrApply;
    }

    public void setAlreadyCreateOrApply(boolean alreadyCreateOrApply) {
        this.alreadyCreateOrApply = alreadyCreateOrApply;
    }

//    public Map<Point, PsMapNode> getMapNodes() {
//        return mapNodes;
//    }
//
//    public void setMapNodes(Map<Point, PsMapNode> mapNodes) {
//        this.mapNodes = mapNodes;
//    }

    public int getSetoutCnt() {
        return setoutCnt;
    }

    public void setSetoutCnt(int setoutCnt) {
        this.setoutCnt = setoutCnt;
    }

    public Map<PsCityWorkQueueType, Map<Long, String>> getHeroStationMap() {
        return heroStationMap;
    }

    public void setHeroStationMap(Map<PsCityWorkQueueType, Map<Long, String>> heroStationMap) {
        this.heroStationMap = heroStationMap;
    }


    public void fillHeroStationMap(Map<PsCityWorkQueueType, Map<Long, String>> heroStationMap) {
        if (this.heroStationMap == null) {
            this.heroStationMap = new HashMap<>();
        }
        if (heroStationMap == null) {
            return;
        }
        heroStationMap.forEach((type, heroMap) -> {
            Map<Long, String> oldHeroMap = this.heroStationMap.get(type);
            if (oldHeroMap == null) {
                oldHeroMap = new HashMap<>();
                this.heroStationMap.put(type, oldHeroMap);
            }
            oldHeroMap.putAll(heroMap);
        });
    }

    public void fillSoldierMap(List<PsCitySoldier> soldierInfos) {
        if (soldierInfos == null) {
            return;
        }
        if (this.soldierMap == null) {
            this.soldierMap = new HashMap<String, PsCitySoldier>();
        }
        for (PsCitySoldier soldierInfo : soldierInfos) {
            soldierMap.put(soldierInfo.cityId, soldierInfo);
        }
    }

    public void fillWoundSoldierMap(List<PsSoldierInfo> soldierInfos) {
        if (soldierInfos == null) {
            return;
        }
        if (this.woundSoldierMap == null) {
            this.woundSoldierMap = new HashMap<String, PsSoldierInfo>();
        }
        for (PsSoldierInfo soldierInfo : soldierInfos) {
            woundSoldierMap.put(soldierInfo.soldierMetaId, soldierInfo);
        }
    }

    public void fillMissionMap(List<PsMission> missions) {
        if (missions == null) {
            return;
        }
        if (this.missionMap == null) {
            this.missionMap = new HashMap<String, PsMission>();
        }
        for (PsMission mission : missions) {
            missionMap.put(mission.metaId, mission);
        }
    }

    public void fillIntPropertyMap(Map<Integer, Long> intPropertyMap) {
        if (intPropertyMap == null) {
            return;
        }
        if (this.intPropertyMap == null) {
            this.intPropertyMap = new HashMap<>();
        }
        for (Integer key : intPropertyMap.keySet()) {
            this.intPropertyMap.put(key, intPropertyMap.get(key));
        }
    }

    public void fillDoublePropertyMap(Map<Integer, Double> doublePropertyMap) {
        if (doublePropertyMap == null) {
            return;
        }
        if (this.doublePropertyMap == null) {
            this.doublePropertyMap = new HashMap<>();
        }
        for (Integer key : doublePropertyMap.keySet()) {
            this.doublePropertyMap.put(key, doublePropertyMap.get(key));
        }

    }

    public void fillHero(List<PsHeroInfo> heroes) {
        if (heroes == null) {
            return;
        }
        if (this.heros == null) {
            heros = new HashMap<>();
        }
        for (PsHeroInfo heroInfo : heroes) {
            this.heros.put(heroInfo.id, heroInfo);
        }
    }

    public void delHero(List<String> ids) {
        ids.forEach(id -> heros.remove(id));
    }

    public void fillPlayerInfo(PsPlayerInfo playerInfo) {
        this.playerInfo = playerInfo;
        fillIntPropertyMap(playerInfo.getIntProperties());
        fillDoublePropertyMap(playerInfo.getDoubleProperties());
    }

    public void fillWorkQueue(List<PsWorkQueue> workQueues) {
        if (queueMap == null) {
            this.queueMap = new HashMap<>();
        }
        workQueues.forEach(queue -> queueMap.put(Short.parseShort(queue.getId()), queue));
    }

    public void updateCityInfo(PsCityInfo newCityInfo) {
        if (this.cityInfo == null) {
            this.cityInfo = newCityInfo;
        } else {
            if (newCityInfo.isSetLands()) {
                List<PsLandInfo> updateLands = newCityInfo.getLands();
                udpateLands(updateLands);
            }
            if (newCityInfo.isSetLevel()) {
                this.cityInfo.setLevel(newCityInfo.level);
            }
            if (newCityInfo.isSetX() && newCityInfo.isSetY()) {
                this.cityInfo.setX(newCityInfo.getX());
                this.cityInfo.setY(newCityInfo.getY());
            }
            if (newCityInfo.isSetAllianceId()) {
                this.cityInfo.setAllianceAliasName(newCityInfo.allianceId);
            }
            if (newCityInfo.isSetAllianceAliasName()) {
                this.cityInfo.setAllianceAliasName(newCityInfo.allianceAliasName);
            }
            if (newCityInfo.isSetUnlocked()) {
                this.cityInfo.setUnlocked(newCityInfo.unlocked);
            }
            if (newCityInfo.isSetCovers()) {
                this.cityInfo.setCovers(newCityInfo.getCovers());
            }
            if (newCityInfo.isSetAllianceId()) {
                this.cityInfo.setAllianceId(newCityInfo.allianceId);
                ;
            }
            if (newCityInfo.isSetAllianceAliasName()) {
                this.cityInfo.setAllianceAliasName(newCityInfo.allianceAliasName);
            }
        }
    }

    private void udpateLands(List<PsLandInfo> updateLands) {
        for (PsLandInfo newInfo : updateLands) {
            boolean isUpdate = false;
            for (int i = 0; i < cityInfo.getLands().size(); i++) {
                PsLandInfo oldInfo = cityInfo.getLands().get(i);
                if (oldInfo.getId() == newInfo.getId()) {
                    cityInfo.getLands().set(i, newInfo);
                    isUpdate = true;
                }
            }
            if (!isUpdate) {
                cityInfo.getLands().add(newInfo);
            }
        }
    }

    public boolean playerDataIsReady() {
        return cityInfo != null && playerInfo != null && heros != null && chapterMissionInfo != null
                && mainMission != null && queueMap != null && soldierMap != null && intPropertyMap != null
                && doublePropertyMap != null;
//        ;
    }

    public void handleMsg(GcMapNodeAppear gcMapNodeAppear) {
        for (PsMapNode node : gcMapNodeAppear.getNodes()) {
            this.mapNodes.put(Point.getInstance(node.getX(), node.getY()), node);
        }
    }

    public void handleMsg(GcPlayerInfo msg) {
        PsPlayerInfo msgBody = msg.getPlayerInfo();
        fillPlayerInfo(msgBody);
        PsPoint psPoint = new PsPoint();
//        psPoint.setX(msgBody.getX());
//        psPoint.setY(msgBody.getY());
        // 一次性压入登录时的协议
        msgSendService.enterMap(session, psPoint);
        msgSendService.getCityWorkQueueList(session);
        msgSendService.getCgAcitivityList(session);
        msgSendService.getCgFavoriteGet(session);
        msgSendService.getCgStoreList(session);
        msgSendService.getRecordList(session, PsRecordType.ALLIANCE_NUM);
        msgSendService.getCgMilestoneList(session);
        msgSendService.getCgWatchtowerList(session);
        msgSendService.getPubTreasureBoxPlusInfo(session);

        // 解锁全部功能
        msgSendService.gmUnlockAllFuncs(session);
        msgSendService.changeName(session, "机器人" + String.valueOf(playerInfo.getRoleId()).substring(9));
        // 增加所有英雄
        msgSendService.gmAddAllHeros(session, 80, 26);
        msgSendService.gmAddAllBuildings(session, 10);

    }

    public void setMsgSendService(MsgSendService msgSendService) {
        this.msgSendService = msgSendService;
    }

    public ClientNetSession getSession() {
        return session;
    }

    public void setSession(ClientNetSession session) {
        this.session = session;
    }

    public void handleMsg(GcCityInfo gcCityInfo) {
        try {
            List<PsCityInfo> msgBody = gcCityInfo.citys;
            var data = msgBody.stream().filter(cityInfo -> cityInfo.getId().equals(String.valueOf(playerInfo.getRoleId()))).findFirst();
            if (data.isPresent()) {
                setCityInfo(data.get());
            } else {
                System.out.println("没有城市数据？？？");
            }
            msgSendService.resetBuildingMaxNum(this);
        } catch (Exception e) {
            AbstractJmeterClient.logger.error("系统异常", e);
        }
    }

    public void handleMsg(GcCityUpdated msg) {
        PsCityInfo msgBody = msg.cityInfo;
        updateCityInfo(msgBody);
        msgSendService.resetBuildingMaxNum(this);
    }

    public void handleMsg(GcSoldierList msg) {
        List<PsCitySoldier> soldierInfos = msg.citySoldiers;
        List<PsSoldierInfo> woundSoldierInfos = msg.woundedSoldiers;
        if (soldierInfos != null) {
            fillSoldierMap(soldierInfos);
        }
        if (woundSoldierInfos != null) {
            fillWoundSoldierMap(woundSoldierInfos);
        }
    }

    public void handleMsg(GcMissionList msg) {
        List<PsMission> missions = msg.sideMissions;
        PsMission mainMission = msg.mainMission;
        fillMissionMap(missions);
        setMainMission(mainMission);
    }

    public void handleMsg(GcMissionReward msg) {
        String missionId = msg.finishMissionId;
        if (getMainMission().getMetaId().equals(missionId)) {
            setMainMission(null);
            msgSendService.getMissionView(session);
        }
        getMissionMap().remove(missionId);
    }

    public void handleMsg(GcMissionUpdate msg) {
        List<PsMission> missions = msg.missions;
        if (missions.size() == 1 && getMainMission() == null) {
            setMainMission(missions.get(0));
        } else {
            for (PsMission mission : missions) {
                if (mission.getMetaId().equals(getMainMission().metaId)) {
                    setMainMission(mission);
                } else {
                    getMissionMap().put(mission.metaId, mission);
                }
            }
        }
    }

    public void handleMsg(GcTechList msg) {
        setTechnologies(msg.getTechnologies());
    }

    public void handleMsg(GcPlayerPropertyUpdated msg) {
        fillIntPropertyMap(msg.getIntProperties());
        fillDoublePropertyMap(msg.getDoubleProperties());
    }

    public void handleMsg(GcHeroList msg) {
        fillHero(msg.heroes);
    }

    public void handleMsg(GcHeroAdd msg) {
        fillHero(msg.adds);
    }

    public void handleMsg(GcHeroGet msg) {
        if (msg.result) {
            fillHero(Collections.singletonList(msg.heroInfo));
        }
    }

    public void handleMsg(GcHeroMod msg) {
        fillHero(msg.mods);
    }

    public void handleMsg(GcHeroDel msg) {
        delHero(msg.ids);
    }

    public void handleMsg(GcChapterMissionInfo msg) {
        setChapterMissionInfo(msg);
    }

    public void handleMsg(GcCityWorkQueueList msg) {
        fillWorkQueue(msg.getQueues());
    }

    public void handleMsg(GcWorkQueueUpdate msg) {
        fillWorkQueue(msg.getQueues());
    }

    public void handleMsg(GcMapSearch msg) {
        //搜索信息返回时
    }

    public void handleMsg(GcBagInfo gcBagInfo) {
        bagMap = new HashMap<>();
        itemMap = new HashMap<>();
        for (var item : gcBagInfo.getItems()) {
            bagMap.put(item.getId(), item);
            itemMap.put(item.getMetaId(), item);
        }
    }

    public void handleMsg(GcItemUpdate itemUpdate) {
        if (bagMap == null) {
            logger.warn("收到更新消息时，背包尚未初始化 玩家:{} 背包:{}", playerInfo.getRoleId(), itemUpdate);
            return;
        }
        var item = bagMap.get(itemUpdate.getId());
        if (item != null) {
            item.setCount(itemUpdate.getCount());
        }
    }

    public void handleMsg(GcUpdateUsableEmoji updateUsableEmoji) {
        updateUsableEmoji.getUsableEmoji().forEach((key, value) -> {
            usableEmoji.put(key, value);
        });
    }

    public void handleMsg(GcSynchronizeTime synchronizeTime) {
        //同步时间
        serverTime = synchronizeTime.getServerTime();
    }

    public void handleMsg(GcCityDefenceInfo cityDefenceInfo) {
        cityInfo.setBurning(cityDefenceInfo.getBurningEndTime() != 0);
    }

    public void handleMsg(GcArenaPlayerInfo gcArenaPlayerInfo) {
        arenaPlayerInfo = gcArenaPlayerInfo.playerInfo;
    }

    public void handleMsg(GcArenaChallengerList gcArenaChallengerList) {
        arenaRivalIds.clear();
        gcArenaChallengerList.getRivals().forEach(rival -> {
            arenaRivalIds.add(rival.getRoleId());
        });
        if (arenaRivalIds.isEmpty()) {
            return;
        }
        Collections.shuffle(arenaRivalIds);
        List<Integer> heroIds = new ArrayList<>();
        List<Integer> allHeroIds = new ArrayList<>();
        for (var hero : heros.values()) {
            allHeroIds.add(Integer.valueOf(hero.getMetaId()));
        }
        Collections.shuffle(allHeroIds);
        for (int i = 0; i < 5; i++) {
            if (i < arenaRivalIds.size()) {
                heroIds.add(allHeroIds.size() > i ? allHeroIds.get(i) : -1);
            }
        }

        msgSendService.arenaSetDefence(session, heroIds);

        // 挑战
        msgSendService.arenaChallenge(session, arenaRivalIds.get(0), heroIds);
    }

    public void handleMsg(GcArenaChallenge gcArenaChallenge) {
        System.out.println(gcArenaChallenge.isWin);
    }


}
