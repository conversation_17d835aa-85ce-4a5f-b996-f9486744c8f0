<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.simfun.sgf</groupId>
		<artifactId>sgf-parent</artifactId>
		<version>1.2.0-SNAPSHOT</version>
		<relativePath>../sgf-parent</relativePath>
	</parent>

	<groupId>com.longtech.icefire</groupId>
	<artifactId>icefire-parent</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>

	<name>IceFire Maven Parent</name>
	<description>IceFire Maven Parent</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<sgf.version>1.2.0-SNAPSHOT</sgf.version>
		<icefire.version>0.0.1-SNAPSHOT</icefire.version>
		<spring.version>6.1.10</spring.version>
		<slf4j.version>2.0.13</slf4j.version>
		<logback.version>1.5.6</logback.version>
		<spring.security.version>6.3.0</spring.security.version>
		<build.plugins.plugin.version>3.9.0</build.plugins.plugin.version>
		<maven.compiler.target>21</maven.compiler.target>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.release>21</maven.compiler.release>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.simfun.sgf</groupId>
				<artifactId>sgf-core</artifactId>
				<version>${sgf.version}</version>
			</dependency>
			<dependency>
				<groupId>com.simfun.sgf</groupId>
				<artifactId>sgf-common</artifactId>
				<version>${sgf.version}</version>
			</dependency>
			<dependency>
				<groupId>com.simfun.sgf</groupId>
				<artifactId>sgf-net</artifactId>
				<version>${sgf.version}</version>
			</dependency>
			<dependency>
				<groupId>com.simfun.sgf</groupId>
				<artifactId>sgf-monitoring</artifactId>
				<version>1.0.0</version>
			</dependency>
			<dependency>
				<groupId>com.longtech.icefire</groupId>
				<artifactId>icefire-core</artifactId>
				<version>${icefire.version}</version>
			</dependency>
			<dependency>
				<groupId>com.longtech.icefire</groupId>
				<artifactId>icefire-protocol</artifactId>
				<version>${icefire.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-nop</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>jcl-over-slf4j</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/ch.qos.logback/logback-classic -->
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-classic</artifactId>
				<version>${logback.version}</version>
			</dependency>
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-core</artifactId>
				<version>${logback.version}</version>
			</dependency>
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-access</artifactId>
				<version>1.4.14</version>
			</dependency>
			<dependency>
				<groupId>org.simpleframework</groupId>
				<artifactId>simple-xml</artifactId>
				<version>2.7.1</version>
			</dependency>
			<dependency>
				<groupId>org.reflections</groupId>
				<artifactId>reflections</artifactId>
				<version>0.10.2</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-core</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jdbc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-aop</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-web</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-webmvc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-orm</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-config</artifactId>
				<version>${spring.security.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-core</artifactId>
				<version>${spring.security.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-web</artifactId>
				<version>${spring.security.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-taglibs</artifactId>
				<version>${spring.security.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis</artifactId>
				<version>3.5.8</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis-spring</artifactId>
				<version>3.0.3</version>
			</dependency>
			<dependency>
				<groupId>org.apache.thrift</groupId>
				<artifactId>libthrift</artifactId>
				<version>0.20.0</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>1.2.83</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>3.14.0</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.16.1</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-dbcp2</artifactId>
				<version>2.12.0</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-math3</artifactId>
				<version>3.6.1</version>
			</dependency>
			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjrt</artifactId>
				<version>1.9.22</version>
			</dependency>
			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjweaver</artifactId>
				<version>1.9.22</version>
				<scope>compile</scope>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>5.2.0</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.ws</groupId>
				<artifactId>spring-ws-core</artifactId>
				<version>4.0.10</version>
			</dependency>
			<dependency>
				<groupId>com.googlecode.concurrentlinkedhashmap</groupId>
				<artifactId>concurrentlinkedhashmap-lru</artifactId>
				<version>1.4.2</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpcore</artifactId>
				<version>4.4.16</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>4.5.14</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpcore-nio</artifactId>
				<version>4.4.16</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient-cache</artifactId>
				<version>4.5.14</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpasyncclient</artifactId>
				<version>4.1.5</version>
			</dependency>
			<dependency>
				<groupId>org.quartz-scheduler</groupId>
				<artifactId>quartz</artifactId>
				<version>2.3.2</version>
			</dependency>
			<dependency>
				<groupId>org.quartz-scheduler</groupId>
				<artifactId>quartz-jobs</artifactId>
				<version>2.3.2</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/jakarta.persistence/jakarta.persistence-api -->
			<dependency>
				<groupId>jakarta.persistence</groupId>
				<artifactId>jakarta.persistence-api</artifactId>
				<version>3.1.0</version>
			</dependency>
			<dependency>
				<groupId>org.dom4j</groupId>
				<artifactId>dom4j</artifactId>
				<version>2.1.4</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun.oss</groupId>
				<artifactId>aliyun-sdk-oss</artifactId>
				<version>2.0.6</version>
			</dependency>
			<dependency>
				<groupId>org.perf4j</groupId>
				<artifactId>perf4j</artifactId>
				<version>0.9.16</version>
			</dependency>
<!-- 			<dependency>
				<groupId>net.jpountz.lz4</groupId>
				<artifactId>lz4</artifactId>
				<version>1.3.0</version>
			</dependency>
 -->
	 		<dependency>
			  <groupId>org.lz4</groupId>
			  <artifactId>lz4-pure-java</artifactId>
			  <version>1.8.0</version>
			</dependency>		
 			<dependency>
				<groupId>com.amazonaws</groupId>
				<artifactId>aws-java-sdk-sns</artifactId>
				<version>1.11.342</version>
			</dependency>
			<dependency>
				<groupId>com.facebook.nifty</groupId>
				<artifactId>nifty-core</artifactId>
				<version>0.23.0</version>
			</dependency>
	        <dependency>
	            <groupId>org.mongodb</groupId>
	            <artifactId>mongodb-driver-legacy</artifactId>
	            <version>4.4.2</version>
	        </dependency>


			<!-- test -->
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>4.12</version>
			</dependency>
			<dependency>
				<groupId>org.easymock</groupId>
				<artifactId>easymock</artifactId>
				<version>3.4</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>33.2.1-jre</version>
			</dependency>
<!-- 			<dependency>
				<groupId>com.alibaba.nacos</groupId>
				<artifactId>nacos-client</artifactId>
				<version>1.2.1</version>
			</dependency>
 --><!-- 			<dependency>
				<groupId>com.ecwid.consul</groupId>
				<artifactId>consul-api</artifactId>
				<version>1.4.2</version>
			</dependency>
 -->
		</dependencies>
	</dependencyManagement>
</project>