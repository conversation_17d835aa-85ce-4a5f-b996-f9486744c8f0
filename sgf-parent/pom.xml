<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.simfun.sgf</groupId>
	<artifactId>sgf-parent</artifactId>
	<version>1.2.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<name>Simple Game Framework Parent</name>
	<description>Simple Game Framework Parent</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<jackson2.version>2.17.1</jackson2.version>
		<sgf.version>1.2.0-SNAPSHOT</sgf.version>
		<spring.version>6.1.6</spring.version>
		<slf4j.version>2.0.13</slf4j.version>
		<build.plugins.plugin.version>3.9.0</build.plugins.plugin.version>
		<maven.compiler.target>21</maven.compiler.target>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.release>21</maven.compiler.release>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.simfun.sgf</groupId>
				<artifactId>sgf-core</artifactId>
				<version>${sgf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>jcl-over-slf4j</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-log4j12</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-nop</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.simpleframework</groupId>
				<artifactId>simple-xml</artifactId>
				<version>2.7.1</version>
			</dependency>
			<dependency>
				<groupId>org.reflections</groupId>
				<artifactId>reflections</artifactId>
				<version>0.10.2</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-aop</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-web</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.thrift</groupId>
				<artifactId>libthrift</artifactId>
				<version>0.20.0</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>1.2.83</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis</artifactId>
				<version>3.5.8</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-all</artifactId>
				<version>4.1.72.Final</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-core</artifactId>
				<version>${jackson2.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-databind</artifactId>
				<version>${jackson2.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-annotations</artifactId>
				<version>${jackson2.version}</version>
			</dependency>
			<dependency>
			  <groupId>org.lz4</groupId>
			  <artifactId>lz4-pure-java</artifactId>
			  <version>1.8.0</version>
			</dependency>		
			<!-- test -->
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>4.13.2</version>
			</dependency>
			<dependency>
				<groupId>org.easymock</groupId>
				<artifactId>easymock</artifactId>
				<version>3.4</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>1.18.30</version>
				<scope>provided</scope>
			</dependency>
			<!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
			<dependency>
				<groupId>commons-codec</groupId>
				<artifactId>commons-codec</artifactId>
				<version>1.17.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/javax.annotation/javax.annotation-api -->
			<dependency>
				<groupId>javax.annotation</groupId>
				<artifactId>javax.annotation-api</artifactId>
				<version>1.3.2</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
 </project>