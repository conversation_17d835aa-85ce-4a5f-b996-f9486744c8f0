[{"id": "1", "isTest": "0", "configEditor": "", "season": "0", "name": "castle<PERSON>uiface", "value": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>1,<PERSON><PERSON><PERSON><PERSON><PERSON>0,<PERSON><PERSON><PERSON><PERSON>_0,<PERSON><PERSON><PERSON><PERSON>_1,<PERSON><PERSON><PERSON><PERSON>_1,<PERSON><PERSON><PERSON><PERSON>_2,<PERSON><PERSON><PERSON><PERSON>_2,<PERSON><PERSON><PERSON><PERSON>_2,<PERSON><PERSON><PERSON><PERSON>_3,<PERSON><PERSON><PERSON><PERSON>_3,<PERSON><PERSON><PERSON><PERSON>_3,<PERSON><PERSON><PERSON><PERSON>_3,<PERSON><PERSON><PERSON><PERSON>_4,<PERSON><PERSON><PERSON><PERSON>_4,<PERSON><PERSON><PERSON><PERSON>_4,<PERSON><PERSON><PERSON><PERSON>_4,<PERSON><PERSON><PERSON><PERSON>_5,<PERSON><PERSON><PERSON><PERSON>_5,<PERSON><PERSON><PERSON><PERSON>_5,<PERSON><PERSON><PERSON><PERSON>_5,<PERSON><PERSON><PERSON><PERSON>_6,<PERSON><PERSON><PERSON><PERSON>_6,<PERSON><PERSON><PERSON><PERSON>_6,<PERSON><PERSON><PERSON><PERSON>_6"}, {"id": "2", "isTest": "0", "configEditor": "", "season": "0", "name": "playerHeadInitial", "value": "01"}, {"id": "3", "isTest": "0", "configEditor": "", "season": "0", "name": "initResource", "value": "6|3500,7|25,8|10,9|400"}, {"id": "4", "isTest": "0", "configEditor": "", "season": "0", "name": "initialProperty", "value": "10044|20,10000|0,10001|0,10002|0,10003|0,10000|0,11004|1000,10041|1,10042|1,10261|1000,10999|60,11001|0,10021|10000,10022|10000,10023|5000,10024|2500"}, {"id": "5", "isTest": "0", "configEditor": "", "season": "0", "name": "initialEnergy", "value": "200"}, {"id": "6", "isTest": "0", "configEditor": "", "season": "0", "name": "initialHero", "value": "3501|1|1|0|0|0|0|0|0"}, {"id": "7", "isTest": "0", "configEditor": "", "season": "0", "name": "initItem", "value": "10201001|80,10202001|80,10101002|50,10102002|50,10103002|50,10104002|50,19902001|1"}, {"id": "8", "isTest": "0", "configEditor": "", "season": "0", "name": "initSoldier", "value": "11001|100"}, {"id": "9", "isTest": "0", "configEditor": "", "season": "0", "name": "initialCastleLv", "value": "3001|1,1002|1"}, {"id": "10", "isTest": "0", "configEditor": "", "season": "0", "name": "staminaGenerateSpeed", "value": "300"}, {"id": "11", "isTest": "0", "configEditor": "", "season": "0", "name": "reconMoveSpeedBase", "value": "0.1"}, {"id": "12", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreMoveSpeedBase", "value": "6"}, {"id": "13", "isTest": "0", "configEditor": "", "season": "0", "name": "groundMaxRound", "value": "8"}, {"id": "14", "isTest": "0", "configEditor": "", "season": "0", "name": "pveEnergyCost", "value": "10"}, {"id": "15", "isTest": "0", "configEditor": "", "season": "0", "name": "rallyPveEnergyCost", "value": "20"}, {"id": "16", "isTest": "0", "configEditor": "", "season": "0", "name": "joinPveEnergyCost", "value": "10"}, {"id": "17", "isTest": "0", "configEditor": "", "season": "0", "name": "stationEnergyCost", "value": "10"}, {"id": "18", "isTest": "0", "configEditor": "", "season": "0", "name": "robEnergyCost", "value": "10"}, {"id": "19", "isTest": "0", "configEditor": "", "season": "0", "name": "rallyPvpEnergyCost", "value": "5"}, {"id": "20", "isTest": "0", "configEditor": "", "season": "0", "name": "joinPvpEnergyCost", "value": "5"}, {"id": "21", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEnergyCost", "value": "0"}, {"id": "22", "isTest": "0", "configEditor": "", "season": "0", "name": "reconEnergyCost", "value": "0"}, {"id": "23", "isTest": "0", "configEditor": "", "season": "0", "name": "pveReviveRate", "value": "-0.71"}, {"id": "24", "isTest": "0", "configEditor": "", "season": "0", "name": "soldierPveReviveRate", "value": "0"}, {"id": "25", "isTest": "0", "configEditor": "", "season": "0", "name": "favoriteNum", "value": "100"}, {"id": "26", "isTest": "0", "configEditor": "", "season": "0", "name": "protectTimeLimit", "value": "259200"}, {"id": "27", "isTest": "0", "configEditor": "", "season": "0", "name": "buildingFreeTime", "value": "0"}, {"id": "28", "isTest": "0", "configEditor": "", "season": "0", "name": "newRoleProtectionTime", "value": "259200"}, {"id": "29", "isTest": "0", "configEditor": "", "season": "0", "name": "protectLevelLimit", "value": "10"}, {"id": "30", "isTest": "0", "configEditor": "", "season": "0", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "200"}, {"id": "31", "isTest": "0", "configEditor": "", "season": "0", "name": "seniorMoveCityCost", "value": "4800"}, {"id": "32", "isTest": "0", "configEditor": "", "season": "0", "name": "buildTimeSpd", "value": "0.041666667"}, {"id": "33", "isTest": "0", "configEditor": "", "season": "0", "name": "trainTimeSpd", "value": "0.041666667"}, {"id": "34", "isTest": "0", "configEditor": "", "season": "0", "name": "cureTimeSpd", "value": "0.041666667"}, {"id": "35", "isTest": "0", "configEditor": "", "season": "0", "name": "cureExploreTimeSpd", "value": "24,3600,0.1,1"}, {"id": "36", "isTest": "0", "configEditor": "", "season": "0", "name": "cureShiWuPsd", "value": "6,10000,1,1"}, {"id": "37", "isTest": "0", "configEditor": "", "season": "0", "name": "cureGangPsd", "value": "6,10000,1,1"}, {"id": "38", "isTest": "0", "configEditor": "", "season": "0", "name": "cureShiYouPsd", "value": "12,10000,1,1"}, {"id": "39", "isTest": "0", "configEditor": "", "season": "0", "name": "cureXituPsd", "value": "24,10000,1,1"}, {"id": "40", "isTest": "0", "configEditor": "", "season": "0", "name": "cureTieKuangPsd", "value": "2,340,3400,0.1"}, {"id": "41", "isTest": "0", "configEditor": "", "season": "0", "name": "cureRanLiaoPsd", "value": "1,340,3400,0.1"}, {"id": "42", "isTest": "0", "configEditor": "", "season": "0", "name": "cureDianLiPsd", "value": "1,340,3400,0.1"}, {"id": "43", "isTest": "0", "configEditor": "", "season": "0", "name": "peaceArea", "value": "10,9,8,7"}, {"id": "44", "isTest": "0", "configEditor": "", "season": "0", "name": "scoutCost", "value": "100"}, {"id": "45", "isTest": "0", "configEditor": "", "season": "0", "name": "timeBase", "value": "60"}, {"id": "46", "isTest": "0", "configEditor": "", "season": "0", "name": "searchRange", "value": "50"}, {"id": "47", "isTest": "0", "configEditor": "", "season": "0", "name": "searchNum", "value": "5"}, {"id": "48", "isTest": "0", "configEditor": "", "season": "0", "name": "cdTimeRe", "value": "2880"}, {"id": "49", "isTest": "0", "configEditor": "", "season": "0", "name": "cdTime", "value": "60"}, {"id": "50", "isTest": "0", "configEditor": "", "season": "0", "name": "reportHeadNum", "value": "10"}, {"id": "51", "isTest": "0", "configEditor": "", "season": "0", "name": "reportMsgNum", "value": "30"}, {"id": "52", "isTest": "0", "configEditor": "", "season": "0", "name": "alliancePanelLimit", "value": "25,60"}, {"id": "53", "isTest": "0", "configEditor": "", "season": "0", "name": "heroResonanceLimit", "value": "5"}, {"id": "54", "isTest": "0", "configEditor": "", "season": "0", "name": "healTimeLimit", "value": "3"}, {"id": "55", "isTest": "0", "configEditor": "", "season": "0", "name": "alliDomainGatherRatio", "value": "1,0.5,0.335"}, {"id": "56", "isTest": "0", "configEditor": "", "season": "0", "name": "gatherContriRatio", "value": "1"}, {"id": "57", "isTest": "0", "configEditor": "", "season": "0", "name": "mercenaryCost", "value": "5000100002000050000000000000000000000"}, {"id": "58", "isTest": "0", "configEditor": "", "season": "0", "name": "scoutKingdomCost", "value": "500"}, {"id": "59", "isTest": "0", "configEditor": "", "season": "0", "name": "activityKingdomWarLevel", "value": "9"}, {"id": "60", "isTest": "0", "configEditor": "", "season": "0", "name": "allianceChestActivityCompose", "value": "10"}, {"id": "61", "isTest": "0", "configEditor": "", "season": "0", "name": "activityBlackKnightReviveRate", "value": "1"}, {"id": "62", "isTest": "0", "configEditor": "", "season": "0", "name": "activityBlackKnightTarget", "value": "0.1"}, {"id": "63", "isTest": "0", "configEditor": "", "season": "0", "name": "activityBlackKnightLevel", "value": "9"}, {"id": "64", "isTest": "0", "configEditor": "", "season": "0", "name": "castleMenuInfoCityLevel", "value": "3"}, {"id": "65", "isTest": "0", "configEditor": "", "season": "0", "name": "resFreshTime", "value": "30"}, {"id": "66", "isTest": "0", "configEditor": "", "season": "0", "name": "wallFireDamage", "value": "1"}, {"id": "67", "isTest": "0", "configEditor": "", "season": "0", "name": "wallRepairIntervalNum", "value": "500,30"}, {"id": "68", "isTest": "0", "configEditor": "", "season": "0", "name": "first<PERSON>echarge", "value": "SC_6;2201|1,10401003|3,19908002|10,10108002|5;10403206|10,10401003|2,1|250,10401002|30"}, {"id": "69", "isTest": "0", "configEditor": "", "season": "0", "name": "directMoveCityItem", "value": "10304001"}, {"id": "70", "isTest": "0", "configEditor": "", "season": "0", "name": "randomMoveCityItem", "value": "10304002"}, {"id": "71", "isTest": "0", "configEditor": "", "season": "0", "name": "talentResetItemId", "value": "10306001"}, {"id": "72", "isTest": "0", "configEditor": "", "season": "0", "name": "commanderCenterUpgradeMail", "value": "26|805025"}, {"id": "73", "isTest": "0", "configEditor": "", "season": "0", "name": "npcLiveTime", "value": "24"}, {"id": "74", "isTest": "0", "configEditor": "", "season": "0", "name": "initHero", "value": "3102"}, {"id": "75", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "vipLoginExp", "value": "210|10|500"}, {"id": "76", "isTest": "0", "configEditor": "", "season": "0", "name": "scoutresource", "value": "1000"}, {"id": "77", "isTest": "0", "configEditor": "", "season": "0", "name": "allianceMemberOffline", "value": "7"}, {"id": "78", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "defaultfashion", "value": "101,1000,2000,3000"}, {"id": "79", "isTest": "0", "configEditor": "", "season": "0", "name": "fashionSwitch", "value": "1"}, {"id": "80", "isTest": "0", "configEditor": "", "season": "0", "name": "soldierPowerParameter", "value": "1"}, {"id": "81", "isTest": "0", "configEditor": "", "season": "0", "name": "videoAdLimit", "value": "5"}, {"id": "82", "isTest": "0", "configEditor": "", "season": "0", "name": "brushplayer", "value": "5|4|6|3|2"}, {"id": "83", "isTest": "0", "configEditor": "", "season": "0", "name": "initAtkNpcLevel", "value": "2"}, {"id": "84", "isTest": "0", "configEditor": "", "season": "0", "name": "openCodUidExport", "value": "true"}, {"id": "85", "isTest": "0", "configEditor": "", "season": "0", "name": "herobroadcast", "value": ""}, {"id": "86", "isTest": "0", "configEditor": "", "season": "0", "name": "numberofevents", "value": "5|0,10|0,15|0,20|0,25|0"}, {"id": "87", "isTest": "0", "configEditor": "", "season": "0", "name": "refreshinterval", "value": "5|10"}, {"id": "88", "isTest": "0", "configEditor": "", "season": "0", "name": "initialHeroWorkNum", "value": "999"}, {"id": "89", "isTest": "0", "configEditor": "", "season": "0", "name": "foodInitialUpperLimit", "value": "30000"}, {"id": "90", "isTest": "0", "configEditor": "", "season": "0", "name": "waterInitialUpperLimit", "value": "30000"}, {"id": "91", "isTest": "0", "configEditor": "", "season": "0", "name": "woodInitialUpperLimit", "value": "30000"}, {"id": "92", "isTest": "0", "configEditor": "", "season": "0", "name": "ironInitialUpperLimit", "value": "30000"}, {"id": "93", "isTest": "0", "configEditor": "", "season": "0", "name": "baseTrainCount", "value": "0"}, {"id": "94", "isTest": "0", "configEditor": "", "season": "0", "name": "eventProbability", "value": "3001|1000"}, {"id": "95", "isTest": "0", "configEditor": "", "season": "0", "name": "sciencePromote", "value": "20"}, {"id": "96", "isTest": "0", "configEditor": "", "season": "0", "name": "soldierTyrannize", "value": "6"}, {"id": "97", "isTest": "0", "configEditor": "", "season": "0", "name": "buffDamageMin", "value": "0"}, {"id": "98", "isTest": "0", "configEditor": "", "season": "0", "name": "buffDamageMax", "value": "3"}, {"id": "99", "isTest": "0", "configEditor": "", "season": "0", "name": "moveSpeedparam", "value": "0.335"}, {"id": "100", "isTest": "0", "configEditor": "", "season": "0", "name": "foodRapidProduction", "value": "0.33"}, {"id": "101", "isTest": "0", "configEditor": "", "season": "0", "name": "waterRapidProduction", "value": "0.33"}, {"id": "102", "isTest": "0", "configEditor": "", "season": "0", "name": "woodRapidProduction", "value": "0.33"}, {"id": "103", "isTest": "0", "configEditor": "", "season": "0", "name": "ironRapidProduction", "value": "0.33"}, {"id": "104", "isTest": "0", "configEditor": "", "season": "0", "name": "goldRapidProduction", "value": "0.1"}, {"id": "105", "isTest": "0", "configEditor": "", "season": "0", "name": "rapidProductionInitialCriticalRate", "value": "0.1"}, {"id": "106", "isTest": "0", "configEditor": "", "season": "0", "name": "rapidProductionInitialCriticalPercent", "value": "0.5"}, {"id": "107", "isTest": "0", "configEditor": "", "season": "0", "name": "initialBaseDurabilityLimit", "value": "1000"}, {"id": "108", "isTest": "0", "configEditor": "", "season": "0", "name": "initialBaseDurability", "value": "1000"}, {"id": "109", "isTest": "0", "configEditor": "", "season": "0", "name": "durabilityRecoverySpeed", "value": "4"}, {"id": "110", "isTest": "0", "configEditor": "", "season": "0", "name": "diamondDurabilityRecoveryNum", "value": "600"}, {"id": "111", "isTest": "0", "configEditor": "", "season": "0", "name": "diamondDurabilityRecoveryCost", "value": "100|200|200|300|300|300|400|400|400|400|500"}, {"id": "112", "isTest": "0", "configEditor": "", "season": "0", "name": "smokeHurtPercent", "value": "0.33"}, {"id": "113", "isTest": "0", "configEditor": "", "season": "0", "name": "smokeTime", "value": "900"}, {"id": "114", "isTest": "0", "configEditor": "", "season": "0", "name": "burnTime", "value": "300"}, {"id": "115", "isTest": "0", "configEditor": "", "season": "0", "name": "burnDurabilityDecreaseSpeed", "value": "5"}, {"id": "116", "isTest": "0", "configEditor": "xky", "season": "0", "name": "rebuildInitialDurabilityNum", "value": "500"}, {"id": "117", "isTest": "0", "configEditor": "", "season": "0", "name": "ruinTime", "value": "900"}, {"id": "118", "isTest": "0", "configEditor": "", "season": "0", "name": "lossCompanstaeCd", "value": "30000"}, {"id": "119", "isTest": "0", "configEditor": "", "season": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"id": "120", "isTest": "0", "configEditor": "", "season": "0", "name": "playerLevelPolicyChange", "value": "8|10|13|16"}, {"id": "121", "isTest": "0", "configEditor": "", "season": "0", "name": "PolicyChangeItemId", "value": "********"}, {"id": "122", "isTest": "0", "configEditor": "", "season": "0", "name": "mailNewMapLocation", "value": "1000588"}, {"id": "123", "isTest": "0", "configEditor": "", "season": "0", "name": "firstDaySupportHeroSetting", "value": "43200|3401"}, {"id": "124", "isTest": "0", "configEditor": "", "season": "0", "name": "initWounded", "value": "11001|0"}, {"id": "125", "isTest": "0", "configEditor": "", "season": "0", "name": "firstBindAccountRewardMailID", "value": "1000591"}, {"id": "126", "isTest": "0", "configEditor": "", "season": "0", "name": "forceGuideVersion", "value": "2.6.0"}, {"id": "127", "isTest": "0", "configEditor": "", "season": "0", "name": "worldPlayerCount1", "value": "10"}, {"id": "128", "isTest": "0", "configEditor": "", "season": "0", "name": "worldPlayerCount2", "value": "1"}, {"id": "129", "isTest": "0", "configEditor": "", "season": "0", "name": "battlePassBuyExp", "value": "5"}, {"id": "130", "isTest": "0", "configEditor": "", "season": "0", "name": "commonBossFreshTime", "value": "1"}, {"id": "131", "isTest": "0", "configEditor": "", "season": "0", "name": "mandatoryRecall", "value": "0"}, {"id": "132", "isTest": "0", "configEditor": "", "season": "0", "name": "forceGuideJumpGroup", "value": "670|680|690"}, {"id": "137", "isTest": "0", "configEditor": "", "season": "0", "name": "activityZhuanPanTime", "value": "259200"}, {"id": "138", "isTest": "0", "configEditor": "", "season": "0", "name": "nameplateDefault", "value": "20001"}, {"id": "139", "isTest": "0", "configEditor": "", "season": "0", "name": "callHeroTimeSpd", "value": "24,3600,0.1,1"}, {"id": "140", "isTest": "0", "configEditor": "", "season": "0", "name": "defaultChatBubble", "value": "3000"}, {"id": "142", "isTest": "0", "configEditor": "", "season": "0", "name": "battlePassBuyLevel", "value": "999"}, {"id": "143", "isTest": "0", "configEditor": "", "season": "0", "name": "defaultHeadPortraitFrame", "value": "2000"}, {"id": "144", "isTest": "0", "configEditor": "", "season": "0", "name": "allianceGiftPushLimit", "value": "5"}, {"id": "145", "isTest": "0", "configEditor": "", "season": "0", "name": "allianceGiftPushTime", "value": "7200"}, {"id": "146", "isTest": "0", "configEditor": "", "season": "0", "name": "continuousRechargeNewDuration", "value": "1209600"}, {"id": "147", "isTest": "0", "configEditor": "", "season": "0", "name": "pumpingCardScrollMessage", "value": "5"}, {"id": "148", "isTest": "0", "configEditor": "", "season": "0", "name": "heroRecruitmentConditionCount", "value": "1:22;1"}, {"id": "149", "isTest": "0", "configEditor": "", "season": "0", "name": "scrollingInformationSpeed", "value": "100"}, {"id": "150", "isTest": "0", "configEditor": "", "season": "0", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3608"}, {"id": "151", "isTest": "0", "configEditor": "", "season": "0", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3611"}, {"id": "152", "isTest": "0", "configEditor": "", "season": "0", "name": "allianceMoveCityItem", "value": "10304003"}, {"id": "153", "isTest": "0", "configEditor": "", "season": "0", "name": "cureCombatExpPsd", "value": "100"}, {"id": "154", "isTest": "0", "configEditor": "", "season": "0", "name": "cureDevelopmentExpPsd", "value": "100"}, {"id": "155", "isTest": "0", "configEditor": "", "season": "0", "name": "shieldedUsageInterval", "value": "5"}, {"id": "156", "isTest": "0", "configEditor": "", "season": "0", "name": "mailBattlePassRewardMail01", "value": "1000710"}, {"id": "157", "isTest": "0", "configEditor": "", "season": "0", "name": "immediatlyBuildTime", "value": "1296000"}, {"id": "158", "isTest": "0", "configEditor": "", "season": "0", "name": "allianceFogUnlockTime", "value": "4320"}, {"id": "159", "isTest": "0", "configEditor": "", "season": "0", "name": "fogUnlockSever", "value": "1,2,3,4,5,6,7,8,9,10,11"}, {"id": "160", "isTest": "0", "configEditor": "", "season": "0", "name": "lampDisplayConditions", "value": "4"}, {"id": "161", "isTest": "0", "configEditor": "", "season": "0", "name": "castleFixStartTime", "value": "19"}, {"id": "162", "isTest": "0", "configEditor": "", "season": "0", "name": "shieldLogSaveTime", "value": "7776000"}, {"id": "163", "isTest": "0", "configEditor": "", "season": "0", "name": "newPlayerImmigrants", "value": "10304006"}, {"id": "164", "isTest": "0", "configEditor": "", "season": "0", "name": "normalImmigration", "value": "10304007"}, {"id": "165", "isTest": "0", "configEditor": "", "season": "0", "name": "battlePassDailyMaxExp", "value": "670"}, {"id": "166", "isTest": "0", "configEditor": "", "season": "0", "name": "forceUpdateVersion", "value": "com.ninestudio.starlight.sg:0.9.0:0.9.0"}, {"id": "167", "isTest": "0", "configEditor": "", "season": "0", "name": "castleOpenRemindMail", "value": "1|720|1000721,2|720|1000722,3|720|1000723,4|720|1000724,5|720|1000725,6|720|1100006,7|720|1100007,8|720|1100008,9|720|1100009,10|720|1100010,11|720|1100011,12|720|1100012,13|720|1100013,14|720|1100014,15|720|1100015,16|720|1100016,17|720|1100017,18|720|1100018,19|720|1100019,20|720|1100020"}, {"id": "168", "isTest": "0", "configEditor": "", "season": "0", "name": "preciousItemRecord", "value": "201|202|203|204|205|101|102|104|105|106|109|108|301|400|401|306|302|307|601|107|304|9908|9902|407|403|408|9917|9915|9916|9906|9911|801|9930|402|9912|9919|9922|9927|9926|9936|409|9913|9940"}, {"id": "169", "isTest": "0", "configEditor": "", "season": "0", "name": "warFeverBuffLevel", "value": "1|999998,2|999998,3|999998,4|999998,5|999998,6|999998,7|999998,8|999998,9|999998,10|999998,11|999998,12|999998,13|999998,14|999998,15|999998,16|999998,17|999998,18|999998,19|999998,20|999998,21|999998,22|999998,23|999998,24|999998,25|999998,26|999998,27|999998,28|999998,29|999998,30|999998,31|999998,32|999998,33|999998,34|999998,35|999998,36|999998,37|999998,38|999998,39|999998,40|999998,41|999998,42|999998"}, {"id": "170", "isTest": "0", "configEditor": "", "season": "0", "name": "deadRisingWave", "value": "20"}, {"id": "171", "isTest": "0", "configEditor": "", "season": "0", "name": "deadRisingTime", "value": "130"}, {"id": "172", "isTest": "0", "configEditor": "", "season": "0", "name": "deadRisingFail", "value": "2"}, {"id": "173", "isTest": "0", "configEditor": "", "season": "0", "name": "deadRisingHardest", "value": "5"}, {"id": "174", "isTest": "0", "configEditor": "", "season": "0", "name": "customAvatarAutoAuditLevel", "value": "adult|2,medical|5,racy|3,spoof|5,violence|5"}, {"id": "175", "isTest": "0", "configEditor": "", "season": "0", "name": "heroEquipSwitch", "value": "13"}, {"id": "176", "isTest": "0", "configEditor": "", "season": "0", "name": "deadRisingWalkSpeed", "value": "14"}, {"id": "177", "isTest": "0", "configEditor": "", "season": "0", "name": "rallyFormationNumProperty", "value": "80001|0.05,80002|0.05,80003|0.05"}, {"id": "178", "isTest": "0", "configEditor": "", "season": "0", "name": "reinforcementFormationNumProperty", "value": "81001|0.05,81002|0.05,81003|0.05"}, {"id": "179", "isTest": "0", "configEditor": "", "season": "0", "name": "castleNPCscore", "value": "1|200|30,2|200|30,3|200|30,4|200|30,5|200|30,6|200|30,7|200|30,8|200|30,9|200|30,10|200|30,11|200|30,12|200|30,13|200|30,14|200|30,15|200|30,16|200|30,17|200|30,18|200|30,19|200|30,20|200|30"}, {"id": "180", "isTest": "0", "configEditor": "", "season": "0", "name": "activityHellGroup", "value": "6|14400"}, {"id": "181", "isTest": "0", "configEditor": "", "season": "0", "name": "customAvatarRejectMail", "value": "1000769"}, {"id": "182", "isTest": "0", "configEditor": "", "season": "0", "name": "mailZakayevNpcLevel", "value": "3|7|0.33|1000576,8|10|0.33|1000770,11|13|0.33|1000771,14|16|0.33|1000783,17|19|0.33|1000772,20|22|0.33|1000781,23|25|0.33|1000782,26|28|0.33|1000784"}, {"id": "183", "isTest": "0", "configEditor": "", "season": "0", "name": "dailyMaximumAmountOfResourceAssistance", "value": "25000000"}, {"id": "184", "isTest": "0", "configEditor": "", "season": "0", "name": "allianceAppDeleteTime", "value": "259200"}, {"id": "189", "isTest": "0", "configEditor": "", "season": "0", "name": "castleLevelFogDissipateDate", "value": "9999|9999|9999|9999|9999"}, {"id": "190", "isTest": "0", "configEditor": "", "season": "0", "name": "castleFogDissipateArea", "value": "1"}, {"id": "192", "isTest": "0", "configEditor": "", "season": "0", "name": "activityCumulativeRechargeMail", "value": "1002100"}, {"id": "193", "isTest": "0", "configEditor": "", "season": "0", "name": "clanRecommendLevel", "value": "1|2|2"}, {"id": "194", "isTest": "0", "configEditor": "", "season": "0", "name": "titleLevelLimit", "value": "8"}, {"id": "195", "isTest": "0", "configEditor": "", "season": "0", "name": "communityGotoReward", "value": "100|200,1002200|1002201"}, {"id": "196", "isTest": "0", "configEditor": "", "season": "0", "name": "survivorCampNPCscore", "value": "1|100|20,2|100|20,3|100|20,4|100|20,5|100|20,6|100|20,7|100|20,8|100|20,9|100|20,10|100|20,11|100|20,12|100|20,13|100|20,14|100|20,15|100|20,16|100|20,17|100|20,18|100|20,19|100|20,20|100|20"}, {"id": "197", "isTest": "0", "configEditor": "", "season": "0", "name": "kingAnnouncementCD", "value": "43200"}, {"id": "198", "isTest": "0", "configEditor": "", "season": "0", "name": "cdkeyVerificationMail", "value": "1006000"}, {"id": "199", "isTest": "0", "configEditor": "", "season": "0", "name": "cdkeyExchangeMail", "value": "1006001"}, {"id": "200", "isTest": "0", "configEditor": "", "season": "0", "name": "communityGotoRewardLevel", "value": "6"}, {"id": "201", "isTest": "0", "configEditor": "", "season": "0", "name": "kingAppointmentTiming", "value": "21600"}, {"id": "202", "isTest": "0", "configEditor": "", "season": "0", "name": "propertyParam40122", "value": "40101|40102|40103"}, {"id": "203", "isTest": "0", "configEditor": "", "season": "0", "name": "heroRestProperty", "value": "1|2021-06-16 00:00:00|2021-06-23 00:00:00"}, {"id": "204", "isTest": "0", "configEditor": "", "season": "0", "name": "kingTransferMail", "value": "1010004"}, {"id": "205", "isTest": "0", "configEditor": "", "season": "0", "name": "kingTransferNobodyResignMail", "value": "1010005"}, {"id": "206", "isTest": "0", "configEditor": "", "season": "0", "name": "kingDisbandClanResignMail", "value": "1010006"}, {"id": "207", "isTest": "0", "configEditor": "", "season": "0", "name": "kingOvertimeTransfer", "value": "259200"}, {"id": "208", "isTest": "0", "configEditor": "", "season": "1", "name": "clanBuildingReinforcementMaxTeam", "value": "10"}, {"id": "209", "isTest": "0", "configEditor": "", "season": "0", "name": "clanBuildingReinforcementBattleTeam", "value": "5"}, {"id": "210", "isTest": "0", "configEditor": "", "season": "1", "name": "castleReinforcementMaxTeam", "value": "10"}, {"id": "211", "isTest": "0", "configEditor": "", "season": "0", "name": "castleReinforcementBattleTeam", "value": "5"}, {"id": "212", "isTest": "0", "configEditor": "", "season": "0", "name": "gvgBuildingReinforcementMaxTeam", "value": "10"}, {"id": "213", "isTest": "0", "configEditor": "", "season": "0", "name": "gvgBuildingReinforcementBattleTeam", "value": "5"}, {"id": "214", "isTest": "0", "configEditor": "", "season": "0", "name": "developmentResetItem", "value": "5,10407004;4,10407003"}, {"id": "215", "isTest": "0", "configEditor": "", "season": "0", "name": "fragmentExchanged", "value": "5,10409004;4,10409003"}, {"id": "216", "isTest": "0", "configEditor": "", "season": "0", "name": "newPlayerMustBuyTime", "value": "604800"}, {"id": "217", "isTest": "0", "configEditor": "", "season": "0", "name": "newPlayerMustBuyFreeSupply", "value": "807021"}, {"id": "218", "isTest": "0", "configEditor": "", "season": "0", "name": "rssBiSendLevel", "value": "8"}, {"id": "219", "isTest": "0", "configEditor": "", "season": "0", "name": "rssBiSendDurationMinutes", "value": "30"}, {"id": "220", "isTest": "0", "configEditor": "", "season": "0", "name": "rssBiOpenServer", "value": "-1"}, {"id": "221", "isTest": "0", "configEditor": "", "season": "0", "name": "itemAllocationNum", "value": "5"}, {"id": "222", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEventCommonResetTime", "value": "4|12|20"}, {"id": "223", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEventFogShowCountMax", "value": "8"}, {"id": "224", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEventFogPoolCountMax", "value": "20"}, {"id": "225", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEventSpecialShowCountMax", "value": "8"}, {"id": "226", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEventSpecialPoolCountMax", "value": "20"}, {"id": "234", "isTest": "0", "configEditor": "", "season": "0", "name": "fogDisappearedNewPlayer", "value": "20210923_080000"}, {"id": "235", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEventOfflineFreshTime", "value": "14400"}, {"id": "236", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEventOfflineFreshFrequency", "value": "6"}, {"id": "237", "isTest": "0", "configEditor": "", "season": "0", "name": "activityHellGroupNumber", "value": "2"}, {"id": "238", "isTest": "0", "configEditor": "", "season": "0", "name": "activityHellGroupDay", "value": "28"}, {"id": "240", "isTest": "0", "configEditor": "", "season": "0", "name": "resetHeroItem", "value": "19926001|100"}, {"id": "241", "isTest": "0", "configEditor": "", "season": "0", "name": "SurvivorCampUseNewVersionMilestoneMinVersion", "value": "3"}, {"id": "242", "isTest": "0", "configEditor": "", "season": "0", "name": "freeBuyLastTime", "value": "5"}, {"id": "243", "isTest": "0", "configEditor": "mayiping", "season": "0", "name": "callBackTime", "value": "2592000"}, {"id": "244", "isTest": "", "configEditor": "", "season": "0", "name": "callBackLevel", "value": "8"}, {"id": "245", "isTest": "0", "configEditor": "mayiping", "season": "0", "name": "callBackPeriod", "value": "7776000"}, {"id": "246", "isTest": "0", "configEditor": "", "season": "0", "name": "freeBuyLastTimes", "value": "1|5,2|3,3|1"}, {"id": "247", "isTest": "0", "configEditor": "", "season": "0", "name": "freeBuyNewPlayerTime", "value": "864000"}, {"id": "248", "isTest": "1", "configEditor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "season": "0", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "10000"}, {"id": "249", "isTest": "1", "configEditor": "z<PERSON><PERSON><PERSON>", "season": "0", "name": "ExitClanCD", "value": "13|0"}, {"id": "250", "isTest": "0", "configEditor": "mayiping", "season": "0", "name": "EugeneFrenzyTime", "value": "1|1|0|7200,2|1|8|7200,3|1|16|7200,4|2|0|7200,5|2|8|7200,6|2|16|7200,7|3|0|7200,8|3|8|7200,9|3|16|7200,10|4|0|7200,11|4|8|7200,12|4|16|7200"}, {"id": "251", "isTest": "0", "configEditor": "", "season": "0", "name": "sellTokenItem", "value": "19922249"}, {"id": "255", "isTest": "0", "configEditor": "", "season": "0", "name": "serverTransferRoleMaxNum", "value": "3"}, {"id": "256", "isTest": "0", "configEditor": "", "season": "0", "name": "newPlayerMustBuyFreeLiBao", "value": "807022"}, {"id": "257", "isTest": "0", "configEditor": "", "season": "0", "name": "firstCommonRecruitBlackBox", "value": "1000088"}, {"id": "261", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "initialTroopCapacity", "value": "500"}, {"id": "262", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "baseMarchTime", "value": "3"}, {"id": "263", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "eneryRecoverSpeed", "value": "300"}, {"id": "264", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "energyRefresh", "value": "12|20"}, {"id": "265", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "energyRecovery", "value": "120"}, {"id": "266", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "numArmyMax", "value": "6"}, {"id": "267", "isTest": "0", "configEditor": "lijiaxi", "season": "0", "name": "afkrewardmax", "value": "28800"}, {"id": "268", "isTest": "0", "configEditor": "lijiaxi", "season": "0", "name": "afkrewardredpoint", "value": "14400"}, {"id": "269", "isTest": "", "configEditor": "", "season": "", "name": "afkrewardreceive", "value": "60"}, {"id": "270", "isTest": "", "configEditor": "", "season": "", "name": "warningTimeBlizzard", "value": "600"}, {"id": "271", "isTest": "", "configEditor": "", "season": "", "name": "unlockNormalBlizzardCd", "value": "1800"}, {"id": "272", "isTest": "", "configEditor": "", "season": "", "name": "durationBlizzard", "value": "300"}, {"id": "273", "isTest": "", "configEditor": "", "season": "", "name": "rewardBlizzard", "value": "2001099"}, {"id": "274", "isTest": "", "configEditor": "", "season": "", "name": "unlockBlizzard", "value": "15|51,14|2001001"}, {"id": "275", "isTest": "", "configEditor": "", "season": "", "name": "mailBlizzard", "value": "2000004"}, {"id": "276", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "wallFireFightingCost", "value": "100"}, {"id": "277", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "scoutLevelLimitation", "value": "12"}, {"id": "278", "isTest": "", "configEditor": "", "season": "", "name": "unlocksnowchapter", "value": "5|6|7"}, {"id": "279", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "RallyRewardLimitation1", "value": "50"}, {"id": "280", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "basicRallyMember", "value": "10"}, {"id": "281", "isTest": "0", "configEditor": "", "season": "0", "name": "offlineDuration", "value": "60"}, {"id": "282", "isTest": "0", "configEditor": "", "season": "0", "name": "outputCap", "value": "12"}, {"id": "283", "isTest": "0", "configEditor": "cs", "season": "0", "name": "numArmystart", "value": "1"}, {"id": "284", "isTest": "0", "configEditor": "lijiaxi", "season": "0", "name": "equipforgelevel", "value": "20"}, {"id": "285", "isTest": "0", "configEditor": "lijiaxi", "season": "0", "name": "equipforgequality", "value": "5"}, {"id": "286", "isTest": "0", "configEditor": "", "season": "0", "name": "warningTimeRobber", "value": "300"}, {"id": "287", "isTest": "0", "configEditor": "cs", "season": "0", "name": "rewardRobber", "value": "2001098"}, {"id": "288", "isTest": "0", "configEditor": "", "season": "0", "name": "unlockRobber", "value": "15|341,14|6001001"}, {"id": "289", "isTest": "0", "configEditor": "cs", "season": "0", "name": "mailRobber", "value": "2000004"}, {"id": "290", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "pvpRallyTime", "value": "101|102|103|104"}, {"id": "291", "isTest": "0", "configEditor": "lijiaxi", "season": "0", "name": "SevenDaysReward", "value": "2900001|2900002|2900003|2900004|2900005|2900006|2900007"}, {"id": "292", "isTest": "0", "configEditor": "lijingxi", "season": "0", "name": "powerGrade", "value": "0|30|60|70|90|100"}, {"id": "293", "isTest": "0", "configEditor": "lijingxi", "season": "0", "name": "discountMonthcardItem", "value": "19901101"}, {"id": "294", "isTest": "0", "configEditor": "lijingxi", "season": "0", "name": "noviceSevendaysHero", "value": "3302"}, {"id": "295", "isTest": "0", "configEditor": "liulidong", "season": "0", "name": "fightSoldierDamageRatio", "value": "1|1|1"}, {"id": "296", "isTest": "0", "configEditor": "wzl", "season": "0", "name": "MissionTips", "value": "6"}, {"id": "297", "isTest": "0", "configEditor": "liulidong", "season": "0", "name": "fightAttackSoldierExponentRatio", "value": "0.4|0.4|0.4"}, {"id": "298", "isTest": "0", "configEditor": "liulidong", "season": "0", "name": "fightDefenderSoldierExponentRatio", "value": "0.2|0.2|0.2"}, {"id": "299", "isTest": "0", "configEditor": "cs", "season": "0", "name": "automatedCityMove", "value": "10"}, {"id": "300", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "queueunlockscienceids", "value": "1100701|1102001|1102701"}, {"id": "301", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "hugeSpendAmount", "value": "15000"}, {"id": "302", "isTest": "0", "configEditor": "wzl", "season": "0", "name": "NpcBubble_amount", "value": "1"}, {"id": "303", "isTest": "0", "configEditor": "wzl", "season": "0", "name": "NpcBubble_time", "value": "3"}, {"id": "304", "isTest": "0", "configEditor": "wzl", "season": "0", "name": "NpcBubble_CD", "value": "1,40"}, {"id": "305", "isTest": "0", "configEditor": "wzl", "season": "0", "name": "NpcBubble_odds", "value": "1"}, {"id": "306", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "vipItemList", "value": "10108001|10108002|10108003"}, {"id": "307", "isTest": "0", "configEditor": "lijingxi", "season": "0", "name": "changeNameCoolDown", "value": "86400"}, {"id": "308", "isTest": "0", "configEditor": "wzl", "season": "0", "name": "npcEventCd", "value": "360"}, {"id": "309", "isTest": "0", "configEditor": "wzl", "season": "0", "name": "npcEventRemain", "value": "30"}, {"id": "310", "isTest": "0", "configEditor": "wzl", "season": "0", "name": "NPCEvent_odds", "value": "100"}, {"id": "311", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "mapSearchRangeNpc", "value": "30|30|80"}, {"id": "312", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "mapSearchRangeWorldBoss", "value": "6|40|80"}, {"id": "313", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "mapSearchRangeRes", "value": "6|40|100"}, {"id": "314", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "resDuration", "value": "32400"}, {"id": "315", "isTest": "0", "configEditor": "lijingxi", "season": "0", "name": "chatVipTitleLimit", "value": "3"}, {"id": "316", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "vipCheckInfoLevel", "value": "2"}, {"id": "317", "isTest": "0", "configEditor": "ll", "season": "0", "name": "evaluationShowLimit", "value": "2|330101|6|71"}, {"id": "318", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEventUnlockBoss", "value": "3"}, {"id": "319", "isTest": "0", "configEditor": "", "season": "0", "name": "exploreEventLimitBoss", "value": "5"}, {"id": "320", "isTest": "0", "configEditor": "lijingxi", "season": "0", "name": "settingListBtn", "value": "btn_service_3|1,btn_language|2,btn_account_link|2,btn_account_switch|2,btn_account_delete|2"}, {"id": "321", "isTest": "0", "configEditor": "", "season": "0", "name": "firstpayprice", "value": "com_icewar_7_99"}, {"id": "322", "isTest": "", "configEditor": "", "season": "", "name": "cameratracking", "value": "4"}, {"id": "323", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "pushInterval", "value": "24"}, {"id": "324", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "pushInterval_mail", "value": "24"}, {"id": "325", "isTest": "0", "configEditor": "lijingxi", "season": "0", "name": "autoHuntingMonthCardType", "value": "1001"}, {"id": "326", "isTest": "", "configEditor": "", "season": "0", "name": "seasonOneClickPickup", "value": "5"}, {"id": "327", "isTest": "0", "configEditor": "lijingxi", "season": "0", "name": "autoFarmEnergyThreshold", "value": "10"}, {"id": "328", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "dailyMissionAutoGet", "value": "10"}, {"id": "329", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "0", "name": "battleOfflineRevenueDropTip", "value": "150|200"}, {"id": "330", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "firstRecharge2", "value": "0;2201|1,10401003|3,19908002|10,10108002|5;10403206|10,10401003|2,1|250,10401002|30"}, {"id": "331", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "firstRechargeAB", "value": "A|firstRecharge,B|firstRecharge2,Z|firstRecharge2"}, {"id": "332", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "firstRechargeABConfig", "value": "OVERSEAS,2024-05-28 00:00:00,2024-06-03 23:59:59,5;CHINA,2024-08-28 00:00:00,2024-08-28 23:59:59,100"}, {"id": "333", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "dailypack_maxprice", "value": "com_icewar_5_99"}, {"id": "334", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "rallyRewardLimitation2", "value": "50"}, {"id": "335", "isTest": "", "configEditor": "", "season": "", "name": "rewardBlizzarding", "value": "2001099"}, {"id": "336", "isTest": "", "configEditor": "", "season": "", "name": "unlockBlizzarding", "value": "15|54004,14|3002001"}, {"id": "337", "isTest": "", "configEditor": "", "season": "", "name": "warningTimeBlizzarding", "value": "300"}, {"id": "338", "isTest": "", "configEditor": "xjy", "season": "", "name": "fashionUnlock", "value": "7"}, {"id": "339", "isTest": "0", "configEditor": "<PERSON><PERSON><PERSON>", "season": "", "name": "siyulink", "value": "sgbh1|5000001,sgbh2|5000000"}, {"id": "340", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "serverCreateLimit", "value": "1"}, {"id": "341", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "totalCreateLimit", "value": "50"}, {"id": "342", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "newServerTokenTime", "value": "1"}, {"id": "343", "isTest": "0", "configEditor": "xjy", "season": "", "name": "menghuoLimit", "value": "30"}, {"id": "344", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "wechatHeadRedDot", "value": "7"}, {"id": "345", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "0", "name": "battleSpeedRate", "value": "1.6|2"}, {"id": "346", "isTest": "0", "configEditor": "xjy", "season": "", "name": "joinPveRallyTimeLimit", "value": "30"}, {"id": "347", "isTest": "0", "configEditor": "<PERSON><PERSON><PERSON><PERSON>", "season": "0", "name": "buildingskin", "value": "scaffold_6_20x20|0,0,0;0,0,0;1,1,1|0|2.3,1.6,2.3|fx_sence_level3"}, {"id": "348", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "redeemCodeUnlock", "value": "7"}, {"id": "349", "isTest": "0", "configEditor": "xjy", "season": "0", "name": "attackCityDamage", "value": "500"}, {"id": "185", "isTest": "0", "configEditor": "", "season": "0", "name": "optionalGiftBagRefreshTime", "value": "14400"}, {"id": "186", "isTest": "0", "configEditor": "", "season": "0", "name": "optionalGiftBagResetTime", "value": "28800"}, {"id": "187", "isTest": "0", "configEditor": "", "season": "0", "name": "optionalGiftBagRefreshPrice", "value": "10|20|30|40|50"}, {"id": "188", "isTest": "0", "configEditor": "", "season": "0", "name": "optionalGiftBagResetPrice", "value": "50"}, {"id": "191", "isTest": "0", "configEditor": "", "season": "0", "name": "optionalGiftBagReward2para", "value": "0.2"}, {"id": "350", "isTest": "0", "configEditor": "", "season": "0", "name": "equipJump", "value": "30135|30136|30137|30138|30139|30140|30141|30142|30148|30143|30144"}, {"id": "351", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "peopleCountLimitMax", "value": "32"}, {"id": "352", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "peopleBubbleLimit", "value": "5"}, {"id": "353", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "battlePassReissueMailId", "value": "2000046"}, {"id": "354", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "failedBootLimit", "value": "3"}, {"id": "355", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "failedLevelRange", "value": "1|30"}, {"id": "356", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "failedBootNum", "value": "1"}, {"id": "357", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "0", "name": "battleJumpUnlockLevel", "value": "21"}, {"id": "358", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "0", "name": "battleJumpTime", "value": "2036160000000"}, {"id": "359", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "playerRecallPushTime", "value": "84600"}, {"id": "360", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "newRoleSpeedTime", "value": "86400"}, {"id": "361", "isTest": "0", "configEditor": "guoyunfan", "season": "0", "name": "speedLevelLimit", "value": "10"}, {"id": "362", "isTest": "0", "configEditor": "gc", "season": "", "name": "reservecampnum", "value": "3|0.8"}, {"id": "363", "isTest": "0", "configEditor": "gc", "season": "", "name": "reservecampresourcenum", "value": "6|10000|10,7|10000|10,8|5000|10,9|2500|10"}, {"id": "364", "isTest": "0", "configEditor": "gc", "season": "", "name": "reservecampfreenum", "value": "0.2"}, {"id": "365", "isTest": "0", "configEditor": "gc", "season": "", "name": "reservecampdeadnum", "value": "0.6"}, {"id": "366", "isTest": "0", "configEditor": "gc", "season": "", "name": "reservecamppack", "value": "junxiang101|junxiang102|junxiang103|junxiang104|junxiang105"}, {"id": "367", "isTest": "0", "configEditor": "zwx", "season": "", "name": "militaryHospitalRemindMail", "value": "0.5|0.75|0.9|1"}, {"id": "368", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleShareBornMaxCount", "value": "5"}, {"id": "369", "isTest": "0", "configEditor": "wzl", "season": "", "name": "BuildingSpeedLimitLv", "value": "7"}, {"id": "370", "isTest": "0", "configEditor": "wang<PERSON>uan", "season": "", "name": "bafangresourcecompensation", "value": "0.01"}, {"id": "371", "isTest": "0", "configEditor": "wang<PERSON>uan", "season": "", "name": "bafangcondition", "value": "100"}, {"id": "372", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "weChatSubscribeReward", "value": "4001"}, {"id": "373", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "joinPeopleTimeLimit", "value": "3"}, {"id": "374", "isTest": "0", "configEditor": "gc", "season": "", "name": "unlockRegion", "value": "2"}, {"id": "375", "isTest": "", "configEditor": "lijingxi", "season": "", "name": "acceleratePackJumpUnlock", "value": "6"}, {"id": "376", "isTest": "", "configEditor": "guoyunfan", "season": "", "name": "lightTowerBubbleTime", "value": "5"}, {"id": "377", "isTest": "", "configEditor": "lijingxi", "season": "", "name": "mailPowerGap", "value": "0.003"}, {"id": "378", "isTest": "0", "configEditor": "cxb", "season": "", "name": "lordTreasureUnlockReward", "value": "10000"}, {"id": "379", "isTest": "0", "configEditor": "cxb", "season": "", "name": "lordTreasureJadeUnlockReward", "value": "1000"}, {"id": "380", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "personalRanking<PERSON><PERSON><PERSON>", "value": "1|10901008,2|10901009,3|10901010,4|10901011,5|10901012,6|10901013,7|10901014"}, {"id": "381", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "personalRankingSettlementTime", "value": "1"}, {"id": "382", "isTest": "0", "configEditor": "zwx", "season": "", "name": "moreSpeedUpJumpBuildKey", "value": "80002;80003;80004;80008;80009;80010"}, {"id": "383", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "shareSpeedAB", "value": "A|shareSpeed0,B|shareSpeed1,Z|shareSpeed0"}, {"id": "384", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "shareSpeedABTime", "value": "CHINA,2024-08-14 00:00:00,2024-08-14 23:59:59,106"}, {"id": "385", "isTest": "0", "configEditor": "zwx", "season": "", "name": "moreSpeedUpJumpScienceKey", "value": "80002;80003;80005;80008;80009;80010"}, {"id": "386", "isTest": "0", "configEditor": "zwx", "season": "", "name": "moreSpeedUpJumpTrainKey", "value": "80002;80003;80006;80008;80009;80010"}, {"id": "387", "isTest": "0", "configEditor": "zwx", "season": "", "name": "moreSpeedUpJumpHealKey", "value": "80002;80003;80006;80008;80009"}, {"id": "388", "isTest": "0", "configEditor": "zwx", "season": "", "name": "moreSpeedUpJumpBuildKey_oversea", "value": "80002;80003;80004;80008;80009"}, {"id": "389", "isTest": "0", "configEditor": "zwx", "season": "", "name": "moreSpeedUpJumpScienceKey_oversea", "value": "80002;80003;80005;80008;80009"}, {"id": "390", "isTest": "0", "configEditor": "zwx", "season": "", "name": "moreSpeedUpJumpTrainKey_oversea", "value": "80002;80003;80006;80008;80009"}, {"id": "391", "isTest": "0", "configEditor": "xjy", "season": "", "name": "autoJoinRallyLimitation", "value": "0.2"}, {"id": "392", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleStateTipTime", "value": "6"}, {"id": "393", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "limitMissionTipsTime", "value": "6"}, {"id": "394", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "limitMissionIntervalTime", "value": "600"}, {"id": "395", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "", "name": "hero<PERSON>ondBattle", "value": "7001001|7001002|7001003"}, {"id": "396", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "", "name": "heroBondSLGCampRestraint", "value": "1|3,3|2,2|1"}, {"id": "397", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "", "name": "heroBondSLGDamageUp", "value": "0.2"}, {"id": "398", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "", "name": "heroBondSLGRestrained", "value": "0.2"}, {"id": "399", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "signInHeroShow", "value": "2402|2101"}, {"id": "400", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1000"}, {"id": "401", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "completedArchitecturalShot", "value": "2"}, {"id": "402", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "soldierupgradelock", "value": "13"}, {"id": "403", "isTest": "0", "configEditor": "cxb", "season": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "0|2.5|0.6"}, {"id": "404", "isTest": "0", "configEditor": "cxb", "season": "", "name": "soldierHurtFlyRatio", "value": "0.3|1"}, {"id": "405", "isTest": "0", "configEditor": "cxb", "season": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "50"}, {"id": "406", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "officerPositionApplicationListLimit", "value": "100"}, {"id": "407", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "playerApplicationValidityPeriod", "value": "24"}, {"id": "408", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "playerReapplicationCooldown", "value": "60"}, {"id": "409", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "officerPositionReservationListLimit", "value": "1|3,2|5,3|5,4|100,5|100,6|5,7|100,8|100,15|3,16|3,17|3,18|3,19|3,20|3,21|3,22|3,24|4"}, {"id": "410", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "emperorEmailUsageCooldown", "value": "720"}, {"id": "411", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "oldServerPrestige", "value": "1|2000000,2|2000000,3|2000000,4|2000000,5|2000000,100|200000,101|2000000,102|200000,103|200000,104|200000,105|750000,106|500000"}, {"id": "412", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "prestigePerFarmhouseExtraction", "value": "10000"}, {"id": "413", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "emperorReappointmentTimeLimit", "value": "24"}, {"id": "414", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "prestigePerLumberYardExtraction", "value": "10000"}, {"id": "415", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "prestigePerCoalMineExtraction", "value": "5000"}, {"id": "416", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "prestigePerIronMineExtraction", "value": "2500"}, {"id": "417", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "unlockRequirements", "value": "16"}, {"id": "418", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "duplicateNamePlayer", "value": "2000097"}, {"id": "419", "isTest": "0", "configEditor": "<PERSON><PERSON><PERSON><PERSON>", "season": "", "name": "vipgiftTab", "value": "4"}, {"id": "420", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "blockAllianceRank", "value": "3"}, {"id": "421", "isTest": "0", "configEditor": "wang<PERSON>uan", "season": "", "name": "plunderLimitMid", "value": "0.001|0"}, {"id": "422", "isTest": "0", "configEditor": "wang<PERSON>uan", "season": "", "name": "plunderLimitLow", "value": "0.2|0"}, {"id": "423", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleLimit", "value": "200"}, {"id": "424", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleEfficiency", "value": "0.005"}, {"id": "425", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleSpeed", "value": "0.01"}, {"id": "426", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleNumMax", "value": "32"}, {"id": "427", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleOldUser", "value": "20|100"}, {"id": "428", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleNewUser", "value": "60|10;40|40"}, {"id": "429", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleFavorabilityLimit", "value": "100"}, {"id": "430", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "numPeopleShared", "value": "100"}, {"id": "431", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "timeShare", "value": "172800"}, {"id": "432", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "timeRecoveryFriend", "value": "3600"}, {"id": "433", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "timeOptionA", "value": "300"}, {"id": "434", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "timeOptionB1", "value": "600"}, {"id": "435", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "timeOptionB2", "value": "3600"}, {"id": "436", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "npcFavorabilityLimit", "value": "0"}, {"id": "437", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "shareActivityTime", "value": "259200"}, {"id": "438", "isTest": "0", "configEditor": "wang<PERSON>uan", "season": "", "name": "participantNum", "value": "3"}, {"id": "439", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "season2officerPositionApplicationListLimit", "value": "10"}, {"id": "440", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "season2playerApplicationValidityPeriod", "value": "12"}, {"id": "441", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "season2playerReapplicationCooldown", "value": "60"}, {"id": "442", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "season2emperorEmailUsageCooldown", "value": "720"}, {"id": "443", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "season2emperorReappointmentTimeLimit", "value": "24"}, {"id": "444", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "season2unlockRequirements", "value": "16"}, {"id": "445", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "reservationTimeLimit", "value": "3;6"}, {"id": "446", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "peopleDieDecreasedFavorability", "value": "10"}, {"id": "447", "isTest": "0", "configEditor": "guoyunfan", "season": "", "name": "skipUnlockLevel", "value": "18"}, {"id": "448", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "warDayActivityIds", "value": "117001,117002,117003,2117001"}, {"id": "449", "isTest": "0", "configEditor": "<PERSON><PERSON><PERSON><PERSON>", "season": "", "name": "SkinStateRank", "value": "10090|10091|150102|150106|10093|10092|10037"}, {"id": "450", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "mapOverlayFilter", "value": "30|31"}, {"id": "451", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "", "name": "cavalryPlayTime", "value": "4101206400000"}, {"id": "452", "isTest": "0", "configEditor": "liujie", "season": "", "name": "satrapIcon", "value": "alliance_icon_op_taishou"}, {"id": "453", "isTest": "0", "configEditor": "<PERSON><PERSON><PERSON>", "season": "", "name": "wechatWeekSumRewardItems", "value": "10106003|5;19908002|1;19908001|1;10205002|1"}, {"id": "454", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "wechatSubscribeLimit", "value": "50"}, {"id": "455", "isTest": "0", "configEditor": "liujie", "season": "", "name": "StateWarSpecialEffectsTime", "value": "3"}, {"id": "456", "isTest": "0", "configEditor": "chen<PERSON><PERSON>", "season": "", "name": "slgDeadConvert", "value": "1|1.5|2|4|7|9.5|13|19.5|24|34|34|34|34|34|34"}, {"id": "457", "isTest": "0", "configEditor": "<PERSON><PERSON><PERSON><PERSON>", "season": "", "name": "NormalPveEntryShow", "value": "330101;2|10"}, {"id": "458", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "horseMatingCount", "value": "2"}, {"id": "459", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "horseMatingRuntime", "value": "480"}, {"id": "460", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "horseMatingBigWin", "value": "100|600"}, {"id": "461", "isTest": "0", "configEditor": "tongs", "season": "", "name": "expeditionTimeLimit", "value": "3600"}, {"id": "462", "isTest": "0", "configEditor": "tongs", "season": "", "name": "peopleInteractionLimit", "value": "2"}, {"id": "463", "isTest": "0", "configEditor": "tongs", "season": "", "name": "peopleChatLimit", "value": "3"}, {"id": "464", "isTest": "0", "configEditor": "gyf", "season": "", "name": "Invitation001", "value": "1|2000,19908002|10,19908001|10"}, {"id": "465", "isTest": "0", "configEditor": "gyf", "season": "", "name": "Invitation002", "value": "0|200"}, {"id": "466", "isTest": "0", "configEditor": "gyf", "season": "", "name": "Invitation003", "value": "86400"}, {"id": "467", "isTest": "0", "configEditor": "gyf", "season": "", "name": "Invitation004", "value": "2"}, {"id": "468", "isTest": "0", "configEditor": "gyf", "season": "", "name": "Invitation005", "value": "7"}, {"id": "469", "isTest": "0", "configEditor": "gyf", "season": "", "name": "Invitation006", "value": "4"}, {"id": "470", "isTest": "0", "configEditor": "wx", "season": "", "name": "payEventFreshCount", "value": "12"}, {"id": "471", "isTest": "0", "configEditor": "wx", "season": "", "name": "payEventFreshjunxiang", "value": "1"}, {"id": "472", "isTest": "0", "configEditor": "wx", "season": "", "name": "watchtowerRefreshBox", "value": "CG_FENGHUOTAI_30"}, {"id": "473", "isTest": "0", "configEditor": "wx", "season": "", "name": "serverGroup", "value": "12"}, {"id": "474", "isTest": "0", "configEditor": "wx", "season": "", "name": "nearserverRankOpen", "value": "21"}, {"id": "475", "isTest": "0", "configEditor": "gyf", "season": "", "name": "season3IdleFx", "value": "fx_ui_wind_loop"}, {"id": "476", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "horseNameLength", "value": "2|4"}, {"id": "477", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "horseNameCost", "value": "100"}, {"id": "478", "isTest": "478", "configEditor": "lijingxi", "season": "", "name": "horseNameCoolDown", "value": "86400"}, {"id": "479", "isTest": "479", "configEditor": "wangjingzhi", "season": "", "name": "requestreinforcementsinterval", "value": "60"}, {"id": "480", "isTest": "480", "configEditor": "lijingxi", "season": "", "name": "horseNameStarLimit", "value": "15"}, {"id": "481", "isTest": "481", "configEditor": "wangjingzhi", "season": "", "name": "limitMonthCardDay", "value": "90"}, {"id": "482", "isTest": "482", "configEditor": "<PERSON><PERSON><PERSON>", "season": "", "name": "chatRiskLimitLevel", "value": "15"}, {"id": "483", "isTest": "0", "configEditor": "liujiaxiang", "season": "", "name": "seasonKingFaq", "value": "KingPrivilegesInfo_content,S2KingPrivilegesInfo_content,KingPrivilegesInfo_S3"}, {"id": "484", "isTest": "0", "configEditor": "gyf", "season": "", "name": "invitationCodeRewardLevel", "value": "4"}, {"id": "485", "isTest": "", "configEditor": "", "season": "", "name": "fireworksUnlock", "value": "CHINA,2025-01-26 00:00:00,2025-02-05 23:59:59"}, {"id": "486", "isTest": "0", "configEditor": "lj", "season": "", "name": "receiveRedPocketTime", "value": "20"}, {"id": "487", "isTest": "0", "configEditor": "gyf", "season": "", "name": "anchorRewardsLimitedTime", "value": "CHINA,2025-01-18 00:00:00,2025-04-19 23:59:59"}, {"id": "488", "isTest": "0", "configEditor": "gyf", "season": "", "name": "Invitation007", "value": "4"}, {"id": "489", "isTest": "0", "configEditor": "lijingxi", "season": "", "name": "seasonCreateRole", "value": "100|101|102|103|104|105|106|107|108|109|110|111"}, {"id": "490", "isTest": "0", "configEditor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "season": "", "name": "defusalRewardParams", "value": "10460001|1;5;2201|1"}]