[{"id": "2", "FieldType": "FightFieldControlType_Forward", "PassCondition": "FieldStagePassType_PassY", "MapSize": "6,100", "MapOffSetX": "42", "Monster": "-3~3,28~32,12010001,5,1|-3~3,32~42,12010001,8,3|-3~3,42~50,12010001,8,3|-3~3,47~50,12010002,8,3|-3~3,51~60,12010002,10,5|-3~3,61~70,12010003,15,5|-3~3,71~80,12010004,20,7|-3~3,81~90,12010005,20,10|-3~3,91~100,12010006,20,13|-3~3,80~90,12010006,20,16|-3~3,90~100,12010006,20,20|-3~3,100~110,12010006,20,20|-3~3,100~110,12010006,20,20", "Building": ""}, {"id": "3", "FieldType": "FightFieldControlType_Backward", "PassCondition": "FieldStagePassType_PassY", "MapSize": "6,100", "MapOffSetX": "42", "Monster": "-2~2,32~35,12010211,1,1|-2~2,32~35,12010212,2,1|-2~2,32~35,12010213,1,1|-2~2,46~70,12010211,10,1|-2~2,42~70,12010212,11,1|-2~2,42~70,12010213,12,1|-3~3,75~80,12010211,8,1|-3~3,75~80,12010212,9,1|-3~3,75~80,12010213,9,1|0~0,81~81,12010214,1,1", "Building": ""}, {"id": "4", "FieldType": "FightFieldControlType_Auto", "PassCondition": "FieldStagePassType_PassY", "MapSize": "10,200", "MapOffSetX": "0", "Monster": "", "Building": ""}, {"id": "5", "FieldType": "FightFieldControlType_Forward", "PassCondition": "FieldStagePassType_PassY", "MapSize": "6,100", "MapOffSetX": "0", "Monster": "-3~3,28~32,12010001,5,1|-3~3,32~42,12010001,8,3|-3~3,42~50,12010001,8,3|-3~3,47~50,12010002,8,3|-3~3,51~60,12010002,10,5|-3~3,61~70,12010003,15,5|-3~3,71~80,12010004,20,7|-3~3,81~90,12010005,20,10|-3~3,91~100,12010006,20,13|-3~3,80~90,12010007,20,16|-3~3,90~100,12010007,20,20", "Building": ""}, {"id": "6", "FieldType": "FightFieldControlType_Stop", "PassCondition": "FeildStagePassType_KillAll", "MapSize": "6,100", "MapOffSetX": "0", "Monster": "-2~2,32~35,12010201,1,1|-2~2,32~35,12010202,2,1|-2~2,32~35,12010203,1,1|-2~2,46~70,12010201,10,1|-2~2,42~70,12010202,11,1|-2~2,42~70,12010203,12,1|-3~3,75~80,12010204,8,1|-3~3,75~80,12010205,9,1|-3~3,75~80,12010206,9,1|-2~2,85~95,12010201,10,1|-2~2,85~95,12010202,11,1|-2~2,85~95,12010203,12,1|-2~2,85~95,12010204,10,1|-2~2,85~95,12010205,11,1|-2~2,85~95,12010206,12,1|0~0,100~100,12010209,1,1", "Building": "0,25,13020211|3,25,13020212|0,42,13020213|-3,60,13020214|3,60,13020215|-3,73,13020216|0,73,13020217|3,73,13020218"}, {"id": "7", "FieldType": "FightFieldControlType_Forward", "PassCondition": "FieldStagePassType_PassY", "MapSize": "6,100", "MapOffSetX": "42", "Monster": "-3~3,28~32,12010201,10,1|0~4,42~45,12010202,20,3|2~6,45~50,12010203,18,3|-3~3,45~50,12010201,18,3|0~0,69~70,12010207,1,5|0~0,73~74,12010208,1,5|0~0,76~77,12010209,1,7|0~0,76~77,12010209,1,7|-3~3,80~95,12010201,15,10|-3~3,80~95,12010202,15,10|-3~3,80~95,12010203,15,10|-3~3,80~95,12010204,15,10|-3~3,80~95,12010205,15,10|-3~3,80~95,12010206,15,10", "Building": ""}, {"id": "8", "FieldType": "FightFieldControlType_Free", "PassCondition": "FeildStagePassType_KillAll", "MapSize": "22,21", "MapOffSetX": "34", "Monster": "5-15,5-15,12010201,12,20|5-15,5-15,12010202,12,20|5-15,5-15,12010203,12,20|5-15,5-15,12010204,12,20|5-15,5-15,12010205,12,20|5-15,5-15,12010206,12,20|15-15,15-15,12010210,1,1", "Building": ""}]