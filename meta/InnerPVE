[{"id": "1100", "levelId": "1", "enemyList": "mon_qun_2", "enemyAction": "ani_show03", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "1", "winEventParams": "1", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600001", "abTest": "0", "unlockGridList": "0", "unlockBigGrid": "", "unlockNpc": "0", "levelName": "", "levelDsc": "", "levelIcon": ""}, {"id": "1200", "levelId": "2", "enemyList": "mon_qun_3", "enemyAction": "ani_show04", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "1", "winEventParams": "2", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600002", "abTest": "0", "unlockGridList": "4", "unlockBigGrid": "", "unlockNpc": "0", "levelName": "", "levelDsc": "", "levelIcon": ""}, {"id": "1300", "levelId": "3", "enemyList": "mon_qun_1;mon_qun_2;mon_qun_2", "enemyAction": "ani_show02;ani_show01;ani_show02", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "1", "winEventParams": "3", "showModelLimt": "2", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "4", "unlockBigGrid": "", "unlockNpc": "0", "levelName": "", "levelDsc": "", "levelIcon": ""}, {"id": "1400", "levelId": "4", "enemyList": "troop_qi_1;troop_qiang_1;troop_qiang_1", "enemyAction": "idle;idle;idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "7", "unlockBigGrid": "Big_Unlock_Plane_1", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes1", "levelIcon": "ui/images/shop/icon_baoxiang1"}, {"id": "1500", "levelId": "5", "enemyList": "npc_man_2;mon_qun_3;mon_qun_3;troop_gong_1;troop_gong_1;troop_qi_1;troop_qi_1;troop_qiang_1;troop_qiang_1;mon_qun_1", "enemyAction": "idle_01", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "3", "winEventParams": "75;ui/images/goods/icon_item_SpeedBuild", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "7", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes2", "levelIcon": "ui/images/shop/icon_baoxiang1"}, {"id": "1600", "levelId": "6", "enemyList": "mon_qun_2;mon_qun_2", "enemyAction": "ani_show03;ani_show01", "unlockTypeList": "9", "unlockParams": "5", "lockTipsLangKey": "innerpve", "winEventType": "1", "winEventParams": "4", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600006", "abTest": "0", "unlockGridList": "7", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes1", "levelIcon": "ui/images/shop/icon_baoxiang1"}, {"id": "1700", "levelId": "7", "enemyList": "mon_qun_2;mon_qun_3;mon_qun_3", "enemyAction": "ani_show04;ani_show04;ani_show04", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "10", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes2", "levelIcon": "ui/images/shop/icon_baoxiang1"}, {"id": "1800", "levelId": "8", "enemyList": "mon_qun_1;mon_qun_2;mon_qun_2", "enemyAction": "ani_show01;ani_attack;ani_show01", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "1", "winEventParams": "5", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600008", "abTest": "0", "unlockGridList": "10", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes1", "levelIcon": "ui/images/shop/icon_baoxiang1"}, {"id": "1900", "levelId": "9", "enemyList": "troop_qi_1;troop_qiang_1;troop_qiang_1", "enemyAction": "ani_attack;idle;ani_attack", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "1", "winEventParams": "6", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "10", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes2", "levelIcon": "ui/images/shop/icon_baoxiang1"}, {"id": "2000", "levelId": "10", "enemyList": "hero_qun_4;troop_qi_1;troop_qiang_1;troop_qiang_1;mon_qun_1;mon_qun_3;mon_qun_3;troop_qi_1", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "3", "winEventParams": "75;ui/images/goods/icon_item_SpeedBuild", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600010", "abTest": "0", "unlockGridList": "0", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes1", "levelIcon": "ui/images/shop/icon_baoxiang1"}]